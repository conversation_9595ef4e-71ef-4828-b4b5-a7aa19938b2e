#!/usr/bin/env python3
"""
💾 BACKUP COMPLETO DA BASE DE DADOS
====================================

Cria backup completo do sistema Mastigador:
- Dump do PostgreSQL
- Exportação do Redis (RDB)
- Lista das filas RabbitMQ
- Compactação dos arquivos baixados
- Metadados do backup

Útil antes de operações destrutivas.
"""

import psycopg2
from psycopg2.extras import RealDictCursor
import redis
import pika
import os
import subprocess
import shutil
import json
import tarfile
from pathlib import Path
from dotenv import load_dotenv
from datetime import datetime
import sys

# Carregar variáveis de ambiente
load_dotenv()

def connect_db():
    """Conecta ao PostgreSQL"""
    host = 'localhost' if os.getenv('POSTGRES_HOST') == 'db' else os.getenv('POSTGRES_HOST', 'localhost')
    return psycopg2.connect(
        host=host,
        port=os.getenv('POSTGRES_PORT', '5432'),
        database=os.getenv('POSTGRES_DB', 'mastigador_db'),
        user=os.getenv('POSTGRES_USER', 'user'),
        password=os.getenv('POSTGRES_PASSWORD', 'password'),
        cursor_factory=RealDictCursor
    )

def connect_redis():
    """Conecta ao Redis"""
    host = 'localhost' if os.getenv('REDIS_HOST') == 'redis' else os.getenv('REDIS_HOST', 'localhost')
    try:
        r = redis.Redis(
            host=host,
            port=int(os.getenv('REDIS_PORT', '6379')),
            db=0,
            decode_responses=True
        )
        r.ping()
        return r
    except:
        return None

def connect_rabbitmq():
    """Conecta ao RabbitMQ"""
    host = 'localhost' if os.getenv('RABBITMQ_HOST') == 'rabbitmq' else os.getenv('RABBITMQ_HOST', 'localhost')
    try:
        connection = pika.BlockingConnection(
            pika.ConnectionParameters(
                host=host,
                port=int(os.getenv('RABBITMQ_PORT', '5672')),
                virtual_host='/',
                credentials=pika.PlainCredentials(
                    os.getenv('RABBITMQ_USER', 'guest'),
                    os.getenv('RABBITMQ_PASS', 'guest')
                )
            )
        )
        return connection
    except:
        return None

def criar_pasta_backup():
    """Cria pasta para o backup"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_dir = Path(f"backup_mastigador_{timestamp}")
    backup_dir.mkdir(exist_ok=True)
    return backup_dir

def backup_postgresql(backup_dir, conn):
    """Faz backup do PostgreSQL"""
    print("🗃️ Fazendo backup do PostgreSQL...")
    
    # Dump do schema e dados
    dump_file = backup_dir / "postgresql_dump.sql"
    
    try:
        # Usar pg_dump via subprocess
        host = 'localhost' if os.getenv('POSTGRES_HOST') == 'db' else os.getenv('POSTGRES_HOST', 'localhost')
        
        cmd = [
            'pg_dump',
            '-h', host,
            '-p', os.getenv('POSTGRES_PORT', '5432'),
            '-U', os.getenv('POSTGRES_USER', 'user'),
            '-d', os.getenv('POSTGRES_DB', 'mastigador_db'),
            '-f', str(dump_file),
            '--no-password'
        ]
        
        # Configurar senha via variável de ambiente
        env = os.environ.copy()
        env['PGPASSWORD'] = os.getenv('POSTGRES_PASSWORD', 'password')
        
        result = subprocess.run(cmd, env=env, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"   ✅ Dump salvo em {dump_file}")
            
            # Estatísticas do dump
            size = dump_file.stat().st_size
            if size > 1024*1024:
                size_str = f"{size / (1024*1024):.1f} MB"
            elif size > 1024:
                size_str = f"{size / 1024:.1f} KB"
            else:
                size_str = f"{size} bytes"
            
            print(f"   📊 Tamanho do dump: {size_str}")
            return True
        else:
            print(f"   ❌ Erro no pg_dump: {result.stderr}")
            
            # Backup manual das tabelas principais
            print("   🔄 Tentando backup manual...")
            return backup_postgresql_manual(backup_dir, conn)
            
    except Exception as e:
        print(f"   ❌ Erro no backup PostgreSQL: {e}")
        return backup_postgresql_manual(backup_dir, conn)

def backup_postgresql_manual(backup_dir, conn):
    """Backup manual do PostgreSQL via queries"""
    print("   🔧 Executando backup manual...")
    
    try:
        cur = conn.cursor()
        
        # Tabelas para backup
        tabelas = [
            'documents', 'versions', 'chunks_parent',
            'translations', 'embeddings', 'tasks', 'file_processing_status'
        ]
        
        backup_data = {}
        
        for tabela in tabelas:
            try:
                cur.execute(f"SELECT * FROM {tabela}")
                registros = cur.fetchall()
                backup_data[tabela] = [dict(r) for r in registros]
                print(f"      ✅ {tabela}: {len(registros)} registros")
            except Exception as e:
                print(f"      ❌ {tabela}: {e}")
                backup_data[tabela] = []
        
        # Salvar como JSON
        backup_file = backup_dir / "postgresql_backup.json"
        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump(backup_data, f, indent=2, default=str, ensure_ascii=False)
        
        print(f"   ✅ Backup manual salvo em {backup_file}")
        cur.close()
        return True
        
    except Exception as e:
        print(f"   ❌ Erro no backup manual: {e}")
        return False

def backup_redis(backup_dir, r):
    """Faz backup do Redis"""
    print("🔴 Fazendo backup do Redis...")
    
    if not r:
        print("   ⚠️ Redis não disponível - pulando backup")
        return False
    
    try:
        # Obter todas as chaves
        keys = r.keys('*')
        redis_data = {}
        
        for key in keys:
            try:
                key_type = r.type(key)
                if key_type == 'string':
                    redis_data[key] = {'type': 'string', 'value': r.get(key)}
                elif key_type == 'hash':
                    redis_data[key] = {'type': 'hash', 'value': r.hgetall(key)}
                elif key_type == 'list':
                    redis_data[key] = {'type': 'list', 'value': r.lrange(key, 0, -1)}
                elif key_type == 'set':
                    redis_data[key] = {'type': 'set', 'value': list(r.smembers(key))}
                elif key_type == 'zset':
                    redis_data[key] = {'type': 'zset', 'value': r.zrange(key, 0, -1, withscores=True)}
            except Exception as e:
                print(f"      ⚠️ Erro ao fazer backup da chave {key}: {e}")
        
        # Salvar backup
        backup_file = backup_dir / "redis_backup.json"
        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump(redis_data, f, indent=2, default=str, ensure_ascii=False)
        
        print(f"   ✅ Backup do Redis salvo: {len(keys)} chaves")
        return True
        
    except Exception as e:
        print(f"   ❌ Erro no backup Redis: {e}")
        return False

def backup_rabbitmq(backup_dir, connection):
    """Faz backup das filas RabbitMQ"""
    print("🐰 Fazendo backup do RabbitMQ...")
    
    if not connection:
        print("   ⚠️ RabbitMQ não disponível - pulando backup")
        return False
    
    try:
        channel = connection.channel()
        
        filas = [
            'file.processing',
            'extraction.results',
            'translation.requests',
            'translation.results', 
            'indexing.requests'
        ]
        
        rabbitmq_data = {
            'filas': {},
            'timestamp': datetime.now().isoformat()
        }
        
        for fila in filas:
            try:
                method = channel.queue_declare(queue=fila, passive=True)
                msg_count = method.method.message_count
                rabbitmq_data['filas'][fila] = {
                    'message_count': msg_count,
                    'messages': []
                }
                
                # Nota: Não consumimos as mensagens para não afetar o sistema
                print(f"   📊 {fila}: {msg_count} mensagens")
                
            except Exception as e:
                print(f"   ⚠️ Erro na fila {fila}: {e}")
        
        # Salvar informações das filas
        backup_file = backup_dir / "rabbitmq_status.json"
        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump(rabbitmq_data, f, indent=2, ensure_ascii=False)
        
        channel.close()
        connection.close()
        
        print("   ✅ Status das filas RabbitMQ salvo")
        return True
        
    except Exception as e:
        print(f"   ❌ Erro no backup RabbitMQ: {e}")
        return False

def backup_arquivos(backup_dir):
    """Faz backup dos arquivos baixados"""
    print("📁 Fazendo backup dos arquivos...")
    
    pasta_data = Path("data/raw")
    
    if not pasta_data.exists():
        print("   ℹ️ Pasta data/raw/ não existe - pulando backup")
        return True
    
    try:
        # Criar tar.gz dos arquivos
        arquivo_tar = backup_dir / "arquivos_data_raw.tar.gz"
        
        with tarfile.open(arquivo_tar, "w:gz") as tar:
            tar.add(pasta_data, arcname="data_raw")
        
        # Estatísticas
        size = arquivo_tar.stat().st_size
        if size > 1024**3:  # GB
            size_str = f"{size / (1024**3):.1f} GB"
        elif size > 1024**2:  # MB
            size_str = f"{size / (1024**2):.1f} MB"
        elif size > 1024:  # KB
            size_str = f"{size / 1024:.1f} KB"
        else:
            size_str = f"{size} bytes"
        
        print(f"   ✅ Arquivos compactados: {size_str}")
        return True
        
    except Exception as e:
        print(f"   ❌ Erro no backup de arquivos: {e}")
        return False

def criar_metadados_backup(backup_dir, stats):
    """Cria arquivo de metadados do backup"""
    print("📋 Criando metadados do backup...")
    
    try:
        # Conectar para estatísticas
        conn = connect_db()
        cur = conn.cursor()
        
        # Contar registros
        tabelas = ['documents', 'versions', 'chunks_parent', 'translations', 'embeddings', 'tasks']
        contadores = {}
        
        for tabela in tabelas:
            try:
                cur.execute(f"SELECT COUNT(*) as total FROM {tabela}")
                contadores[tabela] = cur.fetchone()['total']
            except:
                contadores[tabela] = 0
        
        cur.close()
        conn.close()
        
        # Metadados
        metadados = {
            'backup_info': {
                'timestamp': datetime.now().isoformat(),
                'version': '1.0',
                'created_by': 'backup_base_dados.py'
            },
            'estatisticas': {
                'registros_por_tabela': contadores,
                'total_registros': sum(contadores.values())
            },
            'componentes_backup': stats,
            'sistema_info': {
                'postgres_host': os.getenv('POSTGRES_HOST', 'localhost'),
                'redis_host': os.getenv('REDIS_HOST', 'localhost'),
                'rabbitmq_host': os.getenv('RABBITMQ_HOST', 'localhost')
            }
        }
        
        # Salvar metadados
        metadata_file = backup_dir / "backup_metadata.json"
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(metadados, f, indent=2, ensure_ascii=False)
        
        print(f"   ✅ Metadados salvos em {metadata_file}")
        return True
        
    except Exception as e:
        print(f"   ❌ Erro ao criar metadados: {e}")
        return False

def compactar_backup(backup_dir):
    """Compacta todo o backup em um arquivo final"""
    print("📦 Compactando backup final...")
    
    try:
        backup_final = f"{backup_dir.name}.tar.gz"
        
        with tarfile.open(backup_final, "w:gz") as tar:
            tar.add(backup_dir, arcname=backup_dir.name)
        
        # Tamanho final
        size = Path(backup_final).stat().st_size
        if size > 1024**3:  # GB
            size_str = f"{size / (1024**3):.1f} GB"
        elif size > 1024**2:  # MB
            size_str = f"{size / (1024**2):.1f} MB"
        elif size > 1024:  # KB
            size_str = f"{size / 1024:.1f} KB"
        else:
            size_str = f"{size} bytes"
        
        print(f"   ✅ Backup final: {backup_final} ({size_str})")
        
        # Remover pasta temporária
        shutil.rmtree(backup_dir)
        print(f"   🗑️ Pasta temporária removida: {backup_dir}")
        
        return backup_final
        
    except Exception as e:
        print(f"   ❌ Erro ao compactar backup: {e}")
        return None

def main():
    """Função principal"""
    print(f"💾 BACKUP COMPLETO - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # Criar pasta de backup
    backup_dir = criar_pasta_backup()
    print(f"📂 Pasta de backup: {backup_dir}")
    
    # Conectar aos serviços
    try:
        conn = connect_db()
        print("✅ Conectado ao PostgreSQL")
    except Exception as e:
        print(f"❌ Erro ao conectar PostgreSQL: {e}")
        sys.exit(1)
    
    r = connect_redis()
    rabbitmq_conn = connect_rabbitmq()
    
    # Executar backups
    stats = {
        'postgresql': backup_postgresql(backup_dir, conn),
        'redis': backup_redis(backup_dir, r),
        'rabbitmq': backup_rabbitmq(backup_dir, rabbitmq_conn),
        'arquivos': backup_arquivos(backup_dir)
    }
    
    # Criar metadados
    criar_metadados_backup(backup_dir, stats)
    
    # Compactar backup final
    backup_final = compactar_backup(backup_dir)
    
    conn.close()
    
    # Relatório final
    print("\n" + "=" * 60)
    print("📊 RELATÓRIO DO BACKUP")
    print("=" * 60)
    
    sucessos = sum(1 for success in stats.values() if success)
    total = len(stats)
    
    for componente, sucesso in stats.items():
        emoji = "✅" if sucesso else "❌"
        print(f"{emoji} {componente.ljust(15)}: {'SUCESSO' if sucesso else 'FALHOU'}")
    
    print("-" * 60)
    
    if backup_final:
        print(f"🎉 BACKUP CONCLUÍDO: {backup_final}")
        print(f"📈 Componentes salvos: {sucessos}/{total}")
        
        if sucessos == total:
            print("✨ Backup completo realizado com sucesso!")
        else:
            print("⚠️ Backup parcial - alguns componentes falharam")
            
        print(f"\n💡 Para restaurar: extraia {backup_final} e siga as instruções")
        
    else:
        print("❌ BACKUP FALHOU: Não foi possível criar arquivo final")
        print("🔍 Verifique os logs acima para detalhes")

if __name__ == "__main__":
    main()
