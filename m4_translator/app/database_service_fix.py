"""
Script para corrigir o serviço de banco de dados do M4 Translator
"""

def fix_get_translation_stats():
    """
    Correção para a função get_translation_stats() que estava falhando
    ao tentar acessar a tabela translations com nomes de colunas incorretos.
    
    A tabela existente tem os seguintes campos:
    - translation_id (UUID)
    - version_id (UUID)
    - chunk_id (UUID)
    - language_code (texto)
    - translated_text (texto)
    - model_used (texto)
    - confidence (numérico)
    - created_at (timestamp)
    """
    import logging
    logger = logging.getLogger(__name__)
    
    # Função de correção para ser usada no serviço de banco de dados
    def corrected_get_translation_stats(self) -> dict:
        """
        Retorna estatísticas das traduções - versão corrigida
        
        Returns:
            Dicionário com estatísticas
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # Verificar se a tabela existe
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = 'public' 
                        AND table_name = 'translations'
                    )
                """)
                table_exists = cursor.fetchone()['exists']
                
                if not table_exists:
                    logger.error("Tabela 'translations' não existe no banco")
                    return {
                        'total_translations': 0,
                        'translations_by_language': [],
                        'translations_by_model': [],
                        'recent_translations_1h': 0
                    }
                
                # Contagem total de traduções
                cursor.execute("SELECT COUNT(*) as total FROM translations")
                total_translations = cursor.fetchone()['total']
                
                # Traduções por idioma (usando language_code em vez de language)
                cursor.execute("""
                    SELECT language_code, COUNT(*) as count 
                    FROM translations 
                    GROUP BY language_code 
                    ORDER BY count DESC
                """)
                by_language = cursor.fetchall()
                
                # Traduções por modelo
                cursor.execute("""
                    SELECT model_used, COUNT(*) as count 
                    FROM translations 
                    WHERE model_used IS NOT NULL
                    GROUP BY model_used 
                    ORDER BY count DESC
                """)
                by_model = cursor.fetchall()
                
                # Traduções recentes (última hora)
                cursor.execute("""
                    SELECT COUNT(*) as recent_count 
                    FROM translations 
                    WHERE created_at > NOW() - INTERVAL '1 hour'
                """)
                recent_translations = cursor.fetchone()['recent_count']
                
                return {
                    'total_translations': total_translations,
                    'translations_by_language': [dict(row) for row in by_language],
                    'translations_by_model': [dict(row) for row in by_model],
                    'recent_translations_1h': recent_translations
                }
                
        except Exception as e:
            logger.error(f"Erro ao obter estatísticas: {e}")
            return {
                'total_translations': 0,
                'translations_by_language': [],
                'translations_by_model': [],
                'recent_translations_1h': 0
            }
    
    return corrected_get_translation_stats

if __name__ == "__main__":
    print("Este arquivo deve ser importado, não executado diretamente")
