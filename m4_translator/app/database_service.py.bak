"""
Serviço de conexão com banco de dados para M4 Translator
"""

import psycopg2
import psycopg2.pool
from psycopg2.extras import RealDictCursor
import os
from contextlib import contextmanager
import logging

logger = logging.getLogger(__name__)

class TranslationDatabaseService:
    """Serviço de acesso ao banco de dados para o M4 Translator"""
    
    def __init__(self):
        self.connection_pool = None
        self._initialize_pool()
    
    def _initialize_pool(self) -> None:
        """Inicializa o pool de conexões"""
        try:
            # Configuração do banco a partir das variáveis de ambiente
            postgres_host = os.environ.get('POSTGRES_HOST', 'db')
            postgres_port = os.environ.get('POSTGRES_PORT', '5432')
            postgres_db = os.environ.get('POSTGRES_DB', 'mastigador_db')
            postgres_user = os.environ.get('POSTGRES_USER', 'user')
            postgres_password = os.environ.get('POSTGRES_PASSWORD', 'password')
            
            # Criação da DSN
            dsn = f"postgresql://{postgres_user}:{postgres_password}@{postgres_host}:{postgres_port}/{postgres_db}"
            
            # Inicialização do pool de conexões
            self.connection_pool = psycopg2.pool.ThreadedConnectionPool(
                minconn=1,
                maxconn=10,
                dsn=dsn,
                cursor_factory=RealDictCursor
            )
            logger.info("Pool de conexões inicializado com sucesso")
        except Exception as e:
            logger.error("Erro ao inicializar pool de conexões", error=str(e))
            raise
    
    @contextmanager
    def get_connection(self):
        """Context manager para obter conexão do pool"""
        connection = None
        try:
            connection = self.connection_pool.getconn()
            yield connection
        except Exception as e:
            if connection:
                connection.rollback()
            logger.error("Erro na conexão com banco", error=str(e))
            raise
        finally:
            if connection:
                self.connection_pool.putconn(connection)
    
    def get_translation_stats(self) -> dict:
        """
        Retorna estatísticas das traduções
        
        Returns:
            Dicionário com estatísticas
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # Verificar se a tabela existe
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = 'public' 
                        AND table_name = 'translations'
                    )
                """)
                table_exists = cursor.fetchone()['exists']
                
                if not table_exists:
                    logger.error("Tabela 'translations' não existe no banco")
                    return {
                        'total_translations': 0,
                        'translations_by_language': [],
                        'translations_by_model': [],
                        'recent_translations_1h': 0
                    }
                
                # Contagem total de traduções
                cursor.execute("SELECT COUNT(*) as total FROM translations")
                total_translations = cursor.fetchone()['total']
                
                # Traduções por idioma (usando language_code em vez de language)
                cursor.execute("""
                    SELECT language_code, COUNT(*) as count 
                    FROM translations 
                    GROUP BY language_code 
                    ORDER BY count DESC
                """)
                by_language = cursor.fetchall()
                
                # Traduções por modelo
                cursor.execute("""
                    SELECT model_used, COUNT(*) as count 
                    FROM translations 
                    WHERE model_used IS NOT NULL
                    GROUP BY model_used 
                    ORDER BY count DESC
                """)
                by_model = cursor.fetchall()
                
                # Traduções recentes (última hora)
                cursor.execute("""
                    SELECT COUNT(*) as recent_count 
                    FROM translations 
                    WHERE created_at > NOW() - INTERVAL '1 hour'
                """)
                recent_translations = cursor.fetchone()['recent_count']
                
                return {
                    'total_translations': total_translations,
                    'translations_by_language': [dict(row) for row in by_language],
                    'translations_by_model': [dict(row) for row in by_model],
                    'recent_translations_1h': recent_translations
                }
                
        except Exception as e:
            logger.error(f"Erro ao obter estatísticas: {e}")
            return {
                'total_translations': 0,
                'translations_by_language': [],
                'translations_by_model': [],
                'recent_translations_1h': 0
            }
    
    def store_translation(self, version_id, chunk_id, language_code, translated_text, model_used=None, confidence=None):
        """
        Armazena uma tradução no banco de dados
        
        Args:
            version_id: UUID da versão do documento
            chunk_id: UUID do chunk
            language_code: Código do idioma (ex: 'pt-br', 'en')
            translated_text: Texto traduzido
            model_used: Modelo de IA utilizado (opcional)
            confidence: Nível de confiança da tradução (opcional)
        
        Returns:
            ID da tradução inserida ou None em caso de erro
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                query = """
                INSERT INTO translations (
                    version_id, chunk_id, language_code, translated_text, 
                    model_used, confidence, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, NOW()
                ) RETURNING translation_id
                """
                
                cursor.execute(query, (
                    version_id, chunk_id, language_code, translated_text, 
                    model_used, confidence
                ))
                
                result = cursor.fetchone()
                return result['translation_id'] if result else None
                
        except Exception as e:
            logger.error(f"Erro ao armazenar tradução: {e}")
            return None
    
    def get_translation(self, chunk_id, language_code):
        """
        Recupera uma tradução do banco de dados
        
        Args:
            chunk_id: UUID do chunk
            language_code: Código do idioma (ex: 'pt-br', 'en')
        
        Returns:
            Dicionário com a tradução ou None se não encontrada
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                query = """
                SELECT translation_id, version_id, chunk_id, language_code, 
                       translated_text, model_used, confidence, created_at
                FROM translations
                WHERE chunk_id = %s AND language_code = %s
                ORDER BY created_at DESC
                LIMIT 1
                """
                
                cursor.execute(query, (chunk_id, language_code))
                result = cursor.fetchone()
                
                return dict(result) if result else None
                
        except Exception as e:
            logger.error(f"Erro ao recuperar tradução: {e}")
            return None
    
    def close(self):
        """Fecha o pool de conexões"""
        if self.connection_pool:
            self.connection_pool.closeall()
            logger.info("Pool de conexões fechado")


def fix_get_translation_stats():
    """
    Correção para a função get_translation_stats() que estava falhando
    ao tentar acessar a tabela translations com nomes de colunas incorretos.
    
    A tabela existente tem os seguintes campos:
    - translation_id (UUID)
    - version_id (UUID)
    - chunk_id (UUID)
    - language_code (texto)
    - translated_text (texto)
    - model_used (texto)
    - confidence (numérico)
    - created_at (timestamp)
    """
    import logging
    logger = logging.getLogger(__name__)
    
    # Função de correção para ser usada no serviço de banco de dados
    def corrected_get_translation_stats(self) -> dict:
        """
        Retorna estatísticas das traduções - versão corrigida
        
        Returns:
            Dicionário com estatísticas
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # Verificar se a tabela existe
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = 'public' 
                        AND table_name = 'translations'
                    )
                """)
                table_exists = cursor.fetchone()['exists']
                
                if not table_exists:
                    logger.error("Tabela 'translations' não existe no banco")
                    return {
                        'total_translations': 0,
                        'translations_by_language': [],
                        'translations_by_model': [],
                        'recent_translations_1h': 0
                    }
                
                # Contagem total de traduções
                cursor.execute("SELECT COUNT(*) as total FROM translations")
                total_translations = cursor.fetchone()['total']
                
                # Traduções por idioma (usando language_code em vez de language)
                cursor.execute("""
                    SELECT language_code, COUNT(*) as count 
                    FROM translations 
                    GROUP BY language_code 
                    ORDER BY count DESC
                """)
                by_language = cursor.fetchall()
                
                # Traduções por modelo
                cursor.execute("""
                    SELECT model_used, COUNT(*) as count 
                    FROM translations 
                    WHERE model_used IS NOT NULL
                    GROUP BY model_used 
                    ORDER BY count DESC
                """)
                by_model = cursor.fetchall()
                
                # Traduções recentes (última hora)
                cursor.execute("""
                    SELECT COUNT(*) as recent_count 
                    FROM translations 
                    WHERE created_at > NOW() - INTERVAL '1 hour'
                """)
                recent_translations = cursor.fetchone()['recent_count']
                
                return {
                    'total_translations': total_translations,
                    'translations_by_language': [dict(row) for row in by_language],
                    'translations_by_model': [dict(row) for row in by_model],
                    'recent_translations_1h': recent_translations
                }
                
        except Exception as e:
            logger.error(f"Erro ao obter estatísticas: {e}")
            return {
                'total_translations': 0,
                'translations_by_language': [],
                'translations_by_model': [],
                'recent_translations_1h': 0
            }
    
    return corrected_get_translation_stats

if __name__ == "__main__":
    print("Este arquivo deve ser importado, não executado diretamente")
