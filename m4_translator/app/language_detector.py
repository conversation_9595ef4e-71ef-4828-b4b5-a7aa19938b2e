import logging
from typing import Dict, Optional, Tuple
from langdetect import detect
from langdetect.lang_detect_exception import LangDetectException
from .config import config

logger = logging.getLogger(__name__)

class LanguageDetector:
    """
    Serviço de detecção de idioma usando langdetect
    Implementa o padrão Adapter para permitir fácil troca de provedores
    """
    
    def __init__(self):
        self.threshold = config.LANGUAGE_DETECTION_THRESHOLD
        
    def detect_language(self, text: str) -> Tuple[Optional[str], float]:
        """
        Detecta o idioma do texto fornecido
        
        Args:
            text: Texto para detecção de idioma
            
        Returns:
            Tuple com (código_idioma, confiança) ou (None, 0.0) se falhar
        """
        if not text or not text.strip():
            logger.warning("Texto vazio fornecido para detecção de idioma")
            return None, 0.0
            
        # Limpar texto e verificar se tem conteúdo suficiente
        clean_text = text.strip()
        if len(clean_text) < 10:
            logger.warning(f"Texto muito curto para detecção confiável: '{clean_text[:50]}...'")
            return None, 0.0
            
        try:
            # Detectar idioma
            detected_lang = detect(clean_text)
            
            # Para langdetect, assumimos confiança alta se detectou
            # Em implementações futuras, podemos usar detectlangs() para obter probabilidades
            confidence = 0.9
            
            logger.info(f"Idioma detectado: {detected_lang} (confiança: {confidence:.2f})")
            
            if confidence >= self.threshold:
                return detected_lang, confidence
            else:
                logger.warning(f"Confiança baixa na detecção: {confidence:.2f} < {self.threshold}")
                return None, confidence
                
        except LangDetectException as e:
            logger.error(f"Erro na detecção de idioma: {e}")
            return None, 0.0
        except Exception as e:
            logger.error(f"Erro inesperado na detecção de idioma: {e}")
            return None, 0.0
    
    def is_translation_needed(self, detected_language: Optional[str]) -> bool:
        """
        Verifica se a tradução é necessária baseada no idioma detectado
        
        Args:
            detected_language: Código do idioma detectado
            
        Returns:
            True se tradução for necessária, False caso contrário
        """
        if not detected_language:
            # Se não conseguiu detectar o idioma, assume que precisa traduzir
            logger.info("Idioma não detectado, assumindo necessidade de tradução")
            return True
            
        target_lang_codes = ['pt', 'pt-br', 'pt_br']
        
        # Normalizar o código do idioma
        normalized_detected = detected_language.lower().replace('-', '_')
        
        if normalized_detected in target_lang_codes:
            logger.info(f"Texto já está em português ({detected_language}), tradução não necessária")
            return False
            
        logger.info(f"Tradução necessária de {detected_language} para {config.TARGET_LANGUAGE}")
        return True
    
    def get_language_info(self, text: str) -> Dict[str, any]:
        """
        Retorna informações completas sobre o idioma do texto
        
        Args:
            text: Texto para análise
            
        Returns:
            Dicionário com informações sobre o idioma
        """
        detected_lang, confidence = self.detect_language(text)
        needs_translation = self.is_translation_needed(detected_lang)
        
        return {
            'detected_language': detected_lang,
            'confidence': confidence,
            'needs_translation': needs_translation,
            'target_language': config.TARGET_LANGUAGE,
            'text_length': len(text),
            'is_reliable': confidence >= self.threshold
        }
