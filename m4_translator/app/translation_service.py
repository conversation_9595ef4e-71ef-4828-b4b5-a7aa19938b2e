import logging
import time
from typing import List, Dict, Optional, Tuple
from openai import OpenAI
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
from .config import config

logger = logging.getLogger(__name__)

class TranslationService:
    """
    Serviço de tradução usando OpenAI GPT-4o
    Implementa batching logic para otimizar chamadas da API
    """
    
    def __init__(self):
        if not config.OPENAI_API_KEY:
            raise ValueError("OPENAI_API_KEY não configurada")
            
        self.client = OpenAI(api_key=config.OPENAI_API_KEY)
        self.model = config.OPENAI_MODEL
        self.max_tokens = config.OPENAI_MAX_TOKENS
        self.temperature = config.OPENAI_TEMPERATURE
        self.batch_size = config.BATCH_SIZE
        self.max_batch_tokens = config.MAX_BATCH_TOKENS
        
    def _estimate_tokens(self, text: str) -> int:
        """
        Estima o número de tokens em um texto
        Usa uma aproximação de ~4 caracteres por token
        
        Args:
            text: Texto para estimar tokens
            
        Returns:
            Número estimado de tokens
        """
        return len(text) // 4
    
    def _create_translation_prompt(self, chunks: List[Dict[str, any]]) -> str:
        """
        Cria um prompt otimizado para tradução em lote
        
        Args:
            chunks: Lista de chunks para traduzir
            
        Returns:
            Prompt formatado para a API
        """
        prompt = f"""Você é um tradutor profissional especializado em traduzir textos técnicos e documentos para português brasileiro (pt-BR).

Instruções importantes:
1. Traduza APENAS o conteúdo entre as tags <CONTENT> e </CONTENT>
2. Mantenha a formatação original (quebras de linha, espaçamentos, etc.)
3. Preserve termos técnicos quando apropriado
4. Use português brasileiro formal e claro
5. Mantenha a numeração e estrutura dos chunks
6. Se encontrar texto já em português, mantenha-o como está

Traduza os seguintes chunks de texto:

"""
        
        for chunk in chunks:
            chunk_id = chunk.get('chunk_id', 'unknown')
            content = chunk.get('content', '')
            source_lang = chunk.get('source_language', 'unknown')
            
            prompt += f"""
CHUNK_{chunk_id} (idioma detectado: {source_lang}):
<CONTENT>
{content}
</CONTENT>

"""
        
        prompt += """
Responda APENAS com as traduções no seguinte formato:

CHUNK_{chunk_id}:
[tradução aqui]

CHUNK_{chunk_id}:
[tradução aqui]

"""
        
        return prompt
    
    def _parse_translation_response(self, response_text: str, original_chunks: List[Dict[str, any]]) -> Dict[str, str]:
        """
        Extrai as traduções da resposta da API
        
        Args:
            response_text: Resposta da API OpenAI
            original_chunks: Chunks originais enviados
            
        Returns:
            Dicionário mapeando chunk_id para tradução
        """
        translations = {}
        
        try:
            lines = response_text.strip().split('\n')
            current_chunk_id = None
            current_translation = []
            
            for line in lines:
                line = line.strip()
                
                # Verificar se é uma linha de identificação de chunk
                if line.startswith('CHUNK_') and line.endswith(':'):
                    # Salvar tradução anterior se existe
                    if current_chunk_id and current_translation:
                        translations[current_chunk_id] = '\n'.join(current_translation).strip()
                    
                    # Extrair ID do chunk
                    current_chunk_id = line.replace('CHUNK_', '').replace(':', '').strip()
                    current_translation = []
                    
                elif current_chunk_id and line:
                    # Adicionar linha à tradução atual
                    current_translation.append(line)
            
            # Salvar última tradução
            if current_chunk_id and current_translation:
                translations[current_chunk_id] = '\n'.join(current_translation).strip()
                
            # Verificar se todas as traduções foram encontradas
            expected_chunks = {chunk['chunk_id'] for chunk in original_chunks}
            found_chunks = set(translations.keys())
            
            if found_chunks != expected_chunks:
                missing = expected_chunks - found_chunks
                extra = found_chunks - expected_chunks
                logger.warning(f"Traduções não correspondentes. Faltando: {missing}, Extra: {extra}")
                
        except Exception as e:
            logger.error(f"Erro ao extrair traduções da resposta: {e}")
            
        return translations
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=1, max=10),
        retry=retry_if_exception_type((Exception,))
    )
    def _call_openai_api(self, prompt: str) -> str:
        """
        Chama a API OpenAI com retry automático
        
        Args:
            prompt: Prompt para tradução
            
        Returns:
            Resposta da API
        """
        try:
            logger.info(f"Enviando prompt para OpenAI (modelo: {self.model})")
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": "Você é um tradutor profissional especializado em português brasileiro."
                    },
                    {
                        "role": "user", 
                        "content": prompt
                    }
                ],
                max_tokens=self.max_tokens,
                temperature=self.temperature
            )
            
            result = response.choices[0].message.content
            
            # Log de uso de tokens para monitoramento de custos
            usage = response.usage
            logger.info(f"Tokens usados - Prompt: {usage.prompt_tokens}, "
                       f"Completion: {usage.completion_tokens}, Total: {usage.total_tokens}")
            
            return result
            
        except Exception as e:
            logger.error(f"Erro na chamada para OpenAI API: {e}")
            raise
    
    def create_batches(self, chunks: List[Dict[str, any]]) -> List[List[Dict[str, any]]]:
        """
        Organiza chunks em lotes otimizados para a API
        
        Args:
            chunks: Lista de chunks para processar
            
        Returns:
            Lista de lotes de chunks
        """
        batches = []
        current_batch = []
        current_batch_tokens = 0
        
        for chunk in chunks:
            content = chunk.get('content', '')
            chunk_tokens = self._estimate_tokens(content)
            
            # Verificar se adicionar este chunk excederia os limites
            would_exceed_size = len(current_batch) >= self.batch_size
            would_exceed_tokens = (current_batch_tokens + chunk_tokens) > self.max_batch_tokens
            
            if current_batch and (would_exceed_size or would_exceed_tokens):
                # Finalizar lote atual
                batches.append(current_batch)
                current_batch = [chunk]
                current_batch_tokens = chunk_tokens
            else:
                # Adicionar ao lote atual
                current_batch.append(chunk)
                current_batch_tokens += chunk_tokens
        
        # Adicionar último lote se não estiver vazio
        if current_batch:
            batches.append(current_batch)
        
        logger.info(f"Criados {len(batches)} lotes de tradução")
        return batches
    
    def translate_batch(self, chunks: List[Dict[str, any]]) -> Dict[str, str]:
        """
        Traduz um lote de chunks
        
        Args:
            chunks: Lista de chunks para traduzir
            
        Returns:
            Dicionário mapeando chunk_id para tradução
        """
        if not chunks:
            return {}
        
        try:
            # Criar prompt para o lote
            prompt = self._create_translation_prompt(chunks)
            
            # Estimar tokens do prompt
            prompt_tokens = self._estimate_tokens(prompt)
            logger.info(f"Processando lote com {len(chunks)} chunks (~{prompt_tokens} tokens)")
            
            # Chamar API OpenAI
            start_time = time.time()
            response_text = self._call_openai_api(prompt)
            end_time = time.time()
            
            logger.info(f"Tradução completada em {end_time - start_time:.2f} segundos")
            
            # Extrair traduções da resposta
            translations = self._parse_translation_response(response_text, chunks)
            
            return translations
            
        except Exception as e:
            logger.error(f"Erro ao traduzir lote: {e}")
            return {}
    
    def translate_chunks(self, chunks: List[Dict[str, any]]) -> List[Dict[str, any]]:
        """
        Traduz uma lista de chunks usando batching otimizado
        
        Args:
            chunks: Lista de chunks para traduzir
            
        Returns:
            Lista de chunks com traduções
        """
        if not chunks:
            return []
        
        logger.info(f"Iniciando tradução de {len(chunks)} chunks")
        
        # Criar lotes otimizados
        batches = self.create_batches(chunks)
        
        translated_chunks = []
        
        for i, batch in enumerate(batches):
            logger.info(f"Processando lote {i+1}/{len(batches)} com {len(batch)} chunks")
            
            try:
                # Traduzir lote
                translations = self.translate_batch(batch)
                
                # Combinar traduções com chunks originais
                for chunk in batch:
                    chunk_id = chunk['chunk_id']
                    translation = translations.get(chunk_id, '')
                    
                    if translation:
                        # Criar chunk traduzido
                        translated_chunk = chunk.copy()
                        translated_chunk.update({
                            'translated_content': translation,
                            'translation_status': 'completed',
                            'translation_timestamp': time.time()
                        })
                        translated_chunks.append(translated_chunk)
                        logger.debug(f"Chunk {chunk_id} traduzido com sucesso")
                    else:
                        logger.error(f"Tradução não encontrada para chunk {chunk_id}")
                        # Adicionar chunk com erro
                        error_chunk = chunk.copy()
                        error_chunk.update({
                            'translated_content': '',
                            'translation_status': 'failed',
                            'translation_error': 'Translation not found in response',
                            'translation_timestamp': time.time()
                        })
                        translated_chunks.append(error_chunk)
                
                # Pequena pausa entre lotes para respeitar rate limits
                if i < len(batches) - 1:
                    time.sleep(0.5)
                    
            except Exception as e:
                logger.error(f"Erro ao processar lote {i+1}: {e}")
                
                # Adicionar chunks com erro
                for chunk in batch:
                    error_chunk = chunk.copy()
                    error_chunk.update({
                        'translated_content': '',
                        'translation_status': 'failed',
                        'translation_error': str(e),
                        'translation_timestamp': time.time()
                    })
                    translated_chunks.append(error_chunk)
        
        logger.info(f"Tradução concluída. {len(translated_chunks)} chunks processados")
        return translated_chunks
    
    def translate_single(self, text: str, source_language: Optional[str] = None) -> Tuple[str, bool]:
        """
        Traduz um único texto (usado para API REST)
        
        Args:
            text: Texto para traduzir
            source_language: Idioma de origem (opcional)
            
        Returns:
            Tuple com (tradução, sucesso)
        """
        try:
            # Criar chunk temporário
            temp_chunk = {
                'chunk_id': 'single',
                'content': text,
                'source_language': source_language or 'auto'
            }
            
            # Traduzir usando o método de lote
            result = self.translate_chunks([temp_chunk])
            
            if result and result[0].get('translation_status') == 'completed':
                return result[0]['translated_content'], True
            else:
                return '', False
                
        except Exception as e:
            logger.error(f"Erro na tradução única: {e}")
            return '', False
