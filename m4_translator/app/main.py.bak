import logging
import time
from typing import Dict, List, Optional, Any
from fastapi import <PERSON>AP<PERSON>, HTTPException, BackgroundTasks, Depends
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import uvicorn
from .config import config
from .translation_service import TranslationService
from .cache_service import TranslationCacheService
from .database_service import TranslationDatabaseService
from .translation_orchestrator import TranslationOrchestrator

# Configurar logging
logging.basicConfig(
    level=getattr(logging, config.LOG_LEVEL.upper()),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Modelos Pydantic para API
class TranslationRequest(BaseModel):
    text: str = Field(..., description="Texto para traduzir", min_length=1, max_length=10000)
    source_language: Optional[str] = Field(None, description="Idioma de origem (detectado automaticamente se não fornecido)")
    target_language: str = Field("pt-BR", description="Idioma de destino")

class TranslationResponse(BaseModel):
    original_text: str
    translated_text: str
    source_language: str
    target_language: str
    model_used: str
    confidence: float
    processing_time: float
    cached: bool

class HealthCheckResponse(BaseModel):
    status: str
    components: Dict[str, Any]
    timestamp: float

class StatsResponse(BaseModel):
    translation_stats: Dict[str, Any]
    cache_stats: Dict[str, Any]
    database_stats: Dict[str, Any]

# Criar aplicação FastAPI
app = FastAPI(
    title="M4 Translator API",
    description="API para tradução de textos usando OpenAI GPT-4o com cache Redis",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Configurar CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Em produção, especificar origens específicas
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Serviços globais
translation_service: Optional[TranslationService] = None
cache_service: Optional[TranslationCacheService] = None
database_service: Optional[TranslationDatabaseService] = None
orchestrator: Optional[TranslationOrchestrator] = None

@app.on_event("startup")
async def startup_event():
    """Inicializa serviços na inicialização da aplicação"""
    global translation_service, cache_service, database_service, orchestrator
    
    try:
        logger.info("Iniciando M4 Translator API...")
        
        # Inicializar serviços
        translation_service = TranslationService()
        cache_service = TranslationCacheService()
        database_service = TranslationDatabaseService()
        orchestrator = TranslationOrchestrator()
        
        logger.info("Serviços inicializados com sucesso")
        
    except Exception as e:
        logger.error(f"Erro na inicialização: {e}")
        raise

@app.on_event("shutdown")
async def shutdown_event():
    """Limpa recursos na finalização da aplicação"""
    global orchestrator, database_service
    
    try:
        if orchestrator:
            orchestrator.close()
        if database_service:
            database_service.close()
        logger.info("Recursos limpos com sucesso")
    except Exception as e:
        logger.error(f"Erro na finalização: {e}")

def get_translation_service() -> TranslationService:
    """Dependency injection para o serviço de tradução"""
    if translation_service is None:
        raise HTTPException(status_code=503, detail="Serviço de tradução não disponível")
    return translation_service

def get_cache_service() -> TranslationCacheService:
    """Dependency injection para o serviço de cache"""
    if cache_service is None:
        raise HTTPException(status_code=503, detail="Serviço de cache não disponível")
    return cache_service

def get_database_service() -> TranslationDatabaseService:
    """Dependency injection para o serviço de banco"""
    if database_service is None:
        raise HTTPException(status_code=503, detail="Serviço de banco não disponível")
    return database_service

def get_orchestrator() -> TranslationOrchestrator:
    """Dependency injection para o orquestrador"""
    if orchestrator is None:
        raise HTTPException(status_code=503, detail="Orquestrador não disponível")
    return orchestrator

# Endpoints da API

@app.get("/", response_class=HTMLResponse)
async def root():
    """Página inicial com UI de teste"""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>M4 Translator - Interface de Teste</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
            .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            h1 { color: #333; text-align: center; margin-bottom: 30px; }
            .form-group { margin-bottom: 20px; }
            label { display: block; margin-bottom: 5px; font-weight: bold; color: #555; }
            textarea, select, input { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; }
            textarea { height: 120px; resize: vertical; }
            button { background-color: #007bff; color: white; padding: 12px 30px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; margin-right: 10px; }
            button:hover { background-color: #0056b3; }
            button:disabled { background-color: #6c757d; cursor: not-allowed; }
            .result { margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-left: 4px solid #007bff; border-radius: 4px; }
            .error { border-left-color: #dc3545; background-color: #f8d7da; }
            .loading { text-align: center; margin: 20px 0; }
            .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 20px; }
            .stat-card { background: #e9ecef; padding: 15px; border-radius: 4px; text-align: center; }
            .stat-value { font-size: 24px; font-weight: bold; color: #007bff; }
            .stat-label { font-size: 12px; color: #6c757d; margin-top: 5px; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🌐 M4 Translator</h1>
            <p style="text-align: center; color: #666; margin-bottom: 30px;">
                Interface de teste para tradução de textos usando OpenAI GPT-4o
            </p>
            
            <form id="translationForm">
                <div class="form-group">
                    <label for="text">Texto para traduzir:</label>
                    <textarea id="text" placeholder="Digite ou cole o texto que deseja traduzir..." required></textarea>
                </div>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                    <div class="form-group">
                        <label for="sourceLanguage">Idioma de origem (opcional):</label>
                        <select id="sourceLanguage">
                            <option value="">Detectar automaticamente</option>
                            <option value="en">Inglês</option>
                            <option value="es">Espanhol</option>
                            <option value="fr">Francês</option>
                            <option value="de">Alemão</option>
                            <option value="it">Italiano</option>
                            <option value="zh">Chinês</option>
                            <option value="ja">Japonês</option>
                            <option value="ko">Coreano</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="targetLanguage">Idioma de destino:</label>
                        <select id="targetLanguage">
                            <option value="pt-BR">Português (Brasil)</option>
                            <option value="en">Inglês</option>
                            <option value="es">Espanhol</option>
                            <option value="fr">Francês</option>
                        </select>
                    </div>
                </div>
                
                <button type="submit" id="translateBtn">🔄 Traduzir</button>
                <button type="button" onclick="clearForm()">🗑️ Limpar</button>
                <button type="button" onclick="loadStats()">📊 Estatísticas</button>
            </form>
            
            <div id="loading" class="loading" style="display: none;">
                <p>⏳ Traduzindo... Aguarde...</p>
            </div>
            
            <div id="result" style="display: none;"></div>
            
            <div id="stats" style="display: none;"></div>
        </div>
        
        <script>
            document.getElementById('translationForm').addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const text = document.getElementById('text').value;
                const sourceLanguage = document.getElementById('sourceLanguage').value;
                const targetLanguage = document.getElementById('targetLanguage').value;
                const submitBtn = document.getElementById('translateBtn');
                const loading = document.getElementById('loading');
                const result = document.getElementById('result');
                
                // Mostrar loading
                loading.style.display = 'block';
                result.style.display = 'none';
                submitBtn.disabled = true;
                
                try {
                    const response = await fetch('/translate', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            text: text,
                            source_language: sourceLanguage || null,
                            target_language: targetLanguage
                        })
                    });
                    
                    const data = await response.json();
                    
                    if (response.ok) {
                        result.innerHTML = `
                            <div class="result">
                                <h3>✅ Tradução concluída</h3>
                                <p><strong>Texto original:</strong> ${data.original_text}</p>
                                <p><strong>Texto traduzido:</strong> ${data.translated_text}</p>
                                <p><strong>Idioma detectado:</strong> ${data.source_language}</p>
                                <p><strong>Modelo usado:</strong> ${data.model_used}</p>
                                <p><strong>Confiança:</strong> ${(data.confidence * 100).toFixed(1)}%</p>
                                <p><strong>Tempo de processamento:</strong> ${data.processing_time.toFixed(2)}s</p>
                                <p><strong>Cache:</strong> ${data.cached ? '✅ Hit' : '❌ Miss'}</p>
                            </div>
                        `;
                    } else {
                        result.innerHTML = `
                            <div class="result error">
                                <h3>❌ Erro na tradução</h3>
                                <p>${data.detail || 'Erro desconhecido'}</p>
                            </div>
                        `;
                    }
                    
                    result.style.display = 'block';
                    
                } catch (error) {
                    result.innerHTML = `
                        <div class="result error">
                            <h3>❌ Erro de conexão</h3>
                            <p>Não foi possível conectar ao servidor: ${error.message}</p>
                        </div>
                    `;
                    result.style.display = 'block';
                } finally {
                    loading.style.display = 'none';
                    submitBtn.disabled = false;
                }
            });
            
            function clearForm() {
                document.getElementById('text').value = '';
                document.getElementById('sourceLanguage').value = '';
                document.getElementById('targetLanguage').value = 'pt-BR';
                document.getElementById('result').style.display = 'none';
                document.getElementById('stats').style.display = 'none';
            }
            
            async function loadStats() {
                try {
                    const response = await fetch('/stats');
                    const data = await response.json();
                    
                    document.getElementById('stats').innerHTML = `
                        <h3>📊 Estatísticas do Sistema</h3>
                        <div class="stats">
                            <div class="stat-card">
                                <div class="stat-value">${data.translation_stats.total_requests || 0}</div>
                                <div class="stat-label">Total de Solicitações</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">${data.translation_stats.cache_hit_rate || 0}%</div>
                                <div class="stat-label">Taxa de Cache Hit</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">${data.translation_stats.translations_completed || 0}</div>
                                <div class="stat-label">Traduções Concluídas</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">${data.cache_stats.total_translation_keys || 0}</div>
                                <div class="stat-label">Chaves no Cache</div>
                            </div>
                        </div>
                    `;
                    document.getElementById('stats').style.display = 'block';
                } catch (error) {
                    console.error('Erro ao carregar estatísticas:', error);
                }
            }
        </script>
    </body>
    </html>
    """
    return html_content

@app.post("/translate", response_model=TranslationResponse)
async def translate_text(
    request: TranslationRequest,
    service: TranslationService = Depends(get_translation_service)
):
    """
    Traduz um texto fornecido
    """
    start_time = time.time()
    
    try:
        logger.info(f"Recebida solicitação de tradução: {request.source_language} -> {request.target_language}")
        
        # Traduzir usando o serviço
        translated_text, success = service.translate_single(
            text=request.text,
            source_language=request.source_language
        )
        
        if not success:
            raise HTTPException(status_code=500, detail="Falha na tradução")
        
        processing_time = time.time() - start_time
        
        # Por enquanto, assumimos que não veio do cache para traduções individuais
        # Em uma implementação mais robusta, integraríamos com o cache service
        
        return TranslationResponse(
            original_text=request.text,
            translated_text=translated_text,
            source_language=request.source_language or "auto",
            target_language=request.target_language,
            model_used=config.OPENAI_MODEL,
            confidence=0.9,
            processing_time=processing_time,
            cached=False
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro na tradução: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health", response_model=HealthCheckResponse)
@app.get("/healthz", response_model=HealthCheckResponse)
async def health_check(orch: TranslationOrchestrator = Depends(get_orchestrator)):
    """
    Verifica a saúde de todos os componentes do sistema
    Disponível em /health e /healthz para compatibilidade
    """
    try:
        health_status = orch.health_check()
        
        # Determinar status geral
        all_healthy = all(health_status.values())
        overall_status = "healthy" if all_healthy else "degraded"
        
        return HealthCheckResponse(
            status=overall_status,
            components=health_status,
            timestamp=time.time()
        )
        
    except Exception as e:
        logger.error(f"Erro no health check: {e}")
        return HealthCheckResponse(
            status="error",
            components={"error": str(e)},
            timestamp=time.time()
        )

@app.get("/stats", response_model=StatsResponse)
async def get_stats(
    orch: TranslationOrchestrator = Depends(get_orchestrator),
    cache: TranslationCacheService = Depends(get_cache_service),
    db: TranslationDatabaseService = Depends(get_database_service)
):
    """
    Retorna estatísticas detalhadas do sistema
    """
    try:
        # Recuperar estatísticas com tratamento de erros individual
        try:
            translation_stats = orch.get_stats()
        except Exception as e:
            logger.error(f"Erro ao obter estatísticas de tradução: {e}")
            translation_stats = {"error": str(e), "status": "unavailable"}
            
        try:
            cache_stats = cache.get_cache_stats()
        except Exception as e:
            logger.error(f"Erro ao obter estatísticas de cache: {e}")
            cache_stats = {"error": str(e), "status": "unavailable"}
            
        try:
            database_stats = db.get_translation_stats()
        except Exception as e:
            logger.error(f"Erro ao obter estatísticas de banco de dados: {e}")
            database_stats = {"error": str(e), "status": "unavailable"}
        
        # Retornar mesmo se algumas estatísticas falharem
        return StatsResponse(
            translation_stats=translation_stats,
            cache_stats=cache_stats,
            database_stats=database_stats
        )
        
    except Exception as e:
        logger.error(f"Erro geral ao obter estatísticas: {e}")
        # Retornar uma resposta degradada em vez de erro 500
        return StatsResponse(
            translation_stats={"status": "error"},
            cache_stats={"status": "error"},
            database_stats={"status": "error"}
        )


@app.get("/cache/clear")
async def clear_cache(cache: TranslationCacheService = Depends(get_cache_service)):
    """
    Limpa todas as traduções do cache (usar com cuidado)
    """
    try:
        success = cache.clear_all_translations()
        
        if success:
            return {"message": "Cache limpo com sucesso"}
        else:
            raise HTTPException(status_code=500, detail="Falha ao limpar cache")
            
    except Exception as e:
        logger.error(f"Erro ao limpar cache: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/translations/{version_id}")
async def get_translations_by_version(
    version_id: str,
    language_code: Optional[str] = None,
    db: TranslationDatabaseService = Depends(get_database_service)
):
    """
    Busca traduções de uma versão específica
    """
    try:
        translations = db.get_translations_by_version(version_id, language_code)
        return {"translations": translations}
        
    except Exception as e:
        logger.error(f"Erro ao buscar traduções: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8084,
        reload=True,
        log_level=config.LOG_LEVEL.lower()
    )
