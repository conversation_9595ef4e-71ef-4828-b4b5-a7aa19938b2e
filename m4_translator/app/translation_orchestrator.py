import json
import logging
import time
import pika
from typing import Dict, List, Any, Optional
from pika.exceptions import AMQPConnectionError, AMQPChannelError
from .config import config
from .translation_service import TranslationService
from .cache_service import TranslationCacheService
from .database_service import TranslationDatabaseService

logger = logging.getLogger(__name__)

class TranslationOrchestrator:
    """
    Orquestrador principal que coordena todo o fluxo de tradução:
    - Verificação de cache
    - Tradução via OpenAI
    - Armazenamento em cache e banco
    - Publicação de resultados
    """
    
    def __init__(self):
        self.translation_service = TranslationService()
        self.cache_service = TranslationCacheService()
        self.database_service = TranslationDatabaseService()
        
        # RabbitMQ para publicação de resultados
        self.connection = None
        self.channel = None
        
        # Métricas de performance
        self.stats = {
            'cache_hits': 0,
            'cache_misses': 0,
            'translations_completed': 0,
            'translations_failed': 0,
            'total_processing_time': 0.0
        }
    
    def _connect_rabbitmq(self) -> bool:
        """
        Conecta ao RabbitMQ para publicação de resultados
        
        Returns:
            True se conectou com sucesso, False caso contrário
        """
        try:
            credentials = pika.PlainCredentials(
                config.RABBITMQ_USER, 
                config.RABBITMQ_PASS
            )
            
            parameters = pika.ConnectionParameters(
                host=config.RABBITMQ_HOST,
                port=config.RABBITMQ_PORT,
                credentials=credentials,
                heartbeat=600,
                blocked_connection_timeout=300
            )
            
            self.connection = pika.BlockingConnection(parameters)
            self.channel = self.connection.channel()
            
            # Declarar a fila de resultados se não existir
            self.channel.queue_declare(queue=config.TRANSLATION_RESULTS_TOPIC, durable=True)
            
            logger.info(f"Conectado ao RabbitMQ para publicação: {config.RABBITMQ_HOST}:{config.RABBITMQ_PORT}")
            return True
            
        except AMQPConnectionError as e:
            logger.error(f"Erro de conexão com RabbitMQ: {e}")
            return False
        except Exception as e:
            logger.error(f"Erro inesperado ao conectar ao RabbitMQ: {e}")
            return False
    
    def _disconnect_rabbitmq(self):
        """Fecha a conexão com RabbitMQ"""
        try:
            if self.connection and not self.connection.is_closed:
                self.connection.close()
                logger.debug("Conexão RabbitMQ fechada")
        except Exception as e:
            logger.error(f"Erro ao fechar conexão RabbitMQ: {e}")
    
    def process_translation_request(self, request: Dict[str, Any]) -> bool:
        """
        Processa uma solicitação de tradução completa
        
        Args:
            request: Dados da solicitação de tradução
            
        Returns:
            True se processou com sucesso, False caso contrário
        """
        start_time = time.time()
        
        job_id_log = request.get('job_id', 'unknown_job_id')
        chunk_id_log = request.get('chunk_id', 'unknown_chunk_id')
        logger.info(f"Orchestrator (Job: {job_id_log}, Chunk: {chunk_id_log}): Iniciando process_translation_request.")
        
        # Log detalhado para depurar version_id
        received_version_id = request.get('version_id')
        logger.info(f"Orchestrator (Job: {job_id_log}, Chunk: {chunk_id_log}): Received version_id: '{received_version_id}', Type: {type(received_version_id)}")
        # logger.info(f"Orchestrator (Job: {job_id_log}, Chunk: {chunk_id_log}): Full request data: {request}") # Pode ser muito verboso

        try:
            content = request['content']
            source_language = request['source_language']
            target_language = request['target_language']
            language_info = request.get('metadata', {}).get('language_info', {})
            needs_translation = language_info.get('needs_translation', True)

            logger.info(f"Orchestrator (Job: {job_id_log}, Chunk: {chunk_id_log}): Detalhes - Source: {source_language}, Target: {target_language}, Needs Translation: {needs_translation}")

            if not needs_translation:
                logger.info(f"Orchestrator (Job: {job_id_log}, Chunk: {chunk_id_log}): Não precisa de tradução. Usando conteúdo original.")
                translated_text = content
                model_used = "skipped_translation_same_language"
                confidence = language_info.get('confidence', 1.0)
            else:
                logger.info(f"Orchestrator (Job: {job_id_log}, Chunk: {chunk_id_log}): Verificando cache...")
                cached_translation = self._check_cache(content, source_language, target_language)
                
                if cached_translation:
                    logger.info(f"Orchestrator (Job: {job_id_log}, Chunk: {chunk_id_log}): Cache HIT.")
                    self.stats['cache_hits'] += 1
                    translated_text = cached_translation['translated_text']
                    model_used = cached_translation.get('metadata', {}).get('model_used', 'cached')
                    confidence = cached_translation.get('metadata', {}).get('confidence', 1.0)
                else:
                    logger.info(f"Orchestrator (Job: {job_id_log}, Chunk: {chunk_id_log}): Cache MISS. Traduzindo via OpenAI...")
                    self.stats['cache_misses'] += 1
                    translation_result = self._translate_content(request)
                    
                    if not translation_result:
                        logger.error(f"Orchestrator (Job: {job_id_log}, Chunk: {chunk_id_log}): Falha na tradução via OpenAI.")
                        self.stats['translations_failed'] += 1
                        return False
                    
                    translated_text = translation_result['translated_content']
                    model_used = translation_result.get('model_used', config.OPENAI_MODEL)
                    confidence = translation_result.get('confidence', 0.9)
                    
                    logger.info(f"Orchestrator (Job: {job_id_log}, Chunk: {chunk_id_log}): Tradução OpenAI concluída. Armazenando no cache...")
                    self._store_in_cache(content, source_language, target_language, 
                                       translated_text, model_used, confidence)
            
            logger.info(f"Orchestrator (Job: {job_id_log}, Chunk: {chunk_id_log}): Armazenando tradução no banco de dados...")
            db_store_success = self._store_in_database(request, translated_text, model_used, confidence)
            
            if not db_store_success:
                logger.error(f"Orchestrator (Job: {job_id_log}, Chunk: {chunk_id_log}): Falha ao armazenar tradução no banco.")
                # Não necessariamente falha toda a operação se o DB falhar, mas a publicação pode não ter o ID correto.
                # Decidimos retornar False para indicar que o processo não foi 100%
                return False 
            
            logger.info(f"Orchestrator (Job: {job_id_log}, Chunk: {chunk_id_log}): Publicando resultado...")
            publish_success = self._publish_result(request, translated_text, model_used, confidence)
            
            if publish_success:
                self.stats['translations_completed'] += 1
                processing_time = time.time() - start_time
                self.stats['total_processing_time'] += processing_time
                logger.info(f"Orchestrator (Job: {job_id_log}, Chunk: {chunk_id_log}): Processado com sucesso em {processing_time:.2f}s.")
                return True
            else:
                logger.error(f"Orchestrator (Job: {job_id_log}, Chunk: {chunk_id_log}): Falha ao publicar resultado.")
                return False
                
        except Exception as e:
            logger.error(f"Orchestrator (Job: {job_id_log}, Chunk: {chunk_id_log}): Erro crítico no processamento da tradução: {e}", exc_info=True)
            self.stats['translations_failed'] += 1
            return False
    
    def _check_cache(self, content: str, source_language: str, target_language: str) -> Optional[Dict[str, Any]]:
        """
        Verifica se a tradução está no cache
        
        Args:
            content: Conteúdo a ser traduzido
            source_language: Idioma de origem
            target_language: Idioma de destino
            
        Returns:
            Dados da tradução em cache ou None
        """
        try:
            return self.cache_service.get_translation(content, source_language, target_language)
        except Exception as e:
            logger.error(f"Erro ao verificar cache: {e}")
            return None
    
    def _translate_content(self, request: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Traduz conteúdo usando OpenAI
        
        Args:
            request: Dados da solicitação
            
        Returns:
            Resultado da tradução ou None se falhou
        """
        try:
            # Preparar chunk para tradução
            chunk_data = {
                'chunk_id': request['chunk_id'],
                'content': request['content'],
                'source_language': request['source_language'],
                'target_language': request['target_language']
            }
            
            # Traduzir usando o serviço
            results = self.translation_service.translate_chunks([chunk_data])
            
            if results and len(results) > 0:
                result = results[0]
                if result.get('translation_status') == 'completed':
                    return {
                        'translated_content': result['translated_content'],
                        'model_used': config.OPENAI_MODEL,
                        'confidence': 0.9,  # Confiança padrão para OpenAI
                        'processing_time': result.get('translation_timestamp', time.time())
                    }
                else:
                    logger.error(f"Tradução falhou: {result.get('translation_error', 'Unknown error')}")
                    return None
            else:
                logger.error("Nenhum resultado retornado pelo serviço de tradução")
                return None
                
        except Exception as e:
            logger.error(f"Erro na tradução do conteúdo: {e}")
            return None
    
    def _store_in_cache(self, content: str, source_language: str, target_language: str,
                       translated_text: str, model_used: str, confidence: float):
        """
        Armazena tradução no cache
        
        Args:
            content: Conteúdo original
            source_language: Idioma de origem
            target_language: Idioma de destino
            translated_text: Texto traduzido
            model_used: Modelo usado
            confidence: Confiança da tradução
        """
        try:
            metadata = {
                'model_used': model_used,
                'confidence': confidence,
                'cached_at': time.time()
            }
            
            self.cache_service.set_translation(
                content, source_language, target_language,
                translated_text, metadata
            )
            
        except Exception as e:
            logger.error(f"Erro ao armazenar no cache: {e}")
    
    def _store_in_database(self, request: Dict[str, Any], translated_text: str, 
                          model_used: str, confidence: float) -> bool:
        """
        Armazena tradução no banco de dados
        
        Args:
            request: Dados da solicitação original
            translated_text: Texto traduzido
            model_used: Modelo usado
            confidence: Confiança da tradução
            
        Returns:
            True se armazenou com sucesso, False caso contrário
        """
        try:
            version_id_from_request = request.get('version_id')
            sequence_number_from_request = request.get('sequence_number')
            original_message_chunk_id = request.get('chunk_id') # Para logging

            if version_id_from_request is None or sequence_number_from_request is None:
                logger.error(f"Version ID ou Sequence Number ausentes no request para chunk_id da mensagem {original_message_chunk_id}. Request: {request}")
                return False

            # Adicionar conversão e tratamento de erro para sequence_number
            try:
                chunk_index_int = int(sequence_number_from_request)
            except (ValueError, TypeError):
                logger.error(f"Sequence number '{sequence_number_from_request}' não é um inteiro válido para chunk_id da mensagem {original_message_chunk_id}.")
                return False

            # Buscar o UUID real do chunk_id no banco de dados
            # Usar chunk_index_int na chamada
            real_chunk_uuid = self.database_service.get_real_chunk_uuid(version_id_from_request, chunk_index_int) 

            if not real_chunk_uuid:
                logger.error(f"Não foi possível encontrar o chunk_id UUID real para version_id {version_id_from_request} e sequence_number {sequence_number_from_request} (chunk_id da mensagem: {original_message_chunk_id})")
                return False
            
            logger.info(f"Para chunk_id da mensagem {original_message_chunk_id}, o chunk_id UUID real do banco é: {real_chunk_uuid}")

            logger.info(f"Orch DB: Chamando database_service.store_translation com version_id={version_id_from_request}, real_chunk_id={real_chunk_uuid}")
            translation_id = self.database_service.store_translation(
                version_id=version_id_from_request,
                chunk_id=real_chunk_uuid, 
                language_code=request['target_language'], 
                translated_text=translated_text,
                model_used=model_used,
                confidence=confidence
            )
            logger.info(f"Orch DB: database_service.store_translation retornou translation_id: {translation_id}")
            
            db_success_flag = translation_id is not None
            logger.info(f"Orch DB: _store_in_database vai retornar: {db_success_flag}")
            return db_success_flag
            
        except Exception as e:
            logger.error(f"Erro ao armazenar no banco para chunk_id da mensagem {request.get('chunk_id')}: {e}")
            return False
    
    def _publish_result(self, request: Dict[str, Any], translated_text: str, 
                       model_used: str, confidence: float) -> bool:
        """
        Publica resultado da tradução no tópico translation.results
        
        Args:
            request: Dados da solicitação original
            translated_text: Texto traduzido
            model_used: Modelo usado
            confidence: Confiança da tradução
            
        Returns:
            True se publicou com sucesso, False caso contrário
        """
        try:
            # Conectar ao RabbitMQ se necessário
            if not self.connection or self.connection.is_closed:
                if not self._connect_rabbitmq():
                    return False
            
            # Preparar mensagem de resultado
            result_message = {
                'chunk_id': request['chunk_id'],
                'document_id': request.get('document_id'),
                'version_id': request.get('version_id'),
                'sequence_number': request.get('sequence_number'),
                'original_content': request['content'],
                'source_language': request['source_language'],
                'target_language': request['target_language'],
                'translated_content': translated_text,
                'model_used': model_used,
                'confidence': confidence,
                'timestamp': time.time(),
                'metadata': {
                    'original_request': request.get('metadata', {}),
                    'processing_stats': {
                        'cache_hit': request.get('cache_hit', False),
                        'processing_time': time.time() - request.get('start_time', time.time())
                    }
                }
            }
            
            # Serializar e publicar mensagem
            message_body = json.dumps(result_message)
            
            self.channel.basic_publish(
                exchange='',
                routing_key=config.TRANSLATION_RESULTS_TOPIC,
                body=message_body,
                properties=pika.BasicProperties(
                    delivery_mode=2,  # Tornar mensagem persistente
                    content_type='application/json'
                )
            )
            
            logger.debug(f"Resultado publicado para chunk {request['chunk_id']}")
            return True
            
        except Exception as e:
            logger.error(f"Erro ao publicar resultado: {e}")
            return False
    
    def process_batch_requests(self, requests: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Processa múltiplas solicitações de tradução em lote
        
        Args:
            requests: Lista de solicitações de tradução
            
        Returns:
            Estatísticas do processamento
        """
        start_time = time.time()
        
        successful = 0
        failed = 0
        
        logger.info(f"Processando lote de {len(requests)} solicitações")
        
        for request in requests:
            if self.process_translation_request(request):
                successful += 1
            else:
                failed += 1
        
        processing_time = time.time() - start_time
        
        return {
            'total_requests': len(requests),
            'successful': successful,
            'failed': failed,
            'processing_time': processing_time,
            'avg_time_per_request': processing_time / len(requests) if requests else 0
        }
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Retorna estatísticas do orquestrador
        
        Returns:
            Dicionário com estatísticas
        """
        total_requests = self.stats['cache_hits'] + self.stats['cache_misses']
        cache_hit_rate = (self.stats['cache_hits'] / total_requests * 100) if total_requests > 0 else 0
        avg_processing_time = (self.stats['total_processing_time'] / self.stats['translations_completed']) if self.stats['translations_completed'] > 0 else 0
        
        return {
            'cache_hit_rate': round(cache_hit_rate, 2),
            'total_requests': total_requests,
            'translations_completed': self.stats['translations_completed'],
            'translations_failed': self.stats['translations_failed'],
            'avg_processing_time': round(avg_processing_time, 3),
            'cache_service_healthy': self.cache_service.health_check(),
            'database_service_healthy': self.database_service.health_check()
        }
    
    def health_check(self) -> Dict[str, Any]:
        """
        Verifica a saúde de todos os componentes
        
        Returns:
            Status de saúde dos componentes
        """
        rabbitmq_ok = False
        if self.connection and not self.connection.is_closed:
            rabbitmq_ok = True
        else:
            # Tentar conectar para o health check, mas não manter a conexão se não for usada
            if self._connect_rabbitmq():
                rabbitmq_ok = True
                self._disconnect_rabbitmq() # Desconectar após a verificação para não manter ociosa
            else:
                rabbitmq_ok = False
        
        return {
            'orchestrator': True,
            'translation_service': hasattr(self.translation_service, 'client'),
            'cache_service': self.cache_service.health_check(),
            'database_service': self.database_service.health_check(),
            'rabbitmq_connected': rabbitmq_ok # Usar o resultado da tentativa de conexão
        }
    
    def close(self):
        """Fecha todas as conexões e recursos"""
        try:
            self._disconnect_rabbitmq()
            self.database_service.close()
            logger.info("Orquestrador de tradução finalizado")
        except Exception as e:
            logger.error(f"Erro ao fechar orquestrador: {e}")
