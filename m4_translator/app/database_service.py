"""
Serviço de conexão com banco de dados para M4 Translator
"""

import psycopg2
import psycopg2.pool
from psycopg2.extras import RealDictCursor
import os
from contextlib import contextmanager
import logging
from typing import Optional, List, Dict, Any # Adicionado Optional

logger = logging.getLogger(__name__)

class TranslationDatabaseService:
    """Serviço de acesso ao banco de dados para o M4 Translator"""
    
    def __init__(self):
        self.connection_pool = None
        self._initialize_pool()
    def health_check(self) -> dict:
        """
        Verifica a saúde da conexão com o banco de dados
        
        Returns:
            dict: Status da conexão com o banco de dados
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                return {
                    "status": "healthy",
                    "message": "Conexão com banco de dados OK"
                }
        except Exception as e:
            logger.error(f"Erro na verificação de saúde do banco: {e}")
            return {
                "status": "unhealthy",
                "message": f"Erro na conexão com banco de dados: {str(e)}"
            }

    
    def _initialize_pool(self) -> None:
        """Inicializa o pool de conexões"""
        try:
            # Configuração do banco a partir das variáveis de ambiente
            postgres_host = os.environ.get('POSTGRES_HOST', 'db')
            postgres_port = os.environ.get('POSTGRES_PORT', '5432')
            postgres_db = os.environ.get('POSTGRES_DB', 'mastigador_db')
            postgres_user = os.environ.get('POSTGRES_USER', 'user')
            postgres_password = os.environ.get('POSTGRES_PASSWORD', 'password')
            
            # Criação da DSN
            dsn = f"postgresql://{postgres_user}:{postgres_password}@{postgres_host}:{postgres_port}/{postgres_db}"
            
            # Inicialização do pool de conexões
            self.connection_pool = psycopg2.pool.ThreadedConnectionPool(
                minconn=1,
                maxconn=10,
                dsn=dsn,
                cursor_factory=RealDictCursor
            )
            logger.info("Pool de conexões inicializado com sucesso")
        except Exception as e:
            logger.error(f"Erro ao inicializar pool de conexões: {e}")
            raise
    
    @contextmanager
    def get_connection(self):
        """Context manager para obter conexão do pool"""
        connection = None
        try:
            connection = self.connection_pool.getconn()
            yield connection
        except Exception as e:
            if connection:
                connection.rollback()
            logger.error(f"Erro na conexão com banco: {e}")
            raise
        finally:
            if connection:
                self.connection_pool.putconn(connection)
    
    def get_translation_stats(self) -> dict:
        """
        Retorna estatísticas das traduções
        
        Returns:
            Dicionário com estatísticas
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # Verificar se a tabela existe
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = 'public' 
                        AND table_name = 'translations'
                    )
                """)
                table_exists = cursor.fetchone()['exists']
                
                if not table_exists:
                    logger.error("Tabela 'translations' não existe no banco")
                    return {
                        'total_translations': 0,
                        'translations_by_language': [],
                        'translations_by_model': [],
                        'recent_translations_1h': 0
                    }
                
                # Contagem total de traduções
                cursor.execute("SELECT COUNT(*) as total FROM translations")
                total_translations = cursor.fetchone()['total']
                
                # Traduções por idioma (usando language_code em vez de language)
                cursor.execute("""
                    SELECT language_code, COUNT(*) as count 
                    FROM translations 
                    GROUP BY language_code 
                    ORDER BY count DESC
                """)
                by_language = cursor.fetchall()
                
                # Traduções por modelo
                cursor.execute("""
                    SELECT model_used, COUNT(*) as count 
                    FROM translations 
                    WHERE model_used IS NOT NULL
                    GROUP BY model_used 
                    ORDER BY count DESC
                """)
                by_model = cursor.fetchall()
                
                # Traduções recentes (última hora)
                cursor.execute("""
                    SELECT COUNT(*) as recent_count 
                    FROM translations 
                    WHERE created_at > NOW() - INTERVAL '1 hour'
                """)
                recent_translations = cursor.fetchone()['recent_count']
                
                return {
                    'total_translations': total_translations,
                    'translations_by_language': [dict(row) for row in by_language],
                    'translations_by_model': [dict(row) for row in by_model],
                    'recent_translations_1h': recent_translations
                }
                
        except Exception as e:
            logger.error(f"Erro ao obter estatísticas: {e}")
            return {
                'total_translations': 0,
                'translations_by_language': [],
                'translations_by_model': [],
                'recent_translations_1h': 0
            }
    
    def store_translation(self, version_id, chunk_id, language_code, translated_text, model_used=None, confidence=None):
        """
        Armazena uma tradução no banco de dados
        
        Args:
            version_id: UUID da versão do documento
            chunk_id: UUID do chunk
            language_code: Código do idioma (ex: 'pt-br', 'en')
            translated_text: Texto traduzido
            model_used: Modelo de IA utilizado (opcional)
            confidence: Nível de confiança da tradução (opcional)
        
        Returns:
            ID da tradução inserida ou None em caso de erro
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                query = """
                INSERT INTO translations (
                    version_id, chunk_id, language_code, translated_text, 
                    model_used, confidence, created_at
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, NOW()
                )
                ON CONFLICT (version_id, chunk_id, language_code) DO UPDATE SET
                    translated_text = EXCLUDED.translated_text,
                    model_used = EXCLUDED.model_used,
                    confidence = EXCLUDED.confidence,
                    created_at = NOW()
                RETURNING translation_id;
                """
                
                logger.info(f"DB: Executando UPSERT para version_id={version_id}, chunk_id={chunk_id}")
                cursor.execute(query, (
                    version_id, chunk_id, language_code, translated_text, 
                    model_used, confidence
                ))
                logger.info(f"DB: INSERT executado.")
                
                result = cursor.fetchone()
                logger.info(f"DB: Resultado do fetchone(): {result}")
                
                logger.info(f"DB: Executando commit...")
                conn.commit() 
                logger.info(f"DB: Commit executado.")
                
                return_value = result['translation_id'] if result else None
                logger.info(f"DB: Retornando translation_id: {return_value}")
                return return_value
                
        except Exception as e:
            logger.error(f"DB: Exceção em store_translation: {e}", exc_info=True) # Adicionar exc_info=True
            if conn: 
                logger.info(f"DB: Executando rollback devido à exceção.")
                conn.rollback()
            logger.error(f"Erro ao armazenar tradução: {e}") # Manter este log também
            return None
    
    def get_translation(self, chunk_id, language_code):
        """
        Recupera uma tradução do banco de dados
        
        Args:
            chunk_id: UUID do chunk
            language_code: Código do idioma (ex: 'pt-br', 'en')
        
        Returns:
            Dicionário com a tradução ou None se não encontrada
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                query = """
                SELECT translation_id, version_id, chunk_id, language_code, 
                       translated_text, model_used, confidence, created_at
                FROM translations
                WHERE chunk_id = %s AND language_code = %s
                ORDER BY created_at DESC
                LIMIT 1
                """
                
                cursor.execute(query, (chunk_id, language_code))
                result = cursor.fetchone()
                
                return dict(result) if result else None
                
        except Exception as e:
            logger.error(f"Erro ao recuperar tradução: {e}")
            return None

    def get_real_chunk_uuid(self, version_id: str, chunk_index: int) -> Optional[str]:
        """
        Busca o chunk_id UUID real na tabela chunks_parent usando version_id e chunk_index.
        
        Args:
            version_id: UUID da versão do documento.
            chunk_index: Índice sequencial do chunk (sequence_number da mensagem).
            
        Returns:
            O chunk_id UUID como string, ou None se não encontrado ou em caso de erro.
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                query = """
                SELECT chunk_id 
                FROM chunks_parent 
                WHERE version_id = %s AND chunk_index = %s
                """
                cursor.execute(query, (version_id, chunk_index))
                result = cursor.fetchone()
                
                if result and 'chunk_id' in result:
                    return str(result['chunk_id']) # Garantir que é uma string UUID
                else:
                    logger.warning(f"Nenhum chunk_id encontrado para version_id {version_id} e chunk_index {chunk_index}")
                    return None
        except Exception as e:
            logger.error(f"Erro ao buscar real_chunk_uuid para version_id {version_id}, chunk_index {chunk_index}: {e}")
            return None
    
    def get_translations_by_version(self, version_id: str, language_code: Optional[str] = None):
        """
        Busca todas as traduções de uma versão específica

        Args:
            version_id: UUID da versão
            language_code: Código do idioma (opcional)

        Returns:
            Lista de traduções
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                if language_code:
                    query = """
                    SELECT translation_id, chunk_id, language_code, translated_text,
                           model_used, confidence, created_at
                    FROM translations
                    WHERE version_id = %s AND language_code = %s
                    ORDER BY created_at DESC
                    """
                    cursor.execute(query, (version_id, language_code))
                else:
                    query = """
                    SELECT translation_id, chunk_id, language_code, translated_text,
                           model_used, confidence, created_at
                    FROM translations
                    WHERE version_id = %s
                    ORDER BY created_at DESC
                    """
                    cursor.execute(query, (version_id,))

                return cursor.fetchall()

        except Exception as e:
            logger.error(f"Erro ao buscar traduções por versão: {e}")
            return []

    def get_chunks_without_translation(self, limit: Optional[int] = None):
        """
        Busca chunks que ainda não possuem tradução

        Args:
            limit: Limite de chunks a retornar (opcional)

        Returns:
            Lista de chunks sem tradução
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                query = """
                SELECT
                    cp.chunk_id,
                    cp.version_id,
                    cp.text,
                    cp.chunk_index,
                    d.document_id,
                    d.original_name
                FROM chunks_parent cp
                JOIN versions v ON cp.version_id = v.version_id
                JOIN documents d ON v.document_id = d.document_id
                LEFT JOIN translations t ON cp.chunk_id = t.chunk_id
                WHERE t.translation_id IS NULL
                AND LENGTH(cp.text) > 10
                ORDER BY d.created_at DESC, cp.chunk_index ASC
                """

                if limit:
                    query += f" LIMIT {limit}"

                cursor.execute(query)
                return cursor.fetchall()

        except Exception as e:
            logger.error(f"Erro ao buscar chunks sem tradução: {e}")
            return []

    def close(self):
        """Fecha o pool de conexões"""
        if self.connection_pool:
            self.connection_pool.closeall()
            logger.info("Pool de conexões fechado")
