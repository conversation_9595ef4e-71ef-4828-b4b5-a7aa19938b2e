# 🛠️ Guia de Desenvolvimento - Mastigador de Dados

Guia completo para criar novos módulos e integrar com o sistema existente.

## 🎯 Visão Geral

Este guia foi criado com base na análise dos **7 módulos existentes** e suas **56 APIs funcionais**. Ele fornece padrões testados e templates para acelerar o desenvolvimento.

## 🏗️ Padrões de Arquitetura

### Estrutura Padrão de Módulo
```
mx_novo_modulo/
├── Dockerfile
├── requirements.txt
├── app/
│   ├── __init__.py
│   ├── config.py
│   ├── main.py
│   ├── database_service.py (se usar DB)
│   ├── cache_service.py (se usar Redis)
│   └── consumer.py (se usar RabbitMQ)
└── static/ (opcional para UI)
    ├── index.html
    ├── script.js
    └── styles.css
```

### Numeração de Módulos
- **M1-M8**: Módulos do core system (já implementados)
- **M9+**: Novos módulos (disponíveis para expansão)

### Portas de Rede
```
M1: 8081  M2: 8082  M3: 8083  M4: 8084
M5: 8093  M6: 8086  M7: 8087  M8: 8080
M9: 8089  M10: 8090 M11: 8091 M12: 8092
```

## 🚀 Template para Novo Módulo

### 1. Estrutura FastAPI Base

```python
# app/main.py
import logging
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import psycopg2
from psycopg2 import extras
import redis
import pika

from .config import settings

# Configuração de logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuração FastAPI
app = FastAPI(
    title="M9 - Novo Módulo",
    description="Descrição do que o módulo faz",
    version="1.0.0"
)

# CORS (copiar dos módulos existentes)
origins = [
    "http://localhost:8080",  # API Gateway
    "http://localhost:8081",  # M1
    "http://localhost:8089",  # Este módulo
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Health check obrigatório
@app.get("/healthz", tags=["Status"])
async def health_check():
    """Health check do módulo"""
    # Testar conectividade com dependências
    db_status = "healthy"
    redis_status = "healthy" 
    rabbit_status = "healthy"
    
    # TODO: Implementar verificações reais
    
    overall_status = "healthy"
    return {
        "status": overall_status,
        "version": "1.0.0",
        "database": db_status,
        "redis": redis_status,
        "rabbitmq": rabbit_status
    }

# Endpoints específicos do módulo
@app.get("/", tags=["Main"])
async def root():
    """Endpoint raiz do módulo"""
    return {"message": "M9 - Novo Módulo funcionando!"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8089)
```

### 2. Configuração (config.py)

```python
# app/config.py
import os
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    # Database
    DB_HOST: str = os.getenv("DB_HOST", "postgres")
    DB_PORT: int = int(os.getenv("DB_PORT", "5432"))
    DB_NAME: str = os.getenv("DB_NAME", "mastigador")
    DB_USER: str = os.getenv("DB_USER", "postgres")
    DB_PASSWORD: str = os.getenv("DB_PASSWORD", "postgres")
    
    # Redis
    REDIS_HOST: str = os.getenv("REDIS_HOST", "redis")
    REDIS_PORT: int = int(os.getenv("REDIS_PORT", "6379"))
    REDIS_DB: int = int(os.getenv("REDIS_DB", "0"))
    
    # RabbitMQ
    RABBITMQ_HOST: str = os.getenv("RABBITMQ_HOST", "rabbitmq")
    RABBITMQ_PORT: int = int(os.getenv("RABBITMQ_PORT", "5672"))
    RABBITMQ_USER: str = os.getenv("RABBITMQ_USER", "guest")
    RABBITMQ_PASS: str = os.getenv("RABBITMQ_PASS", "guest")
    
    # Específico do módulo
    MODULE_NAME: str = "M9_NOVO_MODULO"
    API_VERSION: str = "1.0.0"

settings = Settings()
```

### 3. Database Service (se necessário)

```python
# app/database_service.py
import psycopg2
from psycopg2 import extras
from .config import settings
import logging

logger = logging.getLogger(__name__)

def get_db_connection():
    """Obter conexão com PostgreSQL"""
    try:
        conn = psycopg2.connect(
            host=settings.DB_HOST,
            port=settings.DB_PORT,
            database=settings.DB_NAME,
            user=settings.DB_USER,
            password=settings.DB_PASSWORD
        )
        return conn
    except Exception as e:
        logger.error(f"Erro ao conectar no banco: {e}")
        raise

def execute_query(query: str, params: tuple = None):
    """Executar query no banco"""
    conn = None
    try:
        conn = get_db_connection()
        with conn.cursor(cursor_factory=extras.DictCursor) as cur:
            cur.execute(query, params)
            if query.strip().upper().startswith('SELECT'):
                return cur.fetchall()
            conn.commit()
            return cur.rowcount
    except Exception as e:
        if conn:
            conn.rollback()
        logger.error(f"Erro na query: {e}")
        raise
    finally:
        if conn:
            conn.close()
```

### 4. Cache Service (se necessário)

```python
# app/cache_service.py
import redis
import json
import logging
from .config import settings

logger = logging.getLogger(__name__)

class CacheService:
    def __init__(self):
        self.redis_client = redis.Redis(
            host=settings.REDIS_HOST,
            port=settings.REDIS_PORT,
            db=settings.REDIS_DB,
            decode_responses=True
        )
    
    def get(self, key: str):
        """Obter valor do cache"""
        try:
            value = self.redis_client.get(key)
            return json.loads(value) if value else None
        except Exception as e:
            logger.error(f"Erro ao buscar cache {key}: {e}")
            return None
    
    def set(self, key: str, value, expire: int = 3600):
        """Definir valor no cache"""
        try:
            self.redis_client.setex(
                key, 
                expire, 
                json.dumps(value, default=str)
            )
            return True
        except Exception as e:
            logger.error(f"Erro ao salvar cache {key}: {e}")
            return False
    
    def delete(self, key: str):
        """Remover do cache"""
        try:
            return self.redis_client.delete(key)
        except Exception as e:
            logger.error(f"Erro ao deletar cache {key}: {e}")
            return False

cache_service = CacheService()
```

### 5. Consumer RabbitMQ (se necessário)

```python
# app/consumer.py
import pika
import json
import logging
from .config import settings

logger = logging.getLogger(__name__)

class RabbitMQConsumer:
    def __init__(self):
        self.connection = None
        self.channel = None
        
    def connect(self):
        """Conectar ao RabbitMQ"""
        credentials = pika.PlainCredentials(
            settings.RABBITMQ_USER, 
            settings.RABBITMQ_PASS
        )
        parameters = pika.ConnectionParameters(
            host=settings.RABBITMQ_HOST,
            port=settings.RABBITMQ_PORT,
            credentials=credentials,
            heartbeat=600,
            blocked_connection_timeout=300
        )
        self.connection = pika.BlockingConnection(parameters)
        self.channel = self.connection.channel()
        
        # Declarar exchange e queue
        self.channel.exchange_declare(
            exchange='message_exchange',
            exchange_type='direct',
            durable=True
        )
        
    def process_message(self, ch, method, properties, body):
        """Processar mensagem recebida"""
        try:
            data = json.loads(body)
            logger.info(f"Processando: {data}")
            
            # TODO: Implementar lógica específica
            
            # Acknowledg message
            ch.basic_ack(delivery_tag=method.delivery_tag)
            
        except Exception as e:
            logger.error(f"Erro ao processar mensagem: {e}")
            ch.basic_nack(delivery_tag=method.delivery_tag, requeue=False)
    
    def start_consuming(self, queue_name: str):
        """Iniciar consumo de mensagens"""
        self.channel.queue_declare(queue=queue_name, durable=True)
        self.channel.basic_consume(
            queue=queue_name,
            on_message_callback=self.process_message
        )
        logger.info(f"Aguardando mensagens em {queue_name}")
        self.channel.start_consuming()

if __name__ == "__main__":
    consumer = RabbitMQConsumer()
    consumer.connect()
    consumer.start_consuming("nova_queue")
```

### 6. Dockerfile

```dockerfile
# Dockerfile
FROM python:3.11-slim

WORKDIR /app

# Instalar dependências do sistema
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Copiar requirements e instalar dependências Python
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copiar código da aplicação
COPY . .

# Expor porta
EXPOSE 8089

# Comando para executar a aplicação
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8089"]
```

### 7. Requirements.txt

```txt
# requirements.txt
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0
psycopg2-binary==2.9.9
redis==5.0.1
pika==1.3.2
requests==2.31.0
python-multipart==0.0.6
```

## 🔧 Integração com Sistema Existente

### 1. Adicionar ao Docker Compose

```yaml
# Adicionar ao docker-compose.yml
services:
  m9_novo_modulo:
    build: ./m9_novo_modulo
    container_name: m9_novo_modulo
    ports:
      - "8089:8089"
    environment:
      - DB_HOST=postgres
      - REDIS_HOST=redis
      - RABBITMQ_HOST=rabbitmq
      - DB_PASSWORD=${DB_PASSWORD}
    depends_on:
      - postgres
      - redis
      - rabbitmq
    networks:
      - mastigador_network
    restart: unless-stopped
```

### 2. Integrar com API Gateway (M8)

Adicionar rotas no M8 para expor o novo módulo:

```python
# No M8 - app/main.py
@app.get("/v1/novo-modulo/status")
async def novo_modulo_status():
    """Proxy para status do novo módulo"""
    async with httpx.AsyncClient() as client:
        response = await client.get("http://m9_novo_modulo:8089/healthz")
        return response.json()

@app.post("/v1/novo-modulo/processar")
async def novo_modulo_processar(dados: dict):
    """Proxy para processamento do novo módulo"""
    async with httpx.AsyncClient() as client:
        response = await client.post(
            "http://m9_novo_modulo:8089/processar",
            json=dados
        )
        return response.json()
```

### 3. Atualizar Health Check Agregado

```python
# No M8 - adicionar ao health check
async def check_novo_modulo():
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(
                "http://m9_novo_modulo:8089/healthz", 
                timeout=5
            )
            return response.status_code == 200
    except:
        return False

# Adicionar ao endpoint /healthz
module_health["m9_novo_modulo"] = await check_novo_modulo()
```

## 📋 Checklist de Desenvolvimento

### ✅ Preparação
- [ ] Definir número do módulo (M9, M10, etc.)
- [ ] Reservar porta de rede
- [ ] Definir responsabilidades e APIs
- [ ] Criar estrutura de diretórios

### ✅ Implementação Base
- [ ] Configurar FastAPI com CORS
- [ ] Implementar health check obrigatório
- [ ] Configurar logging adequado
- [ ] Criar modelos Pydantic para requests/responses

### ✅ Integração com Dependências
- [ ] Database service (se necessário)
- [ ] Cache service (se necessário) 
- [ ] RabbitMQ consumer (se necessário)
- [ ] Integração com APIs externas (se necessário)

### ✅ Docker e Deployment
- [ ] Criar Dockerfile otimizado
- [ ] Configurar requirements.txt
- [ ] Adicionar ao docker-compose.yml
- [ ] Testar build e execução local

### ✅ Integração com Sistema
- [ ] Adicionar rotas no API Gateway (M8)
- [ ] Atualizar health check agregado
- [ ] Configurar monitoramento
- [ ] Adicionar aos scripts de teste

### ✅ Documentação e Testes
- [ ] Documentar endpoints no OpenAPI/Swagger
- [ ] Criar testes unitários
- [ ] Criar testes de integração
- [ ] Atualizar documentação do projeto

## 🧪 Padrões de Teste

### Teste de Health Check
```python
import requests

def test_health_check():
    response = requests.get("http://localhost:8089/healthz")
    assert response.status_code == 200
    
    data = response.json()
    assert data["status"] == "healthy"
    assert "version" in data
```

### Teste de API
```python
def test_api_endpoint():
    payload = {"teste": "dados"}
    response = requests.post(
        "http://localhost:8089/processar", 
        json=payload
    )
    assert response.status_code == 200
```

### Teste de Integração
```python
def test_integration_with_m8():
    # Testar via API Gateway
    response = requests.get("http://localhost:8080/v1/novo-modulo/status")
    assert response.status_code == 200
```

## 🔍 Debugging e Logs

### Configuração de Logs
```python
import logging

# Configurar logs estruturados
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

# Usar logs informativos
logger.info("Processando solicitação")
logger.warning("Situação de atenção")
logger.error("Erro ocorrido: %s", str(error))
```

### Visualizar Logs
```bash
# Logs do módulo específico
docker-compose logs -f m9_novo_modulo

# Logs de todos os módulos
docker-compose logs -f

# Logs em tempo real
docker-compose logs -f --tail=100
```

## 🚀 Exemplos Práticos

### Módulo de Análise de Sentimentos (M9)
```python
# Endpoint para análise de sentimentos
@app.post("/analyze", tags=["Analysis"])
async def analyze_sentiment(request: SentimentRequest):
    """Analisa sentimento de um texto"""
    try:
        # Buscar no cache primeiro
        cache_key = f"sentiment:{hash(request.text)}"
        cached_result = cache_service.get(cache_key)
        
        if cached_result:
            return cached_result
        
        # Processar com IA (OpenAI, etc.)
        result = await process_sentiment(request.text)
        
        # Salvar no cache
        cache_service.set(cache_key, result, expire=3600)
        
        # Salvar no banco se necessário
        save_analysis_to_db(request.text, result)
        
        return result
        
    except Exception as e:
        logger.error(f"Erro na análise: {e}")
        raise HTTPException(status_code=500, detail="Erro interno")
```

### Módulo de Notificações (M10)
```python
# Consumer para envio de notificações
def process_notification(ch, method, properties, body):
    """Processar notificação recebida"""
    try:
        data = json.loads(body)
        
        notification_type = data.get("type")
        recipient = data.get("recipient")
        message = data.get("message")
        
        if notification_type == "email":
            send_email(recipient, message)
        elif notification_type == "slack":
            send_slack_message(recipient, message)
        
        ch.basic_ack(delivery_tag=method.delivery_tag)
        
    except Exception as e:
        logger.error(f"Erro ao enviar notificação: {e}")
        ch.basic_nack(delivery_tag=method.delivery_tag, requeue=True)
```

## 📚 Recursos e Referências

### Documentação dos Módulos Existentes
- **M1**: [http://localhost:8081/docs](http://localhost:8081/docs) - Drive Connector
- **M2**: [http://localhost:8082/docs](http://localhost:8082/docs) - Task Orchestrator  
- **M4**: [http://localhost:8084/docs](http://localhost:8084/docs) - Translator
- **M5**: [http://localhost:8093/docs](http://localhost:8093/docs) - DB Browser
- **M6**: [http://localhost:8086/docs](http://localhost:8086/docs) - Vector Indexer
- **M7**: [http://localhost:8087/docs](http://localhost:8087/docs) - Diff Engine
- **M8**: [http://localhost:8080/docs](http://localhost:8080/docs) - API Gateway

### Tecnologias Utilizadas
- **FastAPI**: Framework web moderno e rápido
- **Pydantic**: Validação de dados
- **PostgreSQL**: Banco de dados principal
- **Redis**: Cache e sessões
- **RabbitMQ**: Message broker
- **Docker**: Containerização

### Scripts Úteis
```bash
# Criar novo módulo a partir do template
python scripts/create_module.py --name="M9_SENTIMENT_ANALYZER"

# Testar novo módulo
python scripts/test_module.py --module="m9_sentiment_analyzer"

# Adicionar ao sistema
python scripts/integrate_module.py --module="m9_sentiment_analyzer"
```

---

**💡 Dica**: Sempre use os módulos existentes como referência. Eles seguem padrões testados e funcionais que garantem compatibilidade com o sistema.
