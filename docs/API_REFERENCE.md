# 📚 Referência de APIs - Mastigador de Dados

Documentação consolidada de todos os **56 endpoints** disponíveis no sistema.

## 🎯 Visão Geral

O sistema expõe APIs através de **7 módulos** principais, totalizando **56 endpoints** funcionais. Todos seguem padrões REST e estão documentados via OpenAPI/Swagger.

## 🌐 URLs Base dos Módulos

| Módulo | URL Base | Documentação | Endpoints |
|--------|----------|--------------|-----------|
| **M1 Drive Connector** | http://localhost:8081 | [/docs](http://localhost:8081/docs) | 3 |
| **M2 Task Orchestrator** | http://localhost:8082 | [/docs](http://localhost:8082/docs) | 9 |
| **M4 Translator** | http://localhost:8084 | [/docs](http://localhost:8084/docs) | 8 |
| **M5 DB Browser** | http://localhost:8093 | [/docs](http://localhost:8093/docs) | 4 |
| **M6 Vector Indexer** | http://localhost:8086 | [/docs](http://localhost:8086/docs) | 5 |
| **M7 Diff Engine** | http://localhost:8087 | [/docs](http://localhost:8087/docs) | 11 |
| **M8 API Gateway** | http://localhost:8080 | [/docs](http://localhost:8080/docs) | 16 |

## 🔥 M1 - Drive Connector (3 endpoints)

### Health Check
```http
GET /healthz
```
**Descrição**: Verifica a saúde do serviço e conectividade com Google Drive.

**Resposta**:
```json
{
  "status": "healthy",
  "version": "1.0.0",
  "drive_access": "connected"
}
```

### Importar Pasta
```http
POST /import
Content-Type: application/json

{
  "drive_folder_id": "1ABC123...",
  "max_files": 100
}
```
**Descrição**: Importa arquivos de uma pasta do Google Drive.

### Navegar Pasta
```http
GET /browse/{folder_id}
```
**Descrição**: Lista arquivos e subpastas de uma pasta específica.

---

## ⚙️ M2 - Task Orchestrator (9 endpoints)

### Health Check
```http
GET /healthz
```
**Descrição**: Verifica saúde do M2 Task Orchestrator.

### Listar Tarefas
```http
GET /tasks?status=PENDING&module=extractor&limit=100&offset=0
```
**Descrição**: Lista tarefas com filtros opcionais e paginação.

**Parâmetros**:
- `status`: PENDING, PROCESSING, COMPLETED, FAILED, RETRY, CANCELLED
- `module`: Nome do módulo
- `limit`: Máximo 1000
- `offset`: Para paginação

### Criar Tarefa Manual
```http
POST /tasks
Content-Type: application/json

{
  "module": "extractor",
  "payload": {
    "file_path": "/data/raw/file.pdf",
    "original_m1_task_id": "uuid-from-m1"
  }
}
```
**Descrição**: Cria nova tarefa manualmente (útil para testes).

### Estatísticas de Tarefas
```http
GET /tasks/stats
```
**Descrição**: Retorna estatísticas agregadas das tarefas.

**Resposta**:
```json
{
  "total_tasks": 150,
  "pending_tasks": 10,
  "processing_tasks": 5,
  "completed_tasks": 130,
  "failed_tasks": 3,
  "retry_tasks": 2,
  "cancelled_tasks": 0,
  "tasks_by_module": {
    "extractor": 50,
    "translator": 100
  }
}
```

### Detalhes de Tarefa
```http
GET /tasks/{job_id}
```
**Descrição**: Retorna detalhes de uma tarefa específica.

### Atualizar Tarefa (PATCH)
```http
PATCH /tasks/{job_id}
Content-Type: application/json

{
  "status": "COMPLETED",
  "last_error": null,
  "retries": 0
}
```
**Descrição**: Atualização parcial de uma tarefa.

### Cancelar Tarefa
```http
PUT /tasks/{job_id}/cancel
```
**Descrição**: Cancela uma tarefa (status = CANCELLED).

### Retentar Tarefa
```http
PUT /tasks/{job_id}/retry
```
**Descrição**: Reenfileira tarefa que falhou (limitado a 5 tentativas).

### Atualizar Status (por Módulo)
```http
PUT /tasks/{job_id}/status
Content-Type: application/json

{
  "status": "PROCESSING",
  "last_error": null
}
```
**Descrição**: Endpoint para módulos atualizarem status de tarefas.

---

## 🌐 M4 - Translator (8 endpoints)

### UI de Teste
```http
GET /
```
**Descrição**: Página inicial com interface de teste.

### Traduzir Texto
```http
POST /translate
Content-Type: application/json

{
  "text": "Hello world",
  "target_language": "pt-BR",
  "source_language": "auto"
}
```
**Descrição**: Traduz texto fornecido.

**Resposta**:
```json
{
  "original_text": "Hello world",
  "translated_text": "Olá mundo",
  "source_language": "en",
  "target_language": "pt-BR",
  "cached": false
}
```

### Health Check
```http
GET /healthz
GET /health
```
**Descrição**: Verifica saúde de todos os componentes (disponível em ambas URLs).

### Estatísticas do Sistema
```http
GET /stats
```
**Descrição**: Retorna estatísticas detalhadas do sistema.

### Estatísticas do Cache
```http
GET /cache/stats
```
**Descrição**: Estatísticas do cache Redis.

### Limpar Cache
```http
GET /cache/clear
```
**Descrição**: Limpa todas as traduções do cache (usar com cuidado).

### Traduções por Versão
```http
GET /translations/{version_id}
```
**Descrição**: Busca traduções de uma versão específica.

---

## 🗄️ M5 - DB Browser (4 endpoints)

### Health Check
```http
GET /healthz
```
**Descrição**: Verifica saúde do DB Browser.

### Listar Documentos
```http
GET /documents?limit=50&offset=0
```
**Descrição**: Lista documentos do banco.

### Detalhes do Documento
```http
GET /documents/{document_id}
```
**Descrição**: Retorna detalhes de um documento específico.

### Executar Query
```http
GET /query?sql=SELECT COUNT(*) FROM documents
```
**Descrição**: Executa query SQL personalizada (apenas SELECT).

---

## 🔍 M6 - Vector Indexer (5 endpoints)

### Health Check
```http
GET /healthz
```
**Descrição**: Health check do M6 Vector Indexer.

### Indexar Documento
```http
POST /index
Content-Type: application/json

{
  "document_id": "doc123",
  "title": "Documento Exemplo",
  "content": "Conteúdo do documento...",
  "metadata": {
    "author": "João",
    "category": "contrato"
  }
}
```
**Descrição**: Indexa documento para busca vetorial.

### Buscar Documentos
```http
POST /search
Content-Type: application/json

{
  "query": "contratos de fornecimento",
  "limit": 10,
  "similarity_threshold": 0.7
}
```
**Descrição**: Busca documentos por similaridade vetorial.

### Remover do Índice
```http
DELETE /index/{document_id}
```
**Descrição**: Remove documento do índice vetorial.

### Estatísticas do Índice
```http
GET /stats
```
**Descrição**: Estatísticas do índice vetorial.

---

## 📊 M7 - Diff Engine (11 endpoints)

### Interface Web
```http
GET /
```
**Descrição**: Serve página principal da interface web.

### Health Check
```http
GET /healthz
```
**Descrição**: Health check do M7 Diff Engine.

### Comparar Documentos (GET)
```http
GET /diff?document_id=doc123&version1=v1&version2=v2
```
**Descrição**: Compara documentos via query parameters.

### Comparar Documentos (POST)
```http
POST /diff
Content-Type: application/json

{
  "document_id": "doc123",
  "version1": "v1",
  "version2": "v2",
  "diff_type": "unified"
}
```
**Descrição**: Compara duas versões de um documento.

**Resposta**:
```json
{
  "document_id": "doc123",
  "version1": "v1",
  "version2": "v2",
  "diff_type": "unified",
  "differences": [
    {
      "line_number": 15,
      "type": "modified",
      "old_content": "texto antigo",
      "new_content": "texto novo"
    }
  ],
  "summary": {
    "added_lines": 5,
    "removed_lines": 3,
    "modified_lines": 2
  }
}
```

### Listar Versões do Documento
```http
GET /document/{document_id}/versions
```
**Descrição**: Lista todas as versões de um documento.

### Listar Todos os Documentos
```http
GET /api/documents
```
**Descrição**: Lista todos os documentos disponíveis.

### Estatísticas do Cache
```http
GET /cache/stats
```
**Descrição**: Estatísticas do cache Redis.

### Invalidar Cache do Documento
```http
DELETE /cache/document/{document_id}
```
**Descrição**: Invalida cache de documento específico.

### Limpar Todo o Cache
```http
DELETE /cache/all
```
**Descrição**: Limpa todo o cache do M7.

### Listar Documentos em Cache
```http
GET /cache/documents
```
**Descrição**: Lista documentos com cache ativo.

### Diff a Nível de Caractere
```http
POST /diff/character-level
Content-Type: application/json

{
  "line_old": "texto antigo",
  "line_new": "texto novo"
}
```
**Descrição**: Compara duas linhas a nível de caractere.

---

## 🌉 M8 - API Gateway (16 endpoints)

### Health Check Agregado
```http
GET /healthz
```
**Descrição**: Verifica saúde de todos os módulos.

### Importar do Google Drive
```http
POST /v1/import
Content-Type: application/json

{
  "drive_folder_id": "1ABC123...",
  "max_files": 100
}
```
**Descrição**: Proxy para importação via M1.

### Listar Tarefas
```http
GET /v1/tasks?status=PENDING&limit=50
```
**Descrição**: Proxy para M2 - listar tarefas.

### Detalhes da Tarefa
```http
GET /v1/tasks/{job_id}
```
**Descrição**: Proxy para M2 - detalhes da tarefa.

### Atualizar Status da Tarefa
```http
PATCH /v1/tasks/{job_id}
Content-Type: application/json

{
  "status": "COMPLETED"
}
```
**Descrição**: Proxy para M2 - atualizar tarefa.

### Listar Documentos
```http
GET /v1/documents?limit=50&offset=0
```
**Descrição**: Proxy para M5 - listar documentos.

### Detalhes do Documento
```http
GET /v1/documents/{document_id}?format=json
```
**Descrição**: Proxy para M5 - detalhes do documento.

**Parâmetros**:
- `format`: json, html

### Executar Query SQL
```http
GET /v1/query?sql=SELECT COUNT(*) FROM documents
```
**Descrição**: Proxy para M5 - executar query.

### Indexar Documento
```http
POST /v1/index
Content-Type: application/json

{
  "document_id": "doc123",
  "title": "Documento",
  "content": "Conteúdo..."
}
```
**Descrição**: Proxy para M6 - indexar documento.

### Busca Semântica
```http
POST /v1/search
Content-Type: application/json

{
  "query": "contratos de fornecimento",
  "limit": 10
}
```
**Descrição**: Proxy para M6 - busca vetorial.

### Busca por Vetor
```http
POST /v1/search/vector
Content-Type: application/json

{
  "vector": [0.1, 0.2, 0.3, ...],
  "limit": 10
}
```
**Descrição**: Busca direta por vetor.

### Status de Indexação
```http
GET /v1/index-status/{task_id}
```
**Descrição**: Verifica status de indexação.

### Comparar Versões
```http
POST /v1/diff
Content-Type: application/json

{
  "document_id": "doc123",
  "version1": "v1",
  "version2": "v2"
}
```
**Descrição**: Proxy para M7 - comparar documentos.

### Exportar Diferenças
```http
POST /v1/export/diff
Content-Type: application/json

{
  "document_id": "doc123",
  "version1": "v1",
  "version2": "v2",
  "format": "pdf"
}
```
**Descrição**: Exportar diferenças em formato específico.

### Chat com IA
```http
POST /v1/chat
Content-Type: application/json

{
  "message": "Resuma este documento",
  "context": {
    "document_id": "doc123"
  }
}
```
**Descrição**: Interação com IA Orchestrator.

### Stream de Eventos
```http
GET /v1/events
```
**Descrição**: Stream de eventos via Server-Sent Events (SSE).

---

## 🔧 Padrões de Integração

### Headers Padrão
```http
Content-Type: application/json
Accept: application/json
User-Agent: MastigadorDados/1.0
```

### Códigos de Status HTTP
- **200**: Sucesso
- **201**: Criado
- **400**: Erro de validação
- **404**: Não encontrado
- **500**: Erro interno
- **503**: Serviço indisponível

### Formato de Erro
```json
{
  "detail": "Descrição do erro",
  "error_code": "VALIDATION_ERROR",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Paginação
```json
{
  "items": [...],
  "total": 150,
  "limit": 50,
  "offset": 0,
  "has_next": true
}
```

## 🚀 Exemplos de Fluxos Completos

### Fluxo 1: Importar e Processar Documentos
```bash
# 1. Importar via API Gateway
curl -X POST "http://localhost:8080/v1/import" \
  -H "Content-Type: application/json" \
  -d '{"drive_folder_id": "1ABC123..."}'

# 2. Monitorar progresso
curl "http://localhost:8080/v1/tasks?status=PROCESSING"

# 3. Verificar documentos processados
curl "http://localhost:8080/v1/documents"
```

### Fluxo 2: Busca Semântica
```bash
# 1. Indexar documento
curl -X POST "http://localhost:8080/v1/index" \
  -H "Content-Type: application/json" \
  -d '{
    "document_id": "doc123",
    "title": "Contrato de Fornecimento",
    "content": "Este contrato estabelece..."
  }'

# 2. Fazer busca
curl -X POST "http://localhost:8080/v1/search" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "fornecimento de materiais",
    "limit": 5
  }'
```

### Fluxo 3: Comparar Versões
```bash
# 1. Listar versões disponíveis
curl "http://localhost:8087/document/doc123/versions"

# 2. Comparar versões
curl -X POST "http://localhost:8080/v1/diff" \
  -H "Content-Type: application/json" \
  -d '{
    "document_id": "doc123",
    "version1": "v1",
    "version2": "v2"
  }'
```

## 🔐 Autenticação e Segurança

### Google Drive API
- OAuth2 flow para acesso ao Drive
- Tokens armazenados em `token.json`
- Renovação automática de tokens

### OpenAI API
- Chave API configurada via variável de ambiente
- Rate limiting implementado
- Cache para reduzir custos

### Validação de Entrada
- Todos os endpoints validam entrada via Pydantic
- Sanitização de queries SQL
- Validação de UUIDs e IDs

## 📝 SDKs e Bibliotecas Cliente

### Python SDK (Exemplo)
```python
import requests

class MastigadorAPI:
    def __init__(self, base_url="http://localhost:8080"):
        self.base_url = base_url
    
    def import_folder(self, folder_id: str):
        response = requests.post(
            f"{self.base_url}/v1/import",
            json={"drive_folder_id": folder_id}
        )
        return response.json()
    
    def search_documents(self, query: str, limit: int = 10):
        response = requests.post(
            f"{self.base_url}/v1/search",
            json={"query": query, "limit": limit}
        )
        return response.json()
    
    def compare_documents(self, doc_id: str, v1: str, v2: str):
        response = requests.post(
            f"{self.base_url}/v1/diff",
            json={
                "document_id": doc_id,
                "version1": v1,
                "version2": v2
            }
        )
        return response.json()

# Uso
api = MastigadorAPI()
results = api.search_documents("contratos de fornecimento")
```

### JavaScript SDK (Exemplo)
```javascript
class MastigadorAPI {
    constructor(baseUrl = "http://localhost:8080") {
        this.baseUrl = baseUrl;
    }
    
    async importFolder(folderId) {
        const response = await fetch(`${this.baseUrl}/v1/import`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ drive_folder_id: folderId })
        });
        return response.json();
    }
    
    async searchDocuments(query, limit = 10) {
        const response = await fetch(`${this.baseUrl}/v1/search`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ query, limit })
        });
        return response.json();
    }
}

// Uso
const api = new MastigadorAPI();
const results = await api.searchDocuments("contratos de fornecimento");
```

## 🧪 Testando APIs

### Health Checks de Todos os Módulos
```bash
#!/bin/bash
# Script para testar todos os health checks

MODULES=(
    "8081:M1"
    "8082:M2" 
    "8084:M4"
    "8093:M5"
    "8086:M6"
    "8087:M7"
    "8080:M8"
)

for module in "${MODULES[@]}"; do
    port=$(echo $module | cut -d: -f1)
    name=$(echo $module | cut -d: -f2)
    
    status=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost:$port/healthz")
    
    if [ $status -eq 200 ]; then
        echo "✅ $name (porta $port): OK"
    else
        echo "❌ $name (porta $port): ERRO ($status)"
    fi
done
```

### Teste de Carga com Apache Bench
```bash
# Testar endpoint de busca
ab -n 1000 -c 10 -T 'application/json' -p search_payload.json \
   http://localhost:8080/v1/search

# Arquivo search_payload.json:
# {"query": "teste", "limit": 5}
```

---

**📖 Esta documentação é gerada automaticamente** a partir das especificações OpenAPI dos módulos e é atualizada regularmente para refletir mudanças nas APIs.
