# Plano de Validação do Sistema Mastigador de Dados em Lote

Este documento detalha o plano de validação abrangente para todos os módulos, endpoints, variáveis de ambiente, health checks e páginas web do sistema. O objetivo é garantir que todos os componentes estão funcionais e que as integrações estão operando conforme o esperado.

## 1. Inventário de Componentes e Templates de Validação

### 1.1. Módulos do Sistema

| Módulo | Descrição Breve | Funcionalidades Principais | Endpoints Principais | Variáveis de Ambiente Relevantes | Health Check Endpoint | Páginas Web Relevantes |
|---|---|---|---|---|---|---|
| **M1: Drive Connector** | Conecta-se a fontes de dados (e.g., Google Drive) para ingestão de documentos. | Ingestão de documentos, notificação de novos arquivos. | `/upload`, `/status/{job_id}` | `RABBITMQ_HOST`, `M2_API_URL` | `/healthz` | N/A |
| **M2: Task Orchestrator** | Orquestra o fluxo de trabalho de processamento de documentos. | Gerenciamento de tarefas, filas, status de jobs. | `/tasks`, `/tasks/{task_id}`, `/tasks/stats`, `/healthz` | `DATABASE_URL`, `RABBITMQ_HOST` | `/healthz` | N/A |
| **M3: Extractor** | Extrai texto e metadados de documentos. | OCR, extração de texto, processamento de PDFs. | N/A (Consumer RabbitMQ) | `RABBITMQ_HOST`, `M2_API_URL`, `POSTGRES_HOST` | N/A (Verificar logs do consumer) | N/A |
| **M4: Translator** | Realiza a tradução de conteúdo. | Tradução de texto, gerenciamento de cache de tradução. | `/translate`, `/healthz` | `OPENAI_API_KEY`, `REDIS_HOST`, `RABBITMQ_HOST`, `M2_API_URL` | `/healthz` | N/A |
| **M5: DB Browser** | Interface para navegar e consultar o banco de dados. | Visualização de dados, execução de queries. | `/query`, `/healthz` | `DATABASE_URL`, `ADMIN_TOKEN` | `/healthz` | N/A |
| **M6: Vector Indexer** | Indexa documentos para busca semântica. | Geração de embeddings, indexação de vetores. | `/embed`, `/search`, `/healthz` | `OPENAI_API_KEY`, `REDIS_HOST`, `POSTGRES_HOST` | `/healthz` | N/A |
| **M7: Diff Engine** | Compara documentos e gera relatórios de diferenças. | Comparação de texto, visualização de diffs. | `/diff`, `/healthz` | `REDIS_HOST`, `POSTGRES_HOST` | `/healthz` | N/A |
| **M8: API Gateway** | Ponto de entrada unificado para os microsserviços. | Roteamento de requisições, agregação de APIs. | `/v1/documents`, `/v1/tasks`, `/healthz` | `M1_DRIVE_CONNECTOR_URL`, `M2_TASK_ORCHESTRATOR_URL`, etc. | `/healthz` | N/A |
| **M10: AI Orchestrator** | Orquestra interações com modelos de IA. | Geração de respostas, auditoria de queries. | `/query`, `/metrics`, `/monitor/api/audit/queries`, `/health` | `OPENAI_API_KEY`, `REDIS_HOST`, `POSTGRES_HOST` | `/health` | `/monitor/` (UI de monitoramento) |
| **M14: Importek BFF** | Backend-for-Frontend para a SPA do Mastigador. | Agregação de dados para dashboard, roteamento de UI. | `/api/dashboard/metrics`, `/healthz` | `M8_API_GATEWAY_URL`, `M10_AI_ORCHESTRATOR_URL` | `/healthz` | `/` (SPA principal), `/monitor` (UI de monitoramento do M10) |

### 1.2. Templates de Validação

#### 1.2.1. Template de Validação de Endpoint API

| Critério | Descrição | Resultado Esperado | Status (Pass/Fail) | Observações |
|---|---|---|---|---|
| **Status HTTP 200 OK** | Requisição bem-sucedida. | Retorno de status 200. | | |
| **Estrutura da Resposta** | Formato JSON/dados conforme especificação. | Campos obrigatórios presentes, tipos de dados corretos. | | |
| **Dados Válidos** | Resposta contém dados corretos para entrada válida. | Dados consistentes com a entrada e o estado do sistema. | | |
| **Tratamento de Erros (4xx)** | Resposta adequada para entradas inválidas (e.g., 400 Bad Request, 404 Not Found). | Mensagem de erro clara, status HTTP correto. | | |
| **Tratamento de Erros (5xx)** | Resposta adequada para erros internos do servidor (e.g., 500 Internal Server Error). | Mensagem de erro genérica, log de erro no backend. | | |
| **Autenticação/Autorização** | Acesso restrito conforme necessário. | Requisições sem token/com token inválido são rejeitadas (401/403). | | |
| **Tempo de Resposta** | Latência dentro dos limites aceitáveis. | < 500ms (ideal), < 2s (aceitável). | | |

#### 1.2.2. Template de Validação de Variável de Ambiente

| Variável | Módulo(s) | Valor Esperado/Formato | Status (Presente/Correto) | Observações |
|---|---|---|---|---|
| `DATABASE_URL` | M2, M5, M10 | `**********************************/mastigador_db` | | |
| `RABBITMQ_HOST` | M1, M2, M3, M4, M8 | `rabbitmq` | | |
| `REDIS_HOST` | M4, M6, M7, M10 | `redis` | | |
| `OPENAI_API_KEY` | M4, M6, M10 | Chave API válida | | |
| `M2_API_URL` | M1, M3, M4 | `http://mastigador_m2_api:8082` | | |
| `M8_API_GATEWAY_URL` | M14 | `http://mastigador_m8_api_gateway:8080` | | |
| `M10_AI_ORCHESTRATOR_URL` | M14 | `http://mastigador_m10_ai_orchestrator:8010` | | |
| `ADMIN_TOKEN` | M5 | Token de segurança | | |
| ... (outras variáveis específicas de cada módulo) | | | | |

#### 1.2.3. Template de Validação de Health Check

| Módulo/Serviço | Endpoint Health Check | Resposta Esperada | Status (Pass/Fail) | Observações |
|---|---|---|---|---|
| **DB** | `pg_isready` (Docker Healthcheck) | `0` (saudável) | | |
| **RabbitMQ** | `rabbitmq-diagnostics -q ping` (Docker Healthcheck) | `0` (saudável) | | |
| **Redis** | `redis-cli ping` (Docker Healthcheck) | `PONG` | | |
| **M1: Drive Connector** | `/healthz` | `{"status": "ok"}` | | |
| **M2: Task Orchestrator** | `/healthz` | `{"status": "ok"}` | | |
| **M3: Extractor** | (Consumer, verificar logs) | Logs sem erros críticos | | |
| **M4: Translator** | `/healthz` | `{"status": "ok"}` | | |
| **M5: DB Browser** | `/healthz` | `{"status": "ok"}` | | |
| **M6: Vector Indexer** | `/healthz` | `{"status": "ok"}` | | |
| **M7: Diff Engine** | `/healthz` | `{"status": "ok"}` | | |
| **M8: API Gateway** | `/healthz` | `{"overall_backend_status": "healthy", "services": {...}}` | | |
| **M10: AI Orchestrator** | `/health` | `{"status": "ok"}` | | |
| **M14: Importek BFF** | `/healthz` | `{"status": "ok"}` | | |

#### 1.2.4. Template de Validação de Página Web

| Página Web | URL | Elementos Críticos (UI) | Funcionalidade Principal | Status (Pass/Fail) | Observações |
|---|---|---|---|---|
| **M10 Monitor UI** | `http://localhost:8010/monitor` | Gráficos, tabelas de logs, filtros | Exibição de métricas e logs do M10 | | |
| **M14 Dashboard** | `http://localhost:8090/` (SPA principal) | Componentes do dashboard (M2, M10) | Carregamento de dados, interatividade | | |
| **M7 Diff Viewer UI** | `http://localhost:8087/` | Interface de upload, visualização de diff | Comparação de documentos | | |
| **M5 DB Browser UI** | `http://localhost:8093/` | Interface de consulta, exibição de resultados | Navegação e consulta ao DB | | |
| ... (outras páginas web) | | | | |

---

## 2. Execução da Validação (Próximos Passos)

As próximas sub-tarefas se concentrarão na execução da validação para cada componente, atualizando o status e corrigindo problemas conforme necessário.
