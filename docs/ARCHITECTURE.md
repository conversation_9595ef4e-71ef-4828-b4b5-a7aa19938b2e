# 🏗️ Arquitetura - Mastigador de Dados

Documentação completa da arquitetura do sistema, padrões de comunicação e estratégias para integração de novos módulos.

## 🎯 Visão Geral da Arquitetura

O **Mastigador de Dados** segue uma arquitetura de **microserviços distribuídos** com **comunicação assíncrona** via message broker e **cache distribuído** para performance.

```mermaid
graph TB
    subgraph "Frontend/Client"
        CLI[CLI/Scripts]
        WEB[Web Interface]
        API_CLIENTS[API Clients]
    end
    
    subgraph "API Layer"
        M8[M8 API Gateway<br/>:8080]
    end
    
    subgraph "Core Services"
        M1[M1 Drive Connector<br/>:8081]
        M2[M2 Task Orchestrator<br/>:8082]
        M4[M4 Translator<br/>:8084]
        M5[M5 DB Browser<br/>:8093]
        M6[M6 Vector Indexer<br/>:8086]
        M7[M7 Diff Engine<br/>:8087]
    end
    
    subgraph "Infrastructure"
        POSTGRES[(PostgreSQL<br/>:5432)]
        REDIS[(Redis Cache<br/>:6379)]
        RABBITMQ{RabbitMQ<br/>:5672}
    end
    
    subgraph "External Services"
        GDRIVE[Google Drive API]
        OPENAI[OpenAI API]
    end
    
    CLI --> M8
    WEB --> M8
    API_CLIENTS --> M8
    
    M8 --> M1
    M8 --> M2
    M8 --> M4
    M8 --> M5
    M8 --> M6
    M8 --> M7
    
    M1 --> RABBITMQ
    M2 --> POSTGRES
    M2 --> RABBITMQ
    M4 --> POSTGRES
    M4 --> REDIS
    M4 --> RABBITMQ
    M5 --> POSTGRES
    M6 --> POSTGRES
    M7 --> POSTGRES
    M7 --> REDIS
    
    M1 --> GDRIVE
    M4 --> OPENAI
    
    style M8 fill:#e1f5fe
    style POSTGRES fill:#f3e5f5
    style REDIS fill:#fff3e0
    style RABBITMQ fill:#e8f5e8
```

## 🔄 Fluxo de Dados Principal

```mermaid
sequenceDiagram
    participant U as Cliente
    participant M8 as API Gateway
    participant M1 as Drive Connector
    participant MQ as RabbitMQ
    participant M2 as Task Orchestrator
    participant M3 as Extractor
    participant M4 as Translator
    participant M6 as Vector Indexer
    participant M7 as Diff Engine
    participant DB as PostgreSQL
    participant R as Redis
    
    U->>M8: POST /v1/import
    M8->>M1: POST /import
    M1->>MQ: publish: file_discovered
    M1->>M2: notify: tasks created
    
    M2->>MQ: consume: file_discovered
    M2->>DB: insert: task record
    M2->>MQ: publish: extract_task
    
    M3->>MQ: consume: extract_task
    M3->>DB: insert: extracted content
    M3->>MQ: publish: translate_task
    
    M4->>MQ: consume: translate_task
    M4->>R: check: translation cache
    alt Cache Miss
        M4->>OpenAI: translate content
        M4->>R: store: cached translation
    end
    M4->>DB: insert: translated content
    M4->>MQ: publish: index_task
    
    M6->>MQ: consume: index_task
    M6->>DB: insert: vector embeddings
    M6->>MQ: publish: diff_task
    
    M7->>MQ: consume: diff_task
    M7->>DB: query: previous versions
    M7->>R: store: diff results
    M7->>DB: insert: version comparison
    
    M2->>DB: update: task status = COMPLETED
    M8->>U: return: processing complete
```

## 📊 Padrões de Comunicação

### 1. **Comunicação Síncrona (HTTP/REST)**

#### API Gateway → Módulos
```python
# Padrão de proxy no M8
async def proxy_to_module(endpoint: str, method: str, data: dict = None):
    async with httpx.AsyncClient() as client:
        if method == "GET":
            response = await client.get(f"http://{module}:port{endpoint}")
        elif method == "POST":
            response = await client.post(
                f"http://{module}:port{endpoint}", 
                json=data
            )
        return response.json()
```

#### Health Checks
```python
# Padrão de health check (todos os módulos)
@app.get("/healthz")
async def health_check():
    return {
        "status": "healthy",
        "version": "1.0.0",
        "database": await check_database(),
        "redis": await check_redis(),
        "rabbitmq": await check_rabbitmq()
    }
```

### 2. **Comunicação Assíncrona (RabbitMQ)**

#### Estrutura de Exchanges e Queues
```
message_exchange (direct)
├── extraction_queue
├── translation_queue
├── indexing_queue
└── diff_queue
```

#### Padrão de Consumer
```python
# Template para consumer RabbitMQ
def process_message(ch, method, properties, body):
    try:
        data = json.loads(body)
        logger.info(f"Processando: {data}")
        
        # Processar dados
        result = process_data(data)
        
        # Atualizar banco de dados
        update_database(result)
        
        # Publicar próxima etapa (se necessário)
        publish_next_task(result)
        
        # Confirmar processamento
        ch.basic_ack(delivery_tag=method.delivery_tag)
        
    except Exception as e:
        logger.error(f"Erro: {e}")
        ch.basic_nack(delivery_tag=method.delivery_tag, requeue=True)
```

### 3. **Acesso a Dados (PostgreSQL)**

#### Padrão de Database Service
```python
# Template para serviço de banco
class DatabaseService:
    def __init__(self):
        self.connection_params = {
            "host": settings.DB_HOST,
            "port": settings.DB_PORT,
            "database": settings.DB_NAME,
            "user": settings.DB_USER,
            "password": settings.DB_PASSWORD
        }
    
    def execute_query(self, query: str, params: tuple = None):
        with psycopg2.connect(**self.connection_params) as conn:
            with conn.cursor(cursor_factory=extras.DictCursor) as cur:
                cur.execute(query, params)
                if query.strip().upper().startswith('SELECT'):
                    return cur.fetchall()
                conn.commit()
                return cur.rowcount
```

### 4. **Cache (Redis)**

#### Padrão de Cache Service
```python
# Template para serviço de cache
class CacheService:
    def __init__(self):
        self.redis_client = redis.Redis(
            host=settings.REDIS_HOST,
            port=settings.REDIS_PORT,
            decode_responses=True
        )
    
    def get_or_set(self, key: str, factory_func, expire: int = 3600):
        """Pattern: Cache-aside"""
        value = self.get(key)
        if value is None:
            value = factory_func()
            self.set(key, value, expire)
        return value
```

## 🗄️ Esquema de Banco de Dados

### Tabelas Principais
```sql
-- Documentos importados
CREATE TABLE documents (
    id UUID PRIMARY KEY,
    title VARCHAR(500),
    file_path TEXT,
    original_drive_id VARCHAR(255),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Versões dos documentos
CREATE TABLE document_versions (
    id UUID PRIMARY KEY,
    document_id UUID REFERENCES documents(id),
    version_number INTEGER,
    content TEXT,
    content_hash VARCHAR(64),
    created_at TIMESTAMP DEFAULT NOW()
);

-- Tarefas do orquestrador
CREATE TABLE tasks (
    id UUID PRIMARY KEY,
    module VARCHAR(50),
    status VARCHAR(20),
    payload JSONB,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    completed_at TIMESTAMP,
    last_error TEXT,
    retries INTEGER DEFAULT 0
);

-- Traduções em cache
CREATE TABLE translations (
    id UUID PRIMARY KEY,
    version_id UUID REFERENCES document_versions(id),
    source_language VARCHAR(10),
    target_language VARCHAR(10),
    original_text TEXT,
    translated_text TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Índices vetoriais
CREATE TABLE vector_embeddings (
    id UUID PRIMARY KEY,
    document_id UUID REFERENCES documents(id),
    chunk_index INTEGER,
    embedding VECTOR(1536),
    metadata JSONB,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Comparações de versões
CREATE TABLE version_comparisons (
    id UUID PRIMARY KEY,
    document_id UUID REFERENCES documents(id),
    version1_id UUID REFERENCES document_versions(id),
    version2_id UUID REFERENCES document_versions(id),
    diff_result JSONB,
    created_at TIMESTAMP DEFAULT NOW()
);
```

### Índices para Performance
```sql
-- Índices essenciais
CREATE INDEX idx_documents_drive_id ON documents(original_drive_id);
CREATE INDEX idx_document_versions_document_id ON document_versions(document_id);
CREATE INDEX idx_tasks_status ON tasks(status);
CREATE INDEX idx_tasks_module ON tasks(module);
CREATE INDEX idx_translations_version_id ON translations(version_id);
CREATE INDEX idx_vector_embeddings_document_id ON vector_embeddings(document_id);
CREATE INDEX idx_version_comparisons_document_id ON version_comparisons(document_id);
```

## 🔧 Padrões de Configuração

### 1. **Variáveis de Ambiente**
```python
# config.py padrão para todos os módulos
class Settings(BaseSettings):
    # Database
    DB_HOST: str = "postgres"
    DB_PORT: int = 5432
    DB_NAME: str = "mastigador"
    DB_USER: str = "postgres"
    DB_PASSWORD: str = "postgres"
    
    # Redis
    REDIS_HOST: str = "redis"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    
    # RabbitMQ
    RABBITMQ_HOST: str = "rabbitmq"
    RABBITMQ_PORT: int = 5672
    RABBITMQ_USER: str = "guest"
    RABBITMQ_PASS: str = "guest"
    
    # Módulo específico
    MODULE_NAME: str
    MODULE_PORT: int
    LOG_LEVEL: str = "INFO"
    
    class Config:
        env_file = ".env"
```

### 2. **Docker Compose Padrão**
```yaml
# Template para novo módulo
services:
  mx_novo_modulo:
    build: ./mx_novo_modulo
    container_name: mx_novo_modulo
    ports:
      - "808x:808x"
    environment:
      - DB_HOST=postgres
      - REDIS_HOST=redis
      - RABBITMQ_HOST=rabbitmq
      - DB_PASSWORD=${DB_PASSWORD}
      - MODULE_NAME=MX_NOVO_MODULO
      - MODULE_PORT=808x
    depends_on:
      - postgres
      - redis
      - rabbitmq
    networks:
      - mastigador_network
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:808x/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3
```

## 🔄 Estratégias de Integração

### 1. **Para Módulos Síncronos**
```python
# Integração via API Gateway (M8)
@app.post("/v1/novo-modulo/processar")
async def proxy_novo_modulo(dados: dict):
    """Proxy para novo módulo síncrono"""
    try:
        async with httpx.AsyncClient(timeout=30) as client:
            response = await client.post(
                "http://mx_novo_modulo:808x/processar",
                json=dados
            )
            response.raise_for_status()
            return response.json()
    except httpx.RequestError as e:
        raise HTTPException(status_code=503, detail=f"Módulo indisponível: {e}")
    except httpx.HTTPStatusError as e:
        raise HTTPException(status_code=e.response.status_code, detail=e.response.text)
```

### 2. **Para Módulos Assíncronos**
```python
# Integração via RabbitMQ
def setup_module_queues():
    """Configurar filas para novo módulo"""
    connection = pika.BlockingConnection(pika.ConnectionParameters('rabbitmq'))
    channel = connection.channel()
    
    # Declarar exchange
    channel.exchange_declare(
        exchange='message_exchange',
        exchange_type='direct',
        durable=True
    )
    
    # Declarar queue específica
    channel.queue_declare(queue='novo_modulo_queue', durable=True)
    
    # Bind queue ao exchange
    channel.queue_bind(
        exchange='message_exchange',
        queue='novo_modulo_queue',
        routing_key='novo_modulo'
    )
    
    connection.close()

# Publicar mensagem para novo módulo
def send_to_novo_modulo(data: dict):
    """Enviar dados para processamento no novo módulo"""
    connection = pika.BlockingConnection(pika.ConnectionParameters('rabbitmq'))
    channel = connection.channel()
    
    message = json.dumps(data)
    channel.basic_publish(
        exchange='message_exchange',
        routing_key='novo_modulo',
        body=message,
        properties=pika.BasicProperties(delivery_mode=2)  # Persistent
    )
    
    connection.close()
```

### 3. **Para Módulos com Estado**
```python
# Pattern para módulos que mantêm estado
class StatefulModule:
    def __init__(self):
        self.db_service = DatabaseService()
        self.cache_service = CacheService()
        self.state_key = f"module_state:{settings.MODULE_NAME}"
    
    def save_state(self, state_data: dict):
        """Salvar estado no Redis"""
        self.cache_service.set(self.state_key, state_data, expire=86400)
    
    def load_state(self) -> dict:
        """Carregar estado do Redis"""
        return self.cache_service.get(self.state_key) or {}
    
    def persist_to_db(self, data: dict):
        """Persistir dados importantes no banco"""
        query = """
        INSERT INTO module_states (module_name, state_data, created_at)
        VALUES (%s, %s, NOW())
        """
        self.db_service.execute_query(query, (settings.MODULE_NAME, json.dumps(data)))
```

## 📈 Padrões de Monitoramento

### 1. **Health Checks Hierárquicos**
```python
# Health check com dependências
@app.get("/healthz")
async def comprehensive_health_check():
    health_status = {
        "status": "healthy",
        "version": settings.API_VERSION,
        "dependencies": {}
    }
    
    # Verificar banco de dados
    try:
        db_service.execute_query("SELECT 1")
        health_status["dependencies"]["database"] = "healthy"
    except Exception as e:
        health_status["dependencies"]["database"] = f"unhealthy: {str(e)}"
        health_status["status"] = "degraded"
    
    # Verificar Redis
    try:
        cache_service.redis_client.ping()
        health_status["dependencies"]["redis"] = "healthy"
    except Exception as e:
        health_status["dependencies"]["redis"] = f"unhealthy: {str(e)}"
        health_status["status"] = "degraded"
    
    # Verificar RabbitMQ
    try:
        connection = pika.BlockingConnection(pika.ConnectionParameters('rabbitmq'))
        connection.close()
        health_status["dependencies"]["rabbitmq"] = "healthy"
    except Exception as e:
        health_status["dependencies"]["rabbitmq"] = f"unhealthy: {str(e)}"
        health_status["status"] = "degraded"
    
    return health_status
```

### 2. **Logging Estruturado**
```python
# Configuração de logging padrão
import logging
import json
import sys
from datetime import datetime

class StructuredLogger:
    def __init__(self, module_name: str):
        self.module_name = module_name
        self.logger = logging.getLogger(module_name)
        self.logger.setLevel(logging.INFO)
        
        handler = logging.StreamHandler(sys.stdout)
        handler.setFormatter(logging.Formatter('%(message)s'))
        self.logger.addHandler(handler)
    
    def log(self, level: str, message: str, **kwargs):
        log_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "level": level,
            "module": self.module_name,
            "message": message,
            **kwargs
        }
        self.logger.info(json.dumps(log_entry))
    
    def info(self, message: str, **kwargs):
        self.log("INFO", message, **kwargs)
    
    def error(self, message: str, error: Exception = None, **kwargs):
        if error:
            kwargs["error_type"] = type(error).__name__
            kwargs["error_message"] = str(error)
        self.log("ERROR", message, **kwargs)
```

### 3. **Métricas de Performance**
```python
# Decorator para medir performance
import time
import functools

def measure_time(operation_name: str):
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                duration = time.time() - start_time
                logger.info(
                    f"Operation completed",
                    operation=operation_name,
                    duration_seconds=duration,
                    status="success"
                )
                return result
            except Exception as e:
                duration = time.time() - start_time
                logger.error(
                    f"Operation failed",
                    operation=operation_name,
                    duration_seconds=duration,
                    status="error",
                    error=e
                )
                raise
        return wrapper
    return decorator

# Uso
@measure_time("document_processing")
async def process_document(document_id: str):
    # Lógica de processamento
    pass
```

## 🔒 Padrões de Segurança

### 1. **Validação de Entrada**
```python
# Modelos Pydantic para validação
from pydantic import BaseModel, validator
from typing import Optional
import uuid

class DocumentRequest(BaseModel):
    title: str
    content: str
    metadata: Optional[dict] = {}
    
    @validator('title')
    def title_must_not_be_empty(cls, v):
        if not v.strip():
            raise ValueError('Title cannot be empty')
        return v.strip()
    
    @validator('content')
    def content_must_not_be_empty(cls, v):
        if not v.strip():
            raise ValueError('Content cannot be empty')
        return v.strip()
    
    @validator('metadata')
    def validate_metadata(cls, v):
        if v and len(str(v)) > 10000:  # Limite de 10KB
            raise ValueError('Metadata too large')
        return v

class TaskUpdateRequest(BaseModel):
    status: str
    last_error: Optional[str] = None
    
    @validator('status')
    def validate_status(cls, v):
        valid_statuses = ['PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED']
        if v not in valid_statuses:
            raise ValueError(f'Invalid status. Must be one of: {valid_statuses}')
        return v
```

### 2. **Sanitização de Queries SQL**
```python
# Prevenção contra SQL injection
def safe_query_builder(table: str, filters: dict, allowed_columns: set):
    """Construir query SQL de forma segura"""
    # Validar nome da tabela
    if not table.isalnum():
        raise ValueError("Invalid table name")
    
    # Construir cláusulas WHERE
    where_clauses = []
    params = []
    
    for column, value in filters.items():
        if column not in allowed_columns:
            raise ValueError(f"Column {column} not allowed")
        
        if not column.replace('_', '').isalnum():
            raise ValueError(f"Invalid column name: {column}")
        
        where_clauses.append(f"{column} = %s")
        params.append(value)
    
    query = f"SELECT * FROM {table}"
    if where_clauses:
        query += " WHERE " + " AND ".join(where_clauses)
    
    return query, tuple(params)
```

### 3. **Rate Limiting**
```python
# Rate limiting básico com Redis
class RateLimiter:
    def __init__(self, redis_client):
        self.redis = redis_client
    
    def is_allowed(self, key: str, max_requests: int, window_seconds: int) -> bool:
        """Verificar se request está dentro do limite"""
        current = self.redis.get(key)
        
        if current is None:
            # Primeiro request na janela
            self.redis.setex(key, window_seconds, 1)
            return True
        
        if int(current) >= max_requests:
            return False
        
        # Incrementar contador
        self.redis.incr(key)
        return True

# Middleware FastAPI
@app.middleware("http")
async def rate_limit_middleware(request: Request, call_next):
    client_ip = request.client.host
    endpoint = str(request.url.path)
    key = f"rate_limit:{client_ip}:{endpoint}"
    
    if not rate_limiter.is_allowed(key, max_requests=100, window_seconds=60):
        return JSONResponse(
            status_code=429,
            content={"detail": "Rate limit exceeded"}
        )
    
    response = await call_next(request)
    return response
```

## 🚀 Estratégias de Deployment

### 1. **Deployment Blue-Green**
```yaml
# docker-compose.blue.yml
services:
  mx_novo_modulo_blue:
    image: mx_novo_modulo:latest
    container_name: mx_novo_modulo_blue
    ports:
      - "8089:8089"
    environment:
      - DEPLOYMENT_COLOR=blue

# docker-compose.green.yml  
services:
  mx_novo_modulo_green:
    image: mx_novo_modulo:latest
    container_name: mx_novo_modulo_green
    ports:
      - "8090:8089"
    environment:
      - DEPLOYMENT_COLOR=green
```

### 2. **Health Check para Load Balancer**
```python
# Endpoint específico para load balancer
@app.get("/health/live")
async def liveness_probe():
    """Probe para verificar se o container está vivo"""
    return {"status": "alive", "timestamp": datetime.utcnow().isoformat()}

@app.get("/health/ready")
async def readiness_probe():
    """Probe para verificar se o módulo está pronto para receber traffic"""
    # Verificar dependências críticas
    try:
        await check_critical_dependencies()
        return {"status": "ready", "timestamp": datetime.utcnow().isoformat()}
    except Exception as e:
        raise HTTPException(status_code=503, detail="Not ready")
```

### 3. **Graceful Shutdown**
```python
# Shutdown graceful
import signal
import asyncio

class GracefulShutdown:
    def __init__(self):
        self.shutdown_event = asyncio.Event()
        self.active_connections = set()
    
    def setup_signal_handlers(self):
        signal.signal(signal.SIGTERM, self.handle_shutdown)
        signal.signal(signal.SIGINT, self.handle_shutdown)
    
    def handle_shutdown(self, signum, frame):
        logger.info(f"Received signal {signum}, starting graceful shutdown")
        asyncio.create_task(self.shutdown())
    
    async def shutdown(self):
        # Parar de aceitar novas conexões
        self.shutdown_event.set()
        
        # Aguardar conexões ativas terminarem (máximo 30s)
        timeout = 30
        while self.active_connections and timeout > 0:
            await asyncio.sleep(1)
            timeout -= 1
        
        # Fechar recursos
        await self.cleanup_resources()
        
        logger.info("Graceful shutdown completed")
        sys.exit(0)
    
    async def cleanup_resources(self):
        # Fechar conexões com banco
        # Fechar conexões com Redis
        # Fechar conexões com RabbitMQ
        pass
```

---

**🎯 Esta arquitetura foi projetada para escalabilidade, manutenibilidade e facilidade de integração.** Os padrões estabelecidos garantem consistência entre módulos e facilitam a adição de novas funcionalidades.
