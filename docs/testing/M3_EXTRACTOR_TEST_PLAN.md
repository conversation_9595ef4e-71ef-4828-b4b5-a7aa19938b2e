# 📋 Plano de Testes Detalhado - M3 (Extractor)

**Módulo:** M3 - Extractor  
**Versão:** 1.0  
**Data:** 24/06/2025  
**Autor:** Kilo Code - Modo Architect  

## 🎯 Objetivos do Plano de Testes

1. **Garantir qualidade e confiabilidade** do processo de extração de conteúdo
2. **Validar integração** com RabbitMQ, PostgreSQL e API do M2
3. **Testar robustez** com diferentes tipos de arquivos e cenários de erro
4. **Verificar performance** e tratamento de casos extremos

## 🏗️ Arquitetura de Testes

```mermaid
graph TB
    subgraph "Testes Unitários (pytest)"
        A[Extração de Conteúdo]
        B[Detecção de Idioma]
        C[Chunking de Texto]
        D[Validação de Configurações]
    end
    
    subgraph "Testes de Integração (Docker)"
        E[RabbitMQ + Consumer]
        F[PostgreSQL + Persistência]
        G[M2 API + Status Updates]
        H[Pipeline Completo]
    end
    
    subgraph "Arquivos de Teste"
        I[PDFs Textuais]
        J[PDFs OCR]
        K[Excel Files]
        L[Texto Simples]
        M[Arquivos Corrompidos]
    end
    
    A --> I
    A --> J
    A --> K
    A --> L
    A --> M
    
    E --> H
    F --> H
    G --> H
```

## 📊 Estrutura de Testes Detalhada

### 1. **Consumo de Mensagens RabbitMQ**

#### **Testes Unitários:**

**Cenário 1.1:** Parsing de mensagem válida
- **Entrada:** JSON com formato correto do M2
- **Resultado Esperado:** Extração correta dos campos `job_id`, `module_to_process`, `data`
- **Validação:** Verificar se campos obrigatórios estão presentes
- **Método:** Mock do `json.loads()`, verificar valores retornados

**Cenário 1.2:** Mensagem com `module_to_process` diferente de "extractor"
- **Entrada:** JSON com `module_to_process: "translator"`
- **Resultado Esperado:** Mensagem ignorada, ACK enviado
- **Validação:** Log de "ignorando" deve ser gerado
- **Método:** Verificar chamada `ch.basic_ack()`, não processar

**Cenário 1.3:** Mensagem malformada
- **Entrada:** JSON inválido ou campos obrigatórios ausentes
- **Resultado Esperado:** NACK enviado, erro logado
- **Validação:** Verificar tratamento de exceção
- **Método:** `pytest.raises()` para capturar exceções

#### **Testes de Integração:**

**Cenário 1.4:** Consumo real da fila `tasks.queue`
- **Entrada:** Container RabbitMQ + mensagem publicada
- **Resultado Esperado:** Mensagem consumida e processada
- **Validação:** Verificar ACK/NACK via management UI do RabbitMQ
- **Método:** `testcontainers` + análise de logs

### 2. **Extração de Conteúdo**

#### **Testes Unitários - PDF Textual:**

**Cenário 2.1:** PDF com texto extraível via PDFMiner
- **Entrada:** PDF textual simples (`tests/fixtures/pdfs/texto_simples.pdf`)
- **Resultado Esperado:** Texto extraído corretamente
- **Validação:** `extraction_method == "pdfminer"`, conteúdo corresponde ao esperado
- **Método:** `extract_content_from_file()` com arquivo fixture

**Cenário 2.2:** PDF com placeholders (cid:)
- **Entrada:** PDF com muitos `(cid:)` placeholders
- **Resultado Esperado:** Fallback para OCR ativado
- **Validação:** Densidade de placeholders > 15% deve triggar OCR
- **Método:** Contar ocorrências de "(cid:", verificar method change

#### **Testes Unitários - PDF OCR:**

**Cenário 2.3:** PDF baseado em imagem
- **Entrada:** PDF escaneado (`tests/fixtures/pdfs/imagem_escaneada.pdf`)
- **Resultado Esperado:** OCR ativado automaticamente
- **Validação:** `extraction_method == "tesseract_ocr"`, texto OCR presente
- **Método:** Mock do `pytesseract.image_to_string()`

**Cenário 2.4:** PDF multilíngue
- **Entrada:** PDF com português, inglês e chinês
- **Resultado Esperado:** OCR com múltiplos idiomas
- **Validação:** Parâmetro `lang='eng+por+chi_sim'` usado no Tesseract
- **Método:** Verificar argumentos passados para `pytesseract`

#### **Testes Unitários - Excel:**

**Cenário 2.5:** Arquivo Excel com múltiplas planilhas
- **Entrada:** `.xlsx` com 3 planilhas (`tests/fixtures/excel/planilhas_multiplas.xlsx`)
- **Resultado Esperado:** Conteúdo de todas as planilhas extraído
- **Validação:** `extraction_method == "openpyxl"`, separadores de planilha presentes
- **Método:** Verificar presença de "--- Planilha:" no texto

**Cenário 2.6:** Excel com células vazias e fórmulas
- **Entrada:** Excel com células vazias, fórmulas e valores
- **Resultado Esperado:** Apenas valores finais extraídos
- **Validação:** Usar `data_only=True` no openpyxl
- **Método:** Mock `load_workbook()`, verificar parâmetros

#### **Testes Unitários - Texto Genérico:**

**Cenário 2.7:** Arquivo de texto UTF-8
- **Entrada:** `.txt` com encoding UTF-8
- **Resultado Esperado:** Conteúdo lido corretamente
- **Validação:** `extraction_method == "text_file"`
- **Método:** Arquivo fixture com caracteres especiais

**Cenário 2.8:** Arquivo de texto Latin-1
- **Entrada:** `.txt` com encoding Latin-1
- **Resultado Esperado:** Fallback para Latin-1 funciona
- **Validação:** `extraction_method == "text_file_latin1"`
- **Método:** Forçar `UnicodeDecodeError`, verificar fallback

#### **Testes Unitários - Casos de Borda:**

**Cenário 2.9:** Arquivo inexistente
- **Entrada:** Caminho de arquivo inválido
- **Resultado Esperado:** `FileNotFoundError` lançada
- **Validação:** Exceção capturada e logada
- **Método:** `pytest.raises(FileNotFoundError)`

**Cenário 2.10:** Arquivo corrompido
- **Entrada:** PDF/Excel corrompido
- **Resultado Esperado:** Erro tratado graciosamente
- **Validação:** `extraction_method == "error"`, mensagem de erro no conteúdo
- **Método:** Arquivo fixture corrompido

### 3. **Detecção de Idioma**

#### **Testes Unitários:**

**Cenário 3.1:** Texto em português
- **Entrada:** Texto longo em português (500+ caracteres)
- **Resultado Esperado:** `detected_language == "pt-BR"`
- **Validação:** Função `detect_language()` retorna código correto
- **Método:** Texto fixture em português, verificar retorno

**Cenário 3.2:** Texto em inglês
- **Entrada:** Texto longo em inglês (500+ caracteres)
- **Resultado Esperado:** `detected_language == "en-US"`
- **Validação:** Mapeamento de códigos funciona
- **Método:** Texto fixture em inglês, verificar mapeamento

**Cenário 3.3:** Texto muito curto
- **Entrada:** String com menos de 50 caracteres
- **Resultado Esperado:** `detected_language == "unknown"`
- **Validação:** Threshold de tamanho mínimo respeitado
- **Método:** String de 30 caracteres, verificar early return

**Cenário 3.4:** Texto inválido/numérico
- **Entrada:** String apenas com números e símbolos
- **Resultado Esperado:** `detected_language == "unknown"`
- **Validação:** Exceção capturada, fallback para "unknown"
- **Método:** String "123456789!@#$%", verificar exception handling

### 4. **Chunking de Texto**

#### **Testes Unitários:**

**Cenário 4.1:** Texto com quebras de página
- **Entrada:** Texto com separadores `"--- Page Break ---"`
- **Resultado Esperado:** Chunks separados por página
- **Validação:** `chunk_method == "page_break"`, metadados corretos
- **Método:** Função `create_chunks()`, verificar metadados

**Cenário 4.2:** Texto longo sem quebras de página
- **Entrada:** Texto > 1000 caracteres contínuo
- **Resultado Esperado:** Divisão por fronteiras de palavras
- **Validação:** `chunk_method == "word_boundary"`, tamanho ≤ 1000
- **Método:** String de 2500 caracteres, verificar divisão

**Cenário 4.3:** Texto curto (< 1000 caracteres)
- **Entrada:** Texto de 500 caracteres
- **Resultado Esperado:** Um único chunk
- **Validação:** Apenas 1 chunk gerado, conteúdo íntegro
- **Método:** String de 500 caracteres, `len(chunks) == 1`

**Cenário 4.4:** Texto vazio
- **Entrada:** String vazia ou apenas espaços
- **Resultado Esperado:** Nenhum chunk gerado
- **Validação:** Lista vazia retornada
- **Método:** String vazia e "   ", verificar lista vazia

### 5. **Persistência no Banco de Dados**

#### **Testes de Integração:**

**Cenário 5.1:** Inserção de novo documento
- **Entrada:** Chunks de documento inexistente
- **Resultado Esperado:** Registros criados em `documents`, `versions`, `chunks_parent`
- **Validação:** Consultar tabelas no PostgreSQL, verificar UUIDs gerados
- **Método:** `testcontainers` PostgreSQL + queries de verificação

**Cenário 5.2:** Atualização de documento existente
- **Entrada:** Chunks de documento já existente
- **Resultado Esperado:** Versão atualizada, chunks substituídos
- **Validação:** `ON CONFLICT` funciona, `version_id` retornado corretamente
- **Método:** Inserir documento, depois inserir novamente com mesmos dados

**Cenário 5.3:** Falha de conexão com banco
- **Entrada:** PostgreSQL indisponível
- **Resultado Esperado:** `db_version_id == None`, erro logado
- **Validação:** Rollback executado, exceção tratada
- **Método:** Parar container PostgreSQL, verificar erro handling

**Cenário 5.4:** Chunks com metadados complexos
- **Entrada:** Chunks com metadados JSON extensos
- **Resultado Esperado:** Metadados serializados corretamente
- **Validação:** JSON válido armazenado na coluna metadata
- **Método:** Metadados com arrays/objetos aninhados, verificar serialização

### 6. **Publicação de Resultados RabbitMQ**

#### **Testes de Integração:**

**Cenário 6.1:** Publicação de múltiplos chunks
- **Entrada:** Lista com 5 chunks processados
- **Resultado Esperado:** 5 mensagens na fila `extraction.results`
- **Validação:** Contar mensagens na fila, verificar formato JSON
- **Método:** RabbitMQ container + management API para contar mensagens

**Cenário 6.2:** Formato da mensagem para M4
- **Entrada:** Chunk com metadados específicos
- **Resultado Esperado:** JSON com campos `chunk_id`, `content`, `version_id`, etc.
- **Validação:** Schema da mensagem conforme esperado pelo M4
- **Método:** Consumir mensagem da fila, validar schema JSON

**Cenário 6.3:** Falha de conexão RabbitMQ
- **Entrada:** RabbitMQ indisponível durante publicação
- **Resultado Esperado:** Exceção capturada, erro logado
- **Validação:** Processo não falha, log de erro gerado
- **Método:** Parar container RabbitMQ, verificar error handling

### 7. **Atualização de Status no M2**

#### **Testes de Integração:**

**Cenário 7.1:** Atualização para "PROCESSING"
- **Entrada:** Início do processamento de job
- **Resultado Esperado:** `PUT /tasks/{job_id}/status` com `{"status": "PROCESSING"}`
- **Validação:** Mock server M2 recebe requisição correta
- **Método:** `httpx` mock server, interceptar requests

**Cenário 7.2:** Atualização para "COMPLETED"
- **Entrada:** Processamento bem-sucedido
- **Resultado Esperado:** `PUT /tasks/{job_id}/status` with `{"status": "COMPLETED"}`
- **Validação:** Status code 200, job marcado como concluído
- **Método:** Mock server com resposta 200, verificar payload

**Cenário 7.3:** Atualização para "FAILED"
- **Entrada:** Erro durante processamento
- **Resultado Esperado:** `PUT /tasks/{job_id}/status` com `{"status": "FAILED", "last_error": "..."}`
- **Validação:** Mensagem de erro incluída no payload
- **Método:** Forçar erro, verificar payload com error message

**Cenário 7.4:** API M2 indisponível
- **Entrada:** M2 fora do ar durante atualização
- **Resultado Esperado:** Warning logado, processamento continua
- **Validação:** Timeout respeitado, erro não interrompe fluxo
- **Método:** Mock server com timeout, verificar graceful handling

### 8. **Configurações (Variáveis de Ambiente)**

#### **Testes Unitários:**

**Cenário 8.1:** Configurações válidas completas
- **Entrada:** Todas as variáveis de ambiente definidas
- **Resultado Esperado:** `Config()` carregada corretamente
- **Validação:** Valores default e personalizados aplicados
- **Método:** `monkeypatch` para definir envvars, verificar `Config()`

**Cenário 8.2:** Configurações com defaults
- **Entrada:** Apenas variáveis obrigatórias definidas
- **Resultado Esperado:** Valores default aplicados (`MAX_CHUNK_SIZE=1000`, etc.)
- **Validação:** Constantes corretas em `config.py`
- **Método:** Limpar envvars opcionais, verificar defaults

**Cenário 8.3:** Configurações inválidas/ausentes
- **Entrada:** Variáveis críticas ausentes
- **Resultado Esperado:** Erro na inicialização ou fallback
- **Validação:** Exceção ou warning apropriado
- **Método:** Remover envvars obrigatórios, verificar erro

## 🛠️ Ferramentas e Métodos de Validação

```mermaid
graph LR
    subgraph "Validação de Dados"
        A[PostgreSQL Queries]
        B[RabbitMQ Management UI]
        C[Mock Server M2]
        D[Logs Analysis]
    end
    
    subgraph "Ferramentas de Teste"
        E[pytest]
        F[pytest-asyncio]
        G[Docker Compose]
        H[testcontainers-python]
    end
    
    subgraph "Arquivos de Teste"
        I[PDFs Sample]
        J[Excel Sample]
        K[Text Files]
        L[Corrupted Files]
    end
    
    E --> A
    E --> B
    G --> C
    H --> A
    H --> B
```

### **Ferramentas Específicas:**

#### 1. **Banco de Dados:**
- **psycopg2** para consultas diretas ao PostgreSQL
- **Queries de validação** para verificar dados inseridos
- **testcontainers** para containers temporários de teste

#### 2. **RabbitMQ:**
- **pika** para interação direta com filas
- **Management API** para contar mensagens nas filas
- **wiremock/httpx** para simular falhas de conexão

#### 3. **M2 API:**
- **httpx** com mock server para simular respostas
- **pytest-httpx** para interceptar e validar requests
- **Logs** para verificar tentativas de atualização

#### 4. **Arquivos de Teste:**
- **PDFs textuais e baseados em imagem** em `tests/fixtures/`
- **Arquivos Excel** com diferentes layouts
- **Arquivos corrompidos** para testes de robustez

## 📁 Estrutura de Arquivos Proposta

```
tests/
├── unit/
│   ├── test_extractor_service.py          # Testes das funções de extração
│   ├── test_chunking.py                   # Testes de divisão de texto
│   ├── test_language_detection.py         # Testes de detecção de idioma
│   └── test_config.py                     # Testes de configuração
├── integration/
│   ├── test_consumer_rabbitmq.py          # Testes de consumo RabbitMQ
│   ├── test_database_persistence.py       # Testes de persistência
│   ├── test_m2_api_integration.py         # Testes de integração M2
│   └── test_end_to_end_pipeline.py        # Testes completos do pipeline
├── fixtures/
│   ├── pdfs/
│   │   ├── texto_simples.pdf              # PDF textual
│   │   ├── imagem_escaneada.pdf           # PDF para OCR
│   │   ├── multilingue.pdf                # PDF com múltiplos idiomas
│   │   └── corrompido.pdf                 # PDF corrompido
│   ├── excel/
│   │   ├── planilhas_multiplas.xlsx       # Excel com múltiplas abas
│   │   ├── formulas_valores.xlsx          # Excel com fórmulas
│   │   └── corrompido.xlsx                # Excel corrompido
│   └── text/
│       ├── utf8_sample.txt                # Texto UTF-8
│       ├── latin1_sample.txt              # Texto Latin-1
│       └── mixed_encoding.txt             # Encoding misto
├── docker/
│   ├── docker-compose.test.yml            # Ambiente de teste isolado
│   ├── test_rabbitmq.conf                 # Configuração RabbitMQ teste
│   └── test_postgres.env                  # Variáveis PostgreSQL teste
└── conftest.py                            # Configurações pytest e fixtures
```

## 🚀 Dependências de Teste

```python
# requirements-test.txt
pytest>=7.0.0
pytest-asyncio>=0.21.0
pytest-mock>=3.10.0
pytest-httpx>=0.21.0
testcontainers>=3.7.0
docker>=6.0.0
psycopg2-binary>=2.9.0
pika>=1.3.0
requests>=2.28.0
```

## 🔧 Configuração Inicial

### Docker Compose para Testes:

```yaml
# tests/docker/docker-compose.test.yml
version: '3.8'
services:
  test-postgres:
    image: postgres:14
    environment:
      - POSTGRES_DB=test_db
      - POSTGRES_USER=test_user
      - POSTGRES_PASSWORD=test_pass
    ports:
      - "5433:5432"
    
  test-rabbitmq:
    image: rabbitmq:3-management
    environment:
      - RABBITMQ_DEFAULT_USER=test_user
      - RABBITMQ_DEFAULT_PASS=test_pass
    ports:
      - "5673:5672"
      - "15673:15672"
```

### Pytest Configuration:

```ini
# pytest.ini
[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
markers =
    unit: marks tests as unit tests
    integration: marks tests as integration tests
    slow: marks tests as slow running
```

## 📝 Comandos de Execução

```bash
# Executar todos os testes
pytest

# Executar apenas testes unitários
pytest -m unit

# Executar apenas testes de integração
pytest -m integration

# Executar com cobertura
pytest --cov=m3_extractor --cov-report=html

# Executar testes específicos
pytest tests/unit/test_extractor_service.py::test_pdf_textual_extraction

# Executar com logs verbose
pytest -v -s
```

## 🎯 Critérios de Sucesso

### **Cobertura de Código:**
- **Mínimo:** 85% de cobertura geral
- **Crítico:** 95% cobertura em funções core (extração, chunking)

### **Performance:**
- Extração de PDF de 10 páginas: < 30 segundos
- Processamento de mensagem completa: < 60 segundos
- Inserção de 50 chunks no DB: < 5 segundos

### **Confiabilidade:**
- 100% dos casos de erro tratados graciosamente
- Nenhum teste deve falhar de forma intermitente
- Rollback de transações em 100% dos casos de erro

---

**Status:** ✅ Aprovado  
**Próxima Etapa:** Implementação dos testes seguindo este plano  
**Responsável:** Equipe de Desenvolvimento  
**Prazo Estimado:** 3-5 dias úteis