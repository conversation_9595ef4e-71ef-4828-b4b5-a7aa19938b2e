# Comunicação Entre Serviços do Projeto

Este documento descreve como os vários serviços do projeto se comunicam entre si, incluindo os endpoints de health check e as URLs para comunicação interna e externa.

## Endpoints de Health Check

Cada serviço do projeto expõe um endpoint de health check que pode ser usado para verificar o status do serviço:

| Serviço | Container | Porta Interna | Porta Externa | Health Check Endpoint | Formato de Resposta |
|---------|-----------|---------------|---------------|----------------------|---------------------|
| M1 Drive Connector | mastigador_m1_drive | 8081 | 8081 | /healthz | `{"status":"ok","drive_service_status":"ok"}` |
| M2 Task Orchestrator | mastigador_m2_api | 8082 | 8082 | /healthz | `{"status":"healthy","database":"healthy","rabbitmq":"healthy"}` |
| M4 Translator | mastigador_m4_api | 8084 | 8084 | /healthz | `{"status":"healthy","components":{...}}` |
| M5 DB Browser | mastigador_m5_db_browser | 8093 | 8093 | /healthz | `{"status":"ok","database":"ok"}` |
| M6 Vector Indexer | mastigador_m6_indexer | 8086 | 8086 | /healthz | `{"db":"ok","openai":"ok","m6_status":"ok"}` |
| M7 Diff Engine | mastigador_m7_diff | 8087 | 8087 | /healthz | `{"db":"ok","m7_status":"ok"}` |
| M8 API Gateway | mastigador_m8_api_gateway | 8080 | 8088 | /healthz, /health | `{"gateway_status":"ok","overall_backend_status":"ok","services":{...}}` |
| M10 AI Orchestrator | mastigador_m10_ai_orchestrator | 8010 | 8010 | /health | `{"status":"healthy","service":"m10_ai_orchestrator","dependencies":{...}}` |
| M14 Importek BFF | mastigador_m14_bff | 8090 | 8090 | /healthz | `{"status":"ok","module":"m14_importek_bff"}` |

## URLs para Comunicação entre Serviços

### URLs para Comunicação Interna (Docker Network)

Estes são os URLs que os serviços devem usar para se comunicar entre si dentro da rede Docker:

| Serviço | URL para Comunicação Interna |
|---------|------------------------------|
| M1 Drive Connector | http://mastigador_m1_drive:8081 |
| M2 Task Orchestrator | http://mastigador_m2_api:8082 |
| M4 Translator | http://mastigador_m4_api:8084 |
| M5 DB Browser | http://mastigador_m5_db_browser:8093 |
| M6 Vector Indexer | http://mastigador_m6_indexer:8086 |
| M7 Diff Engine | http://mastigador_m7_diff:8087 |
| M8 API Gateway | http://mastigador_m8_api_gateway:8080 |
| M10 AI Orchestrator | http://mastigador_m10_ai_orchestrator:8010 |
| M14 Importek BFF | http://mastigador_m14_bff:8090 |

### URLs para Acesso Externo

Estes são os URLs que podem ser usados para acessar os serviços de fora dos containers (ex: a partir do host):

| Serviço | URL para Acesso Externo |
|---------|-------------------------|
| M1 Drive Connector | http://localhost:8081 |
| M2 Task Orchestrator | http://localhost:8082 |
| M4 Translator | http://localhost:8084 |
| M5 DB Browser | http://localhost:8093 |
| M6 Vector Indexer | http://localhost:8086 |
| M7 Diff Engine | http://localhost:8087 |
| M8 API Gateway | http://localhost:8088 |
| M10 AI Orchestrator | http://localhost:8010 |
| M14 Importek BFF | http://localhost:8090 |

## Variáveis de Ambiente para Comunicação entre Serviços

Para garantir a comunicação correta entre os serviços, as seguintes variáveis de ambiente devem ser configuradas no arquivo `.env`:

```
# URLs dos serviços (para comunicação interna entre containers)
M1_DRIVE_CONNECTOR_URL=http://mastigador_m1_drive:8081
M2_TASK_ORCHESTRATOR_URL=http://mastigador_m2_api:8082
M2_API_URL=http://mastigador_m2_api:8082
M4_TRANSLATOR_URL=http://mastigador_m4_api:8084
M4_API_URL=http://mastigador_m4_api:8084
M5_DB_BROWSER_URL=http://mastigador_m5_db_browser:8093
M6_VECTOR_INDEXER_URL=http://mastigador_m6_indexer:8086
M7_DIFF_ENGINE_URL=http://mastigador_m7_diff:8087
M8_API_GATEWAY_URL=http://mastigador_m8_api_gateway:8080
M10_AI_ORCHESTRATOR_URL=http://mastigador_m10_ai_orchestrator:8010
M14_IMPORTEK_BFF_URL=http://mastigador_m14_bff:8090

# URLs dos serviços (para acesso externo)
EXTERNAL_M1_DRIVE_CONNECTOR_URL=http://localhost:8081
EXTERNAL_M2_TASK_ORCHESTRATOR_URL=http://localhost:8082
EXTERNAL_M4_TRANSLATOR_URL=http://localhost:8084
EXTERNAL_M5_DB_BROWSER_URL=http://localhost:8093
EXTERNAL_M6_VECTOR_INDEXER_URL=http://localhost:8086
EXTERNAL_M7_DIFF_ENGINE_URL=http://localhost:8087
EXTERNAL_M8_API_GATEWAY_URL=http://localhost:8088
EXTERNAL_M10_AI_ORCHESTRATOR_URL=http://localhost:8010
EXTERNAL_M14_IMPORTEK_BFF_URL=http://localhost:8090
```

## Solução de Problemas de Comunicação

Se houver problemas de comunicação entre os serviços, verifique:

1. **Nomes dos Contêineres**: Certifique-se de que os nomes dos contêineres no arquivo `.env` correspondam aos nomes definidos no `docker-compose.yml`.
2. **Portas Expostas**: Verifique se as portas estão corretamente expostas no `docker-compose.yml`.
3. **Status dos Serviços**: Use `docker-compose ps` para verificar se todos os serviços estão em execução.
4. **Logs**: Use `docker-compose logs <serviço>` para verificar os logs de um serviço específico e procurar por erros.
5. **Teste de Conectividade**: Use comandos como o exemplo abaixo para testar a conectividade entre os serviços:

```python
# Exemplo de teste de conectividade usando Python dentro do contêiner
docker exec -it <container_name> python -c "import requests; print(requests.get('<service_url>/healthz').json())"

# Exemplo específico
docker exec -it mastigador_m8_api_gateway python -c "import requests; print(requests.get('http://mastigador_m1_drive:8081/healthz').json())"
```

## Exemplo de Verificação de Health Check Agregado

O M8 API Gateway fornece um endpoint de health check agregado em `/healthz` que verifica o status de todos os serviços. Use este endpoint para verificar rapidamente o status de todo o sistema:

```
curl http://localhost:8088/healthz
```

Resposta exemplo:
```json
{
    "gateway_status": "ok",
    "overall_backend_status": "ok",
    "services": {
        "m7_diff_engine": {
            "db": "ok",
            "m7_status": "ok"
        },
        "m2_task_orchestrator": {
            "status": "healthy",
            "version": "1.0.0",
            "database": "healthy",
            "rabbitmq": "healthy"
        },
        "m5_db_browser": {
            "status": "ok",
            "database": "ok"
        },
        "m1_drive_connector": {
            "status": "ok",
            "drive_service_status": "ok"
        },
        "m6_vector_indexer": {
            "db": "ok",
            "openai": "ok",
            "m6_status": "ok"
        }
    }
}
