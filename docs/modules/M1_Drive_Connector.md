# M1: Drive Connector

## 1. Propósito do Módulo

O Módulo 1 (M1) - Drive Connector é o ponto de entrada de dados no sistema "Mastigador de Dados em Lote". Sua principal responsabilidade é conectar-se a uma pasta específica do Google Drive, listar seu conteúdo (incluindo subpastas até uma profundidade configurável), baixar os arquivos encontrados para um armazenamento local temporário e, para cada arquivo baixado, publicar uma mensagem no RabbitMQ para que os próximos módulos do pipeline possam processá-lo.

## 2. Funcionalidades Chave

-   **Autenticação OAuth2 com Google Drive:** Utiliza credenciais OAuth2 (Client ID, Client Secret, Refresh Token) para se autenticar de forma segura com a API do Google Drive.
-   **Listagem de Conteúdo de Pasta:** Capacidade de listar arquivos e subpastas dentro de uma pasta do Google Drive especificada por seu ID.
-   **Listagem Recursiva de Arquivos:** Navega recursivamente pelas subpastas (até uma profundidade máxima definida) para encontrar todos os arquivos. Mantém o caminho relativo de cada arquivo dentro da estrutura da pasta importada.
-   **Download de Arquivos:** Baixa os arquivos identificados do Google Drive para um diretório local (`data/raw/TASK_ID/RELATIVE_PATH/`). Utiliza streaming para downloads eficientes.
-   **Cálculo de Checksum:** Calcula o checksum SHA256 de cada arquivo baixado para garantir a integridade dos dados.
-   **Publicação no RabbitMQ:** Para cada arquivo baixado com sucesso, publica uma mensagem no tópico `drive.ingest` do RabbitMQ. A mensagem contém metadados do arquivo, incluindo o ID da tarefa de importação, ID do arquivo no Drive, nome original, caminho relativo, tipo MIME, tamanho, caminho local do download e o checksum SHA256.
-   **API RESTful:** Expõe endpoints para iniciar o processo de importação, listar o conteúdo de uma pasta do Drive e verificar a saúde do serviço.
-   **Interface Web de Teste:** Fornece uma UI simples (em `/ui/index.html`) para facilitar testes manuais de importação de pastas.
-   **Modo Mock:** Inclui um `MockDriveService` para testes e desenvolvimento sem a necessidade de credenciais reais do Google Drive, simulando a listagem e o download de arquivos.

## 3. Entradas e Saídas

-   **Entradas:**
    -   **Requisição API (`POST /import`):** Recebe um `drive_folder_id` no corpo da requisição para iniciar o processo de importação.
    -   **Requisição API (`GET /browse/{folder_id}`):** Recebe um `folder_id` como parâmetro de caminho para listar seu conteúdo.
    -   **Google Drive:** Acessa arquivos e metadados do Google Drive usando a API.
    -   **Variáveis de Ambiente:** Credenciais do Google, configurações do RabbitMQ.

-   **Saídas:**
    -   **Arquivos Locais:** Salva os arquivos baixados no diretório `data/raw/{task_id}/{caminho_relativo_do_arquivo}` dentro do volume do contêiner (que é mapeado para o host).
    -   **Mensagens no RabbitMQ:** Publica mensagens JSON no tópico `drive.ingest`. Cada mensagem contém:
        ```json
        {
            "task_id": "string (UUID da tarefa de importação)",
            "drive_file_id": "string (ID do arquivo no Google Drive)",
            "file_name": "string (Nome original do arquivo)",
            "relative_path": "string (Caminho relativo do arquivo dentro da pasta importada)",
            "mime_type": "string",
            "size_bytes": "integer (opcional)",
            "download_path": "string (Caminho do arquivo baixado no sistema, ex: data/raw/task_id/...)",
            "checksum_sha256": "string (SHA256 do arquivo baixado)"
        }
        ```
    -   **Respostas API (JSON):** Para os endpoints `/import`, `/browse`, `/healthz`.

## 4. Principais Componentes Internos

-   `app/main.py`: Aplicação FastAPI, define os endpoints da API (`/import`, `/browse/{folder_id}`, `/healthz`, `/ui`).
-   `app/drive_service.py`: Contém a classe `DriveService` (e `MockDriveService`) com a lógica principal para interagir com a API do Google Drive (listar, baixar arquivos) e orquestrar o processo de importação, incluindo a publicação no RabbitMQ.
-   `app/auth.py`: Lida com a autenticação OAuth2 com o Google Drive e o gerenciamento de credenciais/tokens.
-   `app/config.py`: Carrega e fornece as configurações do módulo a partir de variáveis de ambiente (credenciais do Google, RabbitMQ, etc.).
-   `app/message_queue.py`: Contém a função `publish_to_drive_ingest` para publicar mensagens no RabbitMQ.
-   `static/`: Diretório contendo a UI de teste (`index.html`, `m2_monitor.html`).

## 5. Endpoints da API

A API do M1 é acessível em `http://localhost:8081` (quando rodando localmente via Docker Compose).
A documentação interativa da API (Swagger UI) está disponível em `http://localhost:8081/docs`.

### 5.1. Iniciar Importação de Pasta do Drive

-   **Endpoint:** `POST /import`
-   **Descrição:** Dispara o processo de listagem recursiva, download e enfileiramento de arquivos de uma pasta do Google Drive.
-   **Corpo da Requisição (JSON):**
    ```json
    {
        "drive_folder_id": "ID_DA_PASTA_NO_GOOGLE_DRIVE"
    }
    ```
-   **Resposta de Sucesso (200 OK):**
    ```json
    {
        "task_id": "string (UUID gerado para esta tarefa de importação)",
        "files_enqueued": "integer (Número de arquivos identificados para processamento)"
    }
    ```
-   **Exemplo `curl`:**
    ```bash
    curl -X POST -H "Content-Type: application/json" \
    -d '{"drive_folder_id": "SEU_GOOGLE_DRIVE_FOLDER_ID"}' \
    http://localhost:8081/import
    ```

### 5.2. Navegar em Pasta do Drive

-   **Endpoint:** `GET /browse/{folder_id}`
-   **Descrição:** Lista o conteúdo (arquivos e subpastas) de uma pasta específica do Google Drive.
-   **Parâmetros de Caminho:**
    -   `folder_id`: O ID da pasta do Google Drive a ser listada.
-   **Resposta de Sucesso (200 OK):**
    ```json
    [
        {
            "id": "string",
            "name": "string",
            "mimeType": "string",
            "sizeBytes": "integer | null"
        },
        // ... mais arquivos/pastas
    ]
    ```
-   **Exemplo `curl`:**
    ```bash
    curl http://localhost:8081/browse/ID_DA_PASTA_NO_GOOGLE_DRIVE
    ```

### 5.3. Health Check

-   **Endpoint:** `GET /healthz`
-   **Descrição:** Verifica a saúde do serviço M1 e sua capacidade de se conectar ao Google Drive.
-   **Resposta (200 OK):**
    ```json
    {
        "status": "ok",
        "drive_service_status": "error",
        "drive_service_message": "'NoneType' object has no attribute 'about'"
    }
    ```
    (O status geral é 'ok', mas o serviço do Google Drive está com erro. Este erro específico ocorre porque o módulo está operando em modo mock (`MockDriveService`) devido à falta de credenciais reais do Google Drive. O health check tenta acessar o método `about()` no atributo `service` do `MockDriveService`, mas este atributo é `None`, resultando no erro.)
-   **Exemplo `curl`:**
    ```bash
    curl http://localhost:8081/healthz
    ```

### 5.4. Interface de Teste Web

-   **Endpoint:** `GET /ui` ou `GET /ui/index.html`
-   **Descrição:** Serve uma página HTML simples para testar a funcionalidade de importação de pastas.
-   **Acesso:** Abra `http://localhost:8081/ui/index.html` em um navegador.

## 6. Configurações Importantes

As seguintes variáveis de ambiente devem ser configuradas (geralmente em um arquivo `.env` na raiz do projeto):

-   `GOOGLE_CLIENT_ID`: Client ID da sua aplicação no Google Cloud Console.
-   `GOOGLE_CLIENT_SECRET`: Client Secret da sua aplicação.
-   `GOOGLE_REFRESH_TOKEN`: Refresh Token obtido através do fluxo OAuth2.
-   `TOKEN_STORAGE_PATH`: Caminho para o arquivo onde o token de acesso será armazenado (ex: `token.json`).
-   `RABBITMQ_HOST`: Host do servidor RabbitMQ (ex: `rabbitmq` se rodando via Docker Compose, `localhost` se local).
-   `RABBITMQ_PORT`: Porta do RabbitMQ (padrão: `5672`).
-   `RABBITMQ_USER`: Usuário do RabbitMQ.
-   `RABBITMQ_PASS`: Senha do RabbitMQ.

O `GOOGLE_REDIRECT_URI` também é configurado mas é mais relevante para o processo inicial de obtenção do refresh token.

> **Nota Importante**: As três primeiras variáveis (`GOOGLE_CLIENT_ID`, `GOOGLE_CLIENT_SECRET`, `GOOGLE_REFRESH_TOKEN`) são essenciais para o funcionamento real do módulo com o Google Drive. Sem elas configuradas corretamente, o módulo opera em modo mock, usando dados simulados em vez de se conectar ao Google Drive real. Veja a seção "Status Atual" para mais detalhes.

## 7. Status Atual

Atualmente, o módulo M1 Drive Connector está operando em **modo mock** devido à falta de configuração das credenciais reais do Google Drive. Isso significa que:

- As operações de browse e import estão funcionando com dados simulados através do `MockDriveService`.
- O endpoint `/healthz` retorna um erro (`'NoneType' object has no attribute 'about'`) porque tenta acessar o serviço real do Google Drive, mas o módulo está usando o `MockDriveService`.
- Os usuários percebem o M1 como funcional porque as operações de browse/import usam o `MockDriveService` que retorna dados simulados.

Para habilitar a funcionalidade completa com o Google Drive real, é necessário configurar as seguintes variáveis de ambiente no arquivo `.env` com valores reais:
- `GOOGLE_CLIENT_ID`
- `GOOGLE_CLIENT_SECRET`
- `GOOGLE_REFRESH_TOKEN`

Atualmente, estas variáveis estão com valores placeholder no arquivo `.env`:
```
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_REFRESH_TOKEN=your-google-refresh-token
```

## 8. Importância para o Sistema

O M1 é o ponto de partida para a ingestão de dados. Sem ele, nenhum documento do Google Drive entraria no pipeline de processamento. Ele garante que os arquivos sejam baixados corretamente e que mensagens sejam enviadas para o RabbitMQ, disparando o processamento pelos módulos subsequentes (M2, M3, etc.).

## 9. Interdependências

-   **Produz para:**
    -   RabbitMQ (tópico `drive.ingest`): Envia mensagens com metadados dos arquivos baixados.
    -   Sistema de Arquivos Local: Salva os arquivos baixados em `data/raw/`.
-   **Depende de:**
    -   Google Drive API: Para listar e baixar arquivos.
    -   RabbitMQ: Para publicar mensagens.
-   **Utilizado por (indiretamente):**
    -   M2 Task Orchestrator: Consome as mensagens do tópico `drive.ingest`.
    -   M8 API Gateway: Expõe os endpoints do M1.
