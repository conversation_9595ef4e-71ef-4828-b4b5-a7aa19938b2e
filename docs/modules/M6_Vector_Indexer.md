# M6: Vector Indexer

## 1. Propósito do Módulo

O M6 Vector Indexer é responsável por transformar os chunks de texto (extraídos e traduzidos pelos módulos anteriores) em representações numéricas semânticas (embeddings vetoriais) e armazená-los de forma eficiente para permitir buscas por similaridade. Ele é o núcleo da capacidade de busca inteligente e de RAG (Retrieval-Augmented Generation) do sistema.

## 2. Funcionalidades Chave

- Geração de embeddings vetoriais para chunks de texto utilizando o modelo `text-embedding-3-small` da OpenAI (ou um mock para testes).
- Armazenamento dos embeddings gerados na tabela `chunk_embeddings` do banco de dados PostgreSQL, utilizando a extensão `pgvector`.
- Fornecimento de endpoints API para:
    - Iniciar o processo de indexação de todos os chunks pendentes.
    - Realizar buscas por similaridade semântica (vetorial).
    - Realizar buscas textuais simples.
    - Obter estatísticas sobre o estado da indexação.
    - Listar chunks pendentes de indexação com detalhes do documento de origem.
    - Controlar o modo de operação do serviço OpenAI (real ou mock).
    - Testar a geração de embeddings para um texto específico.
- **Interface de Administração Web:** Uma interface web acessível em `http://localhost:8086/admin-ui` (quando rodando localmente) que permite:
    - Monitorar estatísticas gerais de indexação em tempo real (com atualização automática).
    - Visualizar uma lista paginada de chunks pendentes de indexação, incluindo o nome do documento original.
    - Iniciar o processo de indexação de todos os chunks pendentes com um clique de botão.
- Processamento em lote de chunks para otimizar a geração e inserção de embeddings.
- Lógica de fallback e circuit breaker para o serviço OpenAI.

## 2.1. Acesso à UI de Administração

A interface de administração do Módulo 6 pode ser acessada através do endpoint `/admin-ui` do próprio serviço M6. Se o sistema estiver rodando localmente com as portas padrão, a URL é:

`http://localhost:8086/admin-ui`

Esta interface fornece uma visão geral do estado da indexação, permite visualizar quais chunks (e de quais documentos) ainda precisam ser processados, e oferece um controle manual para disparar o processo de indexação de todos os itens pendentes. Os dados na UI são atualizados automaticamente a cada 10 segundos.

## 3. Entradas e Saídas

-   **Entradas:**
    -   Lê chunks de texto da tabela `chunks_parent` (via `database_service`) que ainda não possuem embeddings para o modelo configurado (`config.OPENAI_EMBEDDING_MODEL`). A consulta envolve joins com as tabelas `versions` e `documents` para obter metadados.
    -   Recebe requisições HTTP em seus endpoints API para busca, controle e status.

-   **Saídas:**
    -   Insere os embeddings gerados na tabela `chunk_embeddings` do banco de dados.
    -   Retorna respostas JSON para as requisições API.
    -   Serve a interface HTML/CSS/JS da UI de Admin.

## 4. Principais Componentes Internos

-   `app/main.py`: Contém a aplicação FastAPI, define todos os endpoints da API, a lógica de orquestração da indexação em background (`process_all_pending_chunks`), e o gerenciamento do ciclo de vida da aplicação. Serve a UI de Admin.
-   `app/database_service.py`: Encapsula toda a interação com o banco de dados PostgreSQL, incluindo a busca por chunks pendentes, inserção de embeddings, buscas vetoriais e textuais, e obtenção de estatísticas. Utiliza `psycopg2` com `ThreadedConnectionPool`.
-   `app/openai_service.py`: Responsável por interagir com a API da OpenAI para gerar embeddings. Inclui lógica de retry, circuit breaker, e a capacidade de operar em modo "real" (OPENAI) ou "mock".
-   `app/config.py`: Define todas as configurações do módulo, carregadas a partir de variáveis de ambiente com valores padrão.
-   `app/static/admin_ui/`: Diretório contendo os arquivos estáticos (`index.html`, `style.css`, `script.js`) para a interface de administração do M6.

## 5. Endpoints da API

-   `GET /healthz`: Verifica a saúde do serviço M6 e suas dependências (conexão com DB, status do `openai_service`).
-   `GET /stats`: Retorna estatísticas detalhadas sobre o processo de indexação (total de chunks, total de embeddings, percentual de cobertura, chunks pendentes, etc.).
-   `POST /index/process-all`: Inicia uma tarefa em background para processar e indexar todos os chunks que ainda não possuem embeddings.
-   `GET /index/pending-chunks`: Retorna uma lista paginada de chunks pendentes de indexação, incluindo `chunk_id`, `document_id`, `original_name` do documento, `version_tag` e `chunk_index`.
-   `POST /search`: Recebe uma query textual, gera seu embedding e realiza uma busca por similaridade vetorial no banco, retornando os chunks mais relevantes.
-   `POST /index/test-embedding`: Permite testar a geração de embedding para um texto fornecido.
-   `GET /openai/metrics`: Retorna métricas do `openai_service` (modo atual, contadores, estado do circuit breaker).
-   `POST /openai/force-mode/{mode}`: Permite forçar o `openai_service` para o modo "OPENAI" ou "MOCK".
-   `GET /admin-ui`: Serve a página principal (index.html) da interface de administração do M6.
-   `GET /admin-ui-static/*`: Serve os arquivos estáticos (CSS, JS) para a UI de Admin.

(Para detalhes completos dos schemas de request/response, consultar a documentação interativa da API em `/docs` ou `/redoc` quando o serviço M6 estiver rodando).

## 6. Configurações Importantes (Principais)

-   `OPENAI_API_KEY`: Chave da API da OpenAI.
-   `OPENAI_EMBEDDING_MODEL`: Modelo de embedding a ser usado (ex: `text-embedding-3-small`).
-   `BATCH_SIZE` (variável de ambiente `EMBEDDING_BATCH_SIZE`): Tamanho do lote para buscar chunks pendentes e para inserir embeddings.
-   `SIMILARITY_THRESHOLD`: Limiar de similaridade padrão para as buscas vetoriais.
-   Variáveis de conexão com PostgreSQL (`POSTGRES_HOST`, `POSTGRES_PORT`, etc.).

## 7. Importância para o Sistema

O M6 Vector Indexer é fundamental para habilitar a busca semântica sobre o conteúdo dos documentos processados. Ele transforma texto em vetores que capturam o significado, permitindo que o sistema encontre informações relevantes mesmo que a consulta do usuário use termos diferentes dos presentes nos documentos. Esta capacidade é a base para funcionalidades avançadas como Retrieval-Augmented Generation (RAG), que será implementada pelo M10 (AI Orchestrator). Sem o M6, as buscas seriam limitadas a correspondências exatas de palavras-chave, e a "inteligência" do sistema em compreender e responder perguntas sobre os documentos seria drasticamente reduzida. A UI de Admin recém-criada também aumenta a observabilidade e controlabilidade deste módulo crítico.

## 8. Interdependências

-   **Consome de:**
    -   Banco de Dados PostgreSQL (especificamente as tabelas `chunks_parent`, `versions`, `documents` para obter os textos a serem indexados).
-   **Produz para:**
    -   Banco de Dados PostgreSQL (insere dados na tabela `chunk_embeddings`).
-   **Utilizado por:**
    -   M8 API Gateway: Expõe os endpoints do M6 para o mundo exterior (ex: para o M9 Frontend ou M10 AI Orchestrator).
    -   M10 AI Orchestrator (futuro): Utilizará o endpoint `/search` do M6 para recuperar chunks relevantes para as consultas do usuário.
-   **Depende de:**
    -   OpenAI API (ou o mock configurado): Para a geração dos embeddings vetoriais.
    -   PostgreSQL (com pgvector): Para armazenamento e consulta dos vetores.

## 9. Status Atual do Sistema (23/06/2025)

### ✅ Funcionalidades Operacionais
- **Conexão OpenAI**: A chave da OpenAI está funcionando corretamente e o serviço está gerando embeddings.
- **Banco de Dados**: Conexão com PostgreSQL está saudável.
- **Health Check**: O endpoint `/healthz` retorna status "ok" para todas as dependências.

### 🔧 Correções Implementadas
- O problema anterior relacionado à chave da OpenAI foi resolvido com sucesso.
