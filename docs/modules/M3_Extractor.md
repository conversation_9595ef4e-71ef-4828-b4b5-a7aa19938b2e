# M3: Extractor

## 1. Propósito do Módulo

O Módulo 3 (M3) - Extractor é responsável por processar os arquivos brutos (baixados pelo M1 e orquestrados pelo M2) e extrair seu conteúdo textual. Ele lida com diferentes formatos de arquivo, como PDF (textual e baseado em imagem via OCR) e Excel. Após a extração, o texto é dividido em "chunks" (pedaços menores) e armazenado no banco de dados. Finalmente, os metadados desses chunks são publicados no RabbitMQ para processamento subsequente (ex: tradução pelo M4).

## 2. Funcionalidades Chave

-   **Consumo de Tarefas:** Escuta o tópico `tasks.queue` do RabbitMQ para tarefas designadas ao módulo "extractor".
-   **Extração de Conteúdo de PDF:**
    -   Utiliza `PDFMiner.six` para extrair texto de PDFs baseados em texto.
    -   Se o PDFMiner retornar pouco texto (indicando um PDF baseado em imagem ou complexo), tenta a extração via OCR.
-   **Extração de Conteúdo via OCR:**
    -   Utiliza `pdf2image` para converter páginas de PDF em imagens.
    -   Utiliza `pytesseract` (Tesseract OCR) para extrair texto dessas imagens. Suporta múltiplos idiomas (ex: inglês e português).
-   **Extração de Conteúdo de Excel:**
    -   Utiliza `openpyxl` para ler dados de arquivos `.xlsx` e `.xls`.
    -   Extrai texto de todas as células em todas as planilhas do arquivo.
-   **Tratamento de Arquivos de Texto Genéricos:** Tenta ler outros tipos de arquivo como texto simples (UTF-8, depois Latin-1).
-   **Detecção de Idioma:** Utiliza `langdetect` para identificar o idioma do texto extraído.
-   **Chunking de Texto:** Divide o texto extraído em chunks menores (máximo de 1000 caracteres por padrão, ou por quebra de página de PDF).
-   **Persistência no Banco de Dados:**
    -   Cria/atualiza registros na tabela `documents` com metadados do arquivo original.
    -   Cria/atualiza registros na tabela `versions` para versionamento.
    -   Salva os chunks de texto na tabela `chunks_parent` (particionada), incluindo metadados como número da página e método de chunking.
-   **Publicação de Resultados:** Para cada chunk salvo, publica uma mensagem no tópico `extraction.results` do RabbitMQ.
-   **Atualização de Status da Tarefa:** Comunica-se com a API do M2 para atualizar o status da tarefa para "PROCESSING", "COMPLETED" ou "FAILED".

## 3. Entradas e Saídas

-   **Entradas:**
    -   **RabbitMQ (Consumidor):** Mensagens do tópico `tasks.queue` (exchange `message_exchange`, routing key `tasks.queue`) quando `module_to_process` é "extractor". Payload esperado:
        ```json
        {
            "job_id": "string (UUID da tarefa gerado pelo M2)",
            "module_to_process": "extractor",
            "data": { // Payload original do M1
                "task_id": "string (UUID da tarefa original do M1)",
                "drive_file_id": "string",
                "file_name": "string",
                "relative_path": "string",
                "mime_type": "string",
                "size_bytes": "integer | null",
                "download_path": "string (caminho do arquivo local, ex: /app/data/raw/...)",
                "checksum_sha256": "string"
            }
        }
        ```
    -   **Arquivos Locais:** Lê os arquivos baixados pelo M1 do sistema de arquivos (caminho fornecido no `download_path`).
    -   **Variáveis de Ambiente:** Configurações do RabbitMQ, PostgreSQL, M2 API, Tesseract.

-   **Saídas:**
    -   **Banco de Dados:**
        -   Cria/atualiza registros nas tabelas `documents` e `versions`.
        -   Insere registros na tabela `chunks_parent`.
    -   **RabbitMQ (Produtor):** Publica mensagens no tópico `extraction.results` (fila `extraction.results`, exchange padrão). Payload da mensagem por chunk:
        ```json
        {
            "chunk_id": "string (ID construído: {document_id}_chunk_{chunk_index})",
            "content": "string (texto do chunk)",
            "document_id": "string (UUID do documento)",
            "version_id": "string (UUID da versão)",
            "sequence_number": "integer (índice do chunk)",
            "job_id": "string (job_id original do M2)",
            "metadata": { /* metadados do chunk, ex: página, método de chunking */ }
        }
        ```
    -   **API do M2:** Faz requisições `PUT` para `M2_API_URL/tasks/{job_id}/status` para atualizar o status da tarefa.

## 4. Principais Componentes Internos

-   `app/consumer.py`: Contém a classe `ExtractorConsumer` com a lógica principal do consumidor RabbitMQ, orquestrando a extração, chunking, salvamento no DB e publicação dos resultados.
-   `app/extractor_service.py`: Fornece as funções de baixo nível para extrair texto de diferentes tipos de arquivo (PDF, OCR, Excel, texto) e detectar idioma.
-   `app/config.py`: Carrega e fornece as configurações do módulo.

## 5. Endpoints da API

O Módulo 3 (Extractor) opera primariamente como um consumidor de mensagens e não expõe uma API HTTP própria para funcionalidades de extração. Sua interação principal é através do RabbitMQ e atualizações de status para o M2.

## 6. Configurações Importantes

Variáveis de ambiente (geralmente em `.env`):
-   **RabbitMQ:**
    -   `RABBITMQ_HOST`, `RABBITMQ_PORT`, `RABBITMQ_USER`, `RABBITMQ_PASS`
-   **PostgreSQL:**
    -   `POSTGRES_HOST`, `POSTGRES_PORT`, `POSTGRES_DB`, `POSTGRES_USER`, `POSTGRES_PASSWORD`
-   **M2 API:**
    -   `M2_API_URL` (default: `http://localhost:8082` ou `http://mastigador_m2_api:8082` no Docker)
-   **Tesseract (Opcional):**
    -   `TESSDATA_PREFIX`: Caminho para os dados de treinamento do Tesseract, se não estiverem em um local padrão.
-   **Extração:**
    -   `MAX_CHUNK_SIZE` (default: 1000 caracteres)
    -   `DEFAULT_LANGUAGE` (default: `pt-BR`, usado como fallback ou para OCR)
-   `LOG_LEVEL`

## 7. Importância para o Sistema

O M3 Extractor é um passo fundamental no pipeline, pois converte documentos de diversos formatos em texto puro e estruturado (chunks). Essa representação textual é a base para os módulos subsequentes, como o M4 (Translator) e o M6 (Vector Indexer). Sem uma extração de conteúdo eficaz, a qualidade da tradução, da indexação semântica e, consequentemente, das buscas e análises futuras seria comprometida.

## 8. Interdependências

-   **Consome de:**
    -   RabbitMQ (tópico `tasks.queue`): Recebe tarefas do M2.
    -   Sistema de Arquivos Local: Lê os arquivos indicados nas mensagens.
-   **Produz para:**
    -   RabbitMQ (tópico `extraction.results`): Envia os chunks extraídos para o M4.
    -   Banco de Dados PostgreSQL: Salva informações sobre documentos, versões e os chunks.
    -   M2 Task Orchestrator API: Atualiza o status das tarefas.
-   **Depende de:**
    -   RabbitMQ: Para comunicação por mensagens.
    -   PostgreSQL: Para persistência de dados.
    -   M2 Task Orchestrator: Para atualizações de status.
    -   Bibliotecas de extração: `pdfminer.six`, `pytesseract`, `pdf2image`, `openpyxl`, `langdetect`.
    -   (Potencialmente) `poppler-utils` (para `pdf2image`).
