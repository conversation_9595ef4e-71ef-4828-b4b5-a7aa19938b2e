# M5: DB Browser

## 1. Propósito do Módulo

O Módulo 5 (M5) - DB Browser fornece uma interface API RESTful para consultar e visualizar os dados armazenados no banco de dados PostgreSQL do sistema "Mastigador de Dados em Lote". Ele permite listar documentos, suas versões, visualizar o conteúdo de chunks (originais e traduzidos) e executar queries SQL SELECT de forma controlada. Este módulo é útil para depuração, monitoramento e acesso direto aos dados por outras ferramentas ou interfaces.

## 2. Funcionalidades Chave

-   **Conexão com Banco de Dados:** Utiliza a biblioteca `databases` para conexões assíncronas com o PostgreSQL.
-   **Listagem de Documentos:** Endpoint para listar todos os documentos e suas respectivas versões armazenadas.
-   **Detalhes de Documento/Versão:** Endpoint para obter detalhes de uma versão específica de um documento, incluindo:
    -   Retorno em formato JSON com a lista de chunks (texto original, traduzido e metadados).
    -   Retorno opcional em formato HTML (`format=full`) que renderiza o conteúdo concatenado dos chunks (original ou traduzido).
-   **Execução de Queries SQL:** Endpoint para executar queries SQL `SELECT` personalizadas no banco de dados. Inclui uma verificação básica para permitir apenas `SELECT`.
-   **Health Check:** Endpoint para verificar a saúde do serviço e sua conexão com o banco de dados.

## 3. Entradas e Saídas

-   **Entradas:**
    -   **Requisições API:** Para os endpoints de listagem, detalhamento e query.
    -   **Banco de Dados:** Lê dados das tabelas `documents`, `versions`, `chunks_parent`, `translations`.
    -   **Variáveis de Ambiente:** `DATABASE_URL` (ou as variáveis individuais `POSTGRES_USER`, `POSTGRES_PASSWORD`, etc., para construí-la).

-   **Saídas:**
    -   **Respostas API (JSON):** Para a maioria dos endpoints.
    -   **Resposta API (HTML):** Para `GET /documents/{document_id_str}?format=full`.

## 4. Principais Componentes Internos

-   `app/main.py`: Aplicação FastAPI que define todos os endpoints, a lógica de consulta ao banco de dados e a formatação das respostas.
-   `databases.Database`: Objeto usado para interagir com o banco de dados de forma assíncrona.

## 5. Endpoints da API

A API do M5 é acessível em `http://localhost:8093` (quando rodando localmente via Docker Compose).
A documentação interativa da API (Swagger UI) está disponível em `http://localhost:8093/docs`.

### 5.1. Health Check

-   **Endpoint:** `GET /healthz`
-   **Descrição:** Verifica a saúde do M5 e sua conexão com o banco de dados.
-   **Resposta de Sucesso (200 OK Exemplo):**
    ```json
    {
        "status": "ok",
        "database": "ok"
    }
    ```
-   **Exemplo `curl`:**
    ```bash
    curl http://localhost:8093/healthz
    ```

### 5.2. Listar Documentos

-   **Endpoint:** `GET /documents`
-   **Descrição:** Lista todos os documentos e suas versões.
-   **Resposta de Sucesso (200 OK Exemplo):**
    ```json
    [
        {
            "document_id": "uuid-doc-1",
            "original_name": "documento_alpha.pdf",
            "versions": [
                {"version_tag": "v1", "version_id": "uuid-ver-1a"},
                {"version_tag": "v2", "version_id": "uuid-ver-1b"}
            ]
        },
        // ... mais documentos
    ]
    ```
-   **Exemplo `curl`:**
    ```bash
    curl http://localhost:8093/documents
    ```

### 5.3. Detalhes do Documento/Versão

-   **Endpoint:** `GET /documents/{document_id_str}`
-   **Descrição:** Retorna detalhes de uma versão específica de um documento.
-   **Parâmetros de Caminho:**
    -   `document_id_str`: O UUID do documento.
-   **Parâmetros de Query:**
    -   `version` (obrigatório): A tag da versão (ex: "v1").
    -   `format` (opcional): Se "full", retorna HTML. Caso contrário, JSON com chunks.
    -   `lang` (opcional, para `format=full`, padrão "pt"): Se "pt", tenta mostrar texto traduzido; caso contrário, original.
-   **Resposta JSON (200 OK Exemplo, sem `format=full`):**
    ```json
    {
        "document_id": "uuid-doc-1",
        "version_tag": "v1",
        "chunks": [
            {
                "chunk_index": 0,
                "original_text": "Texto original do chunk 0...",
                "translated_text": "Texto traduzido do chunk 0...",
                "chunk_metadata": {"page": 1, ...}
            },
            // ... mais chunks
        ]
    }
    ```
-   **Resposta HTML (200 OK Exemplo, com `format=full`):**
    ```html
    <html><head><title>Document uuid-doc-1 - v1</title></head><body>
    <p>Texto traduzido do chunk 0...</p><p>Texto traduzido do chunk 1...</p>
    </body></html>
    ```
-   **Exemplo `curl` (JSON):**
    ```bash
    curl "http://localhost:8093/documents/UUID_DO_DOCUMENTO?version=v1"
    ```
-   **Exemplo `curl` (HTML em português):**
    ```bash
    curl "http://localhost:8093/documents/UUID_DO_DOCUMENTO?version=v1&format=full&lang=pt"
    ```

### 5.4. Executar Query SQL

-   **Endpoint:** `GET /query`
-   **Descrição:** Executa uma query SQL `SELECT` no banco de dados. **Atenção: Apenas queries `SELECT` são permitidas.**
-   **Parâmetros de Query:**
    -   `sql` (obrigatório): A query SQL SELECT a ser executada.
-   **Resposta de Sucesso (200 OK Exemplo):**
    ```json
    {
        "columns": ["coluna1", "coluna2"],
        "rows": [
            {"coluna1": "valor1a", "coluna2": "valor2a"},
            {"coluna1": "valor1b", "coluna2": "valor2b"}
        ]
    }
    ```
-   **Exemplo `curl`:**
    ```bash
    curl -G "http://localhost:8093/query" --data-urlencode "sql=SELECT document_id, original_name FROM documents LIMIT 5"
    ```

## 6. Configurações Importantes

O M5 utiliza a variável de ambiente `DATABASE_URL` para a conexão com o PostgreSQL. Se não definida, ela é construída a partir das seguintes variáveis:
-   `POSTGRES_USER` (default: `user`)
-   `POSTGRES_PASSWORD` (default: `password`)
-   `POSTGRES_DB` (default: `mastigador_db`)
-   `POSTGRES_HOST` (default: `db` - nome do serviço Docker)
-   `POSTGRES_PORT` (default: `5432`)

Exemplo de `DATABASE_URL`: `**********************************/mastigador_db`

## 7. Importância para o Sistema

O M5 DB Browser fornece uma maneira padronizada e controlada de acessar os dados brutos e processados armazenados pelo sistema. É útil para:
-   Depuração e troubleshooting.
-   Monitoramento do estado dos dados.
-   Integração com ferramentas de análise ou visualização externas.
-   Permitir que o M8 API Gateway agregue informações (ex: buscar `original_name` para enriquecer resultados de busca do M6).

## 8. Interdependências

-   **Consome de:**
    -   Banco de Dados PostgreSQL: Lê dados de todas as tabelas principais (`documents`, `versions`, `chunks_parent`, `translations`).
-   **Utilizado por:**
    -   M8 API Gateway: Para expor a funcionalidade de browse e query.
    -   Desenvolvedores/Administradores: Para inspeção direta dos dados.
-   **Depende de:**
    -   PostgreSQL.
