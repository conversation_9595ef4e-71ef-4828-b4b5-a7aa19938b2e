# M8: API Gateway

## 1. Propósito do Módulo

O Módulo 8 (M8) - API Gateway atua como um ponto de entrada único e unificado para todas as requisições externas destinadas ao sistema "Mastigador de Dados em Lote". Ele simplifica a arquitetura de microsserviços, fornecendo uma fachada consistente para os clientes (como o frontend M9 ou ferramentas de API) e lidando com o roteamento de requisições para os serviços backend apropriados (M1, M2, M5, M6, M7, etc.). Além disso, o M8 pode realizar tarefas como agregação de dados de múltiplos serviços, health check agregado e, futuramente, autenticação e rate limiting.

## 2. Funcionalidades Chave

-   **Proxy Reverso:** Encaminha requisições HTTP recebidas para os microsserviços internos corretos com base no caminho da URL.
    -   Suporta prefixo `/v1/` para versionamento da API.
    -   Oferece rotas de conveniência sem o prefixo `/v1/` para compatibilidade com o frontend.
-   **Agregação de Health Check:** Fornece um endpoint (`/healthz`) que consolida o status de saúde de todos os principais microsserviços backend, oferecendo uma visão geral do estado do sistema.
-   **Agregação de Resultados de Busca:** O endpoint `/v1/search` primeiro consulta o M6 (Vector Indexer) para busca semântica e, em seguida, enriquece os resultados com metadados adicionais (como `original_name` do documento) obtidos do M5 (DB Browser).
-   **Streaming de Eventos (Server-Sent Events - SSE):** Expõe um endpoint (`/v1/events`) que permite aos clientes se inscreverem para receber eventos em tempo real de diversos tópicos do RabbitMQ, como `drive.ingest`, `tasks.queue`, `extraction.results`, e `translation.results`.
-   **Roteamento para Futuros Módulos:** Inclui placeholders para roteamento para módulos futuros (ex: M10 AI Orchestrator).

## 3. Entradas e Saídas

-   **Entradas:**
    -   Requisições HTTP de clientes externos (ex: frontend M9, ferramentas de API).
    -   Respostas HTTP dos microsserviços backend (M1, M2, M5, M6, M7).
    -   Mensagens de tópicos do RabbitMQ (para o endpoint SSE).
    -   Variáveis de Ambiente: URLs dos serviços backend, credenciais do RabbitMQ.

-   **Saídas:**
    -   Respostas HTTP para os clientes externos, que podem ser respostas diretas de um microsserviço (proxy) ou respostas agregadas/transformadas pelo M8.
    -   Stream de Server-Sent Events para clientes conectados ao endpoint `/v1/events`.

## 4. Principais Componentes Internos

-   `app/main.py`: Aplicação FastAPI que define todos os endpoints do gateway, a lógica de proxy (`_proxy_request`), a agregação de health checks, a agregação de busca e o gerador de eventos SSE (`rabbitmq_event_generator`).
-   `httpx.AsyncClient`: Usado para fazer requisições HTTP assíncronas para os serviços backend.
-   `aio_pika`: Biblioteca para interagir de forma assíncrona com o RabbitMQ para o stream de eventos SSE.

## 5. Endpoints da API

A API do M8 é acessível em `http://localhost:8080` (quando rodando localmente via Docker Compose).
A documentação interativa da API (Swagger UI) está disponível em `http://localhost:8080/docs`.

### 5.1. Health Check Agregado

-   **Endpoint:** `GET /healthz` (também `GET /health` para compatibilidade)
-   **Descrição:** Retorna o status do gateway e um status agregado da saúde de todos os microsserviços backend (M1, M2, M5, M6, M7).
-   **Resposta de Sucesso (200 OK Exemplo):**
    ```json
    {
        "gateway_status": "ok",
        "overall_backend_status": "ok", // ou "error" se algum backend falhar
        "services": {
            "m1_drive_connector": {"status": "ok", "drive_service_status": "ok", ...},
            "m2_task_orchestrator": {"status": "healthy", "database": "healthy", ...},
            "m5_db_browser": {"status": "ok", "database": "ok"},
            "m6_vector_indexer": {"db": "ok", "openai": "ok", "m6_status": "ok"},
            "m7_diff_engine": {"db": "ok", "m7_status": "ok"}
        }
    }
    ```
-   **Exemplo `curl`:**
    ```bash
    curl http://localhost:8080/healthz
    ```

### 5.2. Rotas de Proxy (Prefixadas com `/v1/`)

O M8 faz proxy para os seguintes serviços, geralmente prefixando as rotas com `/v1/`. O caminho após `/v1/` é repassado ao serviço de destino.

-   **M1 Drive Connector (base: `http://localhost:8081`):**
    -   `POST /v1/import`: Delega para `M1_DRIVE_CONNECTOR_URL/import`.
        ```bash
        curl -X POST -H "Content-Type: application/json" \
        -d '{"drive_folder_id": "ID_DA_PASTA"}' \
        http://localhost:8080/v1/import
        ```
-   **M2 Task Orchestrator (base: `http://localhost:8082`):**
    -   `GET /v1/tasks`: Delega para `M2_TASK_ORCHESTRATOR_URL/tasks`.
    -   `GET /v1/tasks/{job_id}`: Delega para `M2_TASK_ORCHESTRATOR_URL/tasks/{job_id}`.
    -   `PATCH /v1/tasks/{job_id}`: Delega para `M2_TASK_ORCHESTRATOR_URL/tasks/{job_id}`.
-   **M5 DB Browser (base: `http://localhost:8093`):**
    -   `GET /v1/documents`: Delega para `M5_DB_BROWSER_URL/documents`.
    -   `GET /v1/documents/{document_id_str}`: Delega para `M5_DB_BROWSER_URL/documents/{document_id_str}`.
    -   `GET /v1/query`: Delega para `M5_DB_BROWSER_URL/query`.
-   **M6 Vector Indexer (base: `http://localhost:8086`):**
    -   `POST /v1/index`: Delega para `M6_VECTOR_INDEXER_URL/index`.
    -   `POST /v1/search/vector`: Delega para `M6_VECTOR_INDEXER_URL/search/vector`. (Corrigido de `/by-vector`)
    -   `GET /v1/index-status/{task_id}`: Delega para `M6_VECTOR_INDEXER_URL/index-status/{task_id}`.
-   **M7 Diff Engine (base: `http://localhost:8087`):**
    -   `POST /v1/diff`: Delega para `M7_DIFF_ENGINE_URL/diff`.
    -   `POST /v1/export/diff`: Delega para `M7_DIFF_ENGINE_URL/export/diff`.

### 5.3. Rota de Busca Agregada

-   **Endpoint:** `POST /v1/search` (também `POST /search` para compatibilidade)
-   **Descrição:** Realiza uma busca semântica no M6 e enriquece os resultados com o nome original do documento do M5.
-   **Corpo da Requisição (JSON, exemplo para M6):**
    ```json
    {
        "query": "texto da busca",
        "limit": 10,
        "similarity_threshold": 0.7
    }
    ```
-   **Resposta de Sucesso (200 OK Exemplo):**
    ```json
    [ // Lista de resultados do M6, cada um com um campo adicional "original_name"
        {
            "document_id": "uuid_do_documento",
            "content": "texto do chunk...",
            "similarity_score": 0.85,
            "metadata": { ... },
            "original_name": "Nome Original do Documento.pdf" 
        },
        // ...
    ]
    ```
-   **Exemplo `curl`:**
    ```bash
    curl -X POST -H "Content-Type: application/json" \
    -d '{"query": "termo de busca", "limit": 5, "similarity_threshold": 0.75}' \
    http://localhost:8080/v1/search
    ```

### 5.4. Streaming de Eventos SSE

-   **Endpoint:** `GET /v1/events`
-   **Descrição:** Abre uma conexão Server-Sent Events que transmite mensagens dos tópicos do RabbitMQ: `drive.ingest`, `tasks.queue`, `extraction.results`, `translation.results`.
-   **Exemplo de Uso (JavaScript no navegador):**
    ```javascript
    const eventSource = new EventSource("http://localhost:8080/v1/events");
    eventSource.onmessage = function(event) {
        console.log("Novo evento:", JSON.parse(event.data));
    };
    eventSource.onerror = function(err) {
        console.error("Erro no EventSource:", err);
    };
    ```

### 5.5. Placeholder para Chat (M10)

-   **Endpoint:** `POST /v1/chat`
-   **Descrição:** Placeholder para o futuro M10 AI Orchestrator. Atualmente retorna 501 Not Implemented.

## 6. Configurações Importantes

Variáveis de ambiente que definem as URLs dos serviços backend:
-   `M1_DRIVE_CONNECTOR_URL` (padrão: `http://mastigador_m1_drive:8081`)
-   `M2_TASK_ORCHESTRATOR_URL` (padrão: `http://mastigador_m2_api:8082`)
-   `M5_DB_BROWSER_URL` (padrão: `http://mastigador_m5_db_browser:8093`)
-   `M6_VECTOR_INDEXER_URL` (padrão: `http://mastigador_m6_indexer:8086`)
-   `M7_DIFF_ENGINE_URL` (padrão: `http://mastigador_m7_diff:8087`)
Variáveis de ambiente para conexão com RabbitMQ (para SSE):
-   `RABBITMQ_HOST`
-   `RABBITMQ_USER`
-   `RABBITMQ_PASS`

## 7. Importância para o Sistema

O API Gateway (M8) é essencial para desacoplar os clientes dos microsserviços individuais. Ele fornece uma superfície de API estável e gerenciada centralmente. Facilita o desenvolvimento do frontend, a segurança (futuramente), o monitoramento e a evolução da arquitetura de microsserviços sem quebrar os clientes. A agregação de health checks e de resultados de busca também adiciona valor ao simplificar a interação do cliente com o sistema.

## 8. Interdependências

-   **Consome de (faz requisições para):**
    -   M1 Drive Connector (`/healthz`, `/import`, etc.)
    -   M2 Task Orchestrator (`/healthz`, `/tasks`, etc.)
    -   M5 DB Browser (`/healthz`, `/documents`, etc.)
    -   M6 Vector Indexer (`/healthz`, `/search`, etc.)
    -   M7 Diff Engine (`/healthz`, `/diff`, etc.)
    -   RabbitMQ (para o endpoint `/v1/events`)
-   **Utilizado por:**
    -   Qualquer cliente externo, primariamente o M9 Frontend UI.
    -   Ferramentas de teste e administração.
