# Referência da API M2 - Orquestrador de Tarefas

Este documento detalha as correções e o estado atual da API M2, focando nas melhorias de estabilidade e confiabilidade após a recente operação de recuperação.

## Visão Geral das Correções

Uma regressão massiva foi identificada nos testes de integração da API M2, resultando em uma queda significativa na taxa de sucesso. A investigação revelou problemas com mappings ausentes/incorretos no WireMock e incompatibilidades de schema. As seguintes correções foram implementadas:

### 1. Resolução de Conflitos de Prioridade no WireMock
*   **Problema:** Mappings genéricos estavam sendo avaliados antes de mappings mais específicos, causando respostas incorretas (e.g., 200 OK em vez de 404 Not Found ou 400 Bad Request).
*   **Solução:** A prioridade dos mocks no WireMock foi ajustada. Mappings específicos (como 404 para documentos não encontrados e 400 para tipos de arquivo inválidos) agora possuem prioridades mais baixas (valores numéricos menores) para garantir que sejam correspondidos antes de mappings genéricos de sucesso.

### 2. Correção do Schema e Valor do Endpoint `/health`
*   **Problema:** O endpoint `/health` estava retornando um schema (`"services"`) e um valor (`object`) que não correspondiam às expectativas dos testes (`"service"` e `"M2-MockAPI"`).
*   **Solução:** O mapping do WireMock para `/health` foi atualizado para retornar o schema e os valores exatos esperados pelos testes, incluindo a chave `"service"` e o valor `"M2-MockAPI"`, além de um objeto `checks` detalhado.

### 3. Restauração e Validação de Mappings de Upload
*   **Problema:** Testes de upload estavam falhando com `404 Not Found` devido à ausência ou configuração incorreta de mappings para o endpoint `POST /api/v1/documents/upload`. Além disso, o helper `{{randomValue type='UUID'}}` estava gerando UUIDs malformados, causando falhas na validação.
*   **Solução:** Mappings para cenários de sucesso (`201 Created`) e de tipo de arquivo inválido (`400 Bad Request`) foram recriados com as prioridades corretas. O `document_id` no mock de sucesso foi substituído por um UUID estático e válido (`123e4567-e89b-12d3-a456-************`) para garantir a consistência e a validade nos testes de workflow.

### 4. Simulação Aprimorada de Erro de Conexão
*   **Problema:** O teste de erro de conexão (`test_api_connection_error`) falhava porque o mock do WireMock respondia muito rapidamente, impedindo que a exceção de timeout fosse acionada.
*   **Solução:** Um atraso fixo de 500ms (`"fixedDelayMilliseconds": 500`) foi adicionado ao mapping do endpoint `/health` no WireMock. Isso garante que a resposta do mock seja mais lenta que o timeout de 1ms configurado no teste, forçando a exceção `requests.exceptions.Timeout` ou `requests.exceptions.ConnectionError` esperada. O teste foi refatorado para usar o header `X-Test-Connection-Error` para acionar este cenário.

## Status Atual

Todos os 21 testes de integração da API M2 (`m3_extractor/tests/integration/test_m2_api_integration.py`) estão passando com 100% de sucesso. A API M2 está estável e o ambiente de teste está totalmente funcional.

## Próximos Passos

A operação de recuperação foi concluída com sucesso. A equipe pode agora retomar a **Tarefa #20: Implementar Plano de Testes Completo do M3 (Extractor)**, com a confiança de que a API M2 está operando conforme o esperado.