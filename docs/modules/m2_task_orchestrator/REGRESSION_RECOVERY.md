# Relatório de Recuperação: Regressão Massiva da API M2

## Sumário Executivo
Uma regressão crítica foi identificada nos testes de integração da API M2, com a taxa de sucesso caindo de 90% para 24%. A causa raiz foi atribuída a mappings incorretos/ausentes do WireMock, incluindo conflitos de prioridade e incompatibilidades de schema. Uma operação de recuperação de emergência foi iniciada para restaurar a estabilidade do ambiente de testes.

## Análise da Causa Raiz
1.  **Conflitos de Prioridade do WireMock:** Mappings genéricos estavam sendo avaliados antes de mappings mais específicos, resultando em respostas inesperadas (ex: 200 OK em vez de 404 Not Found).
2.  **Incompatibilidade de Schema do Health Check:** O endpoint `/health` do mock retornava um schema (`"services"`) que não correspondia ao esperado pelos testes (`"service"`).
3.  **Mappings de Upload Ausentes/Incorretos:** Mappings para cenários de upload (sucesso 201, tipo de arquivo inválido 400) estavam faltando ou configurados incorretamente, incluindo a geração de UUIDs inválidos no mock.
4.  **Simulação de Erros de Conexão/Timeout:** Testes para `ConnectionError` e `Timeout` não estavam acionando os mocks corretos ou não tinham atrasos configurados para simular timeouts.

## Ações de Recuperação e Correções Aplicadas

As seguintes correções foram implementadas de forma incremental:

1.  **Correção de Prioridades do WireMock:**
    *   O mapping para o status 404 (Document Not Found) foi configurado com uma prioridade mais alta (menor valor numérico) para garantir que fosse avaliado antes do mapping genérico de sucesso (200 OK).
    *   Exemplo de ajuste de prioridade:
        ```json
        {
          "name": "Document Status Not Found - FIXED PRIORITY",
          "priority": 1,
          "request": { ... },
          "response": { "status": 404, ... }
        }
        ```

2.  **Correção de Schema e Atraso do Health Check (`/health`):**
    *   O `jsonBody` do mapping do endpoint `/health` (ID: `f18f5ad0-a2a2-4730-b827-46782eae5d03`) foi atualizado para usar a chave `"service"` (singular) em vez de `"services"` (plural) e o valor `"M2-MockAPI"`, além de adicionar um objeto `checks` para simular dependências.
    *   Um `fixedDelayMilliseconds` de 500ms foi adicionado ao mapping do `/health` para permitir o teste de timeout.
    *   Exemplo de ajuste:
        ```json
        {
          "id": "f18f5ad0-a2a2-4730-b827-46782eae5d03",
          "name": "Health Check Endpoint - SCHEMA/VALUE/DELAY FIXED",
          "request": { "method": "GET", "url": "/health" },
          "response": {
            "status": 200,
            "jsonBody": {
              "status": "healthy",
              "service": "M2-MockAPI",
              "checks": { "database": "connected", "queue": "connected", "external_api": "connected" }
            },
            "fixedDelayMilliseconds": 500
          },
          "priority": 10
        }
        ```

3.  **Restauração e Correção de Mappings de Upload:**
    *   **Sucesso (201 Created):** Um novo mapping foi criado para o cenário de upload bem-sucedido, retornando um `document_id` estático e válido (`123e4567-e89b-12d3-a456-426614174000`) para garantir consistência nos testes subsequentes de status. Prioridade definida como 5.
    *   **Tipo de Arquivo Inválido (400 Bad Request):** O mapping para tipo de arquivo inválido (`image/jpeg`) foi recriado com `priority: 1` para garantir que fosse correspondido antes do mapping de sucesso.
    *   **Simulação de Erro de Conexão:** Um mapping foi adicionado com `fault: "CONNECTION_RESET_BY_PEER"` e `priority: 1`, acionado por um header `X-Test-Connection-Error` para simular falhas de rede de forma controlada.

4.  **Refatoração de Testes no `test_m2_api_integration.py`:**
    *   O teste `test_api_connection_error` foi modificado para enviar o header `X-Test-Connection-Error` em vez de tentar conectar a um host inválido, garantindo que o mock de falha de conexão fosse acionado.
    *   O teste `test_api_timeout_handling` agora depende do `fixedDelayMilliseconds` configurado no mock do `/health` para simular o timeout.

## Validação
Após a aplicação de todas as correções, a suíte de testes de integração da API M2 foi reexecutada. Todos os 21 testes passaram com 100% de sucesso, confirmando a resolução da regressão.

## Próximos Passos
*   Adicionar este relatório ao Memory Bank para referência futura.
*   Reindexar o codebase para que as informações atualizadas sejam pesquisáveis.
*   Retomar a **Tarefa #20: "Implementar Plano de Testes Completo do M3 (Extractor)"**.