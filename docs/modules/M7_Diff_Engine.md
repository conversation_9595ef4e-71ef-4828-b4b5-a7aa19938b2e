# M7: Diff Engine

## 1. Propósito do Módulo

O Módulo 7 (M7) - Diff Engine é responsável por comparar duas versões de um mesmo documento e identificar as diferenças entre elas. Ele analisa o conteúdo textual (original ou traduzido) das versões, destaca as linhas adicionadas, removidas ou modificadas, e fornece um resumo das alterações. O M7 utiliza um cache Redis para armazenar resultados de comparações já realizadas, otimizando a performance para requisições repetidas.

## 2. Funcionalidades Chave

-   **Comparação de Documentos:** Compara o conteúdo textual de duas versões de um documento.
    -   Pode comparar o texto original ou o texto traduzido (especificando o idioma).
-   **Algoritmo de Diff:** Utiliza `difflib.SequenceMatcher` para identificar as diferenças linha a linha.
-   **Tratamento de Documentos Grandes:** Implementa uma estratégia de chunking para comparar documentos muito grandes que excedam um limite de tamanho.
-   **Nível de Caractere (Experimental):** Oferece um endpoint para comparar duas linhas específicas em nível de caractere.
-   **Cache de Resultados (Redis):**
    -   Armazena os resultados de comparações (`DiffResult`) no Redis para acelerar respostas futuras para as mesmas comparações.
    -   As chaves de cache podem ser baseadas nas tags de versão ou, opcionalmente, em hashes do conteúdo para maior precisão.
    -   Fornece endpoints para gerenciar o cache (ver stats, invalidar, limpar).
-   **API RESTful:** Expõe endpoints para:
    -   Solicitar uma comparação de diff (GET ou POST).
    -   Listar versões de um documento.
    -   Listar todos os documentos conhecidos pelo banco.
    -   Gerenciar o cache.
    -   Realizar diff em nível de caractere.
-   **Interface Web Estática:** Serve uma página HTML (`static/index.html`) para interagir com algumas funcionalidades do diff engine.
-   **Health Check:** Endpoint para verificar a saúde do serviço e suas dependências (DB, Cache).

## 3. Entradas e Saídas

-   **Entradas:**
    -   **Requisições API:**
        -   Para `/diff`: `document_id`, `version_old`, `version_new`, `use_translated_content`, `language`, `use_cache`.
        -   Para outros endpoints: IDs de documento, tags de versão, etc.
    -   **Banco de Dados PostgreSQL:** Lê o conteúdo textual (original ou traduzido) das versões dos documentos das tabelas `documents`, `versions`, `chunks_parent`, `translations`.
    -   **Redis Cache:** Verifica se resultados de diff já existem.
    -   **Variáveis de Ambiente:** URLs de banco e Redis, TTLs de cache, configurações de diff.

-   **Saídas:**
    -   **Respostas API (JSON):**
        -   Para `/diff`: Um objeto `DiffResponse` contendo os chunks de diferença, resumo e metadados.
        -   Para outros endpoints: Listas de documentos, versões, estatísticas de cache, etc.
    -   **Redis Cache:** Armazena novos resultados de `DiffResult`.
    -   **Interface Web Estática:** Serve o `index.html` e arquivos associados.

## 4. Principais Componentes Internos

-   `app/main.py`: Aplicação FastAPI, define todos os endpoints da API.
-   `app/diff_engine.py`: Contém a classe `DiffEngine` com a lógica principal de comparação de textos, usando `difflib`. Define as estruturas `DiffChunk` e `DiffResult`.
-   `app/database_service.py`: Encapsula a interação com o banco de dados PostgreSQL para buscar o conteúdo das versões dos documentos.
-   `app/cache_service.py`: Gerencia o cache dos resultados de diff no Redis.
-   `app/config.py`: Carrega e fornece as configurações do módulo.
-   `static/`: Diretório contendo a UI estática.

## 5. Endpoints da API

A API do M7 é acessível em `http://localhost:8087` (quando rodando localmente via Docker Compose).
A documentação interativa da API (Swagger UI) está disponível em `http://localhost:8087/docs`.

### 5.1. Interface Web Estática

-   **Endpoint:** `GET /`
-   **Descrição:** Serve a página principal da interface web estática (`static/index.html`).
-   **Acesso:** Abra `http://localhost:8087/` em um navegador.

### 5.2. Health Check

-   **Endpoint:** `GET /healthz`
-   **Descrição:** Verifica a saúde do M7 e suas conexões com DB e Cache.
-   **Resposta de Sucesso (200 OK Exemplo):**
    ```json
    {
        "db": "ok", // ou "error"
        "m7_status": "ok" // ou "error"
        // O status do cache não é incluído aqui para alinhar com o PRD do M8
    }
    ```
-   **Exemplo `curl`:**
    ```bash
    curl http://localhost:8087/healthz
    ```

### 5.3. Comparar Documentos (Diff)

-   **Endpoints:**
    -   `POST /diff` (corpo da requisição JSON)
    -   `GET /diff` (parâmetros de query)
-   **Descrição:** Compara duas versões de um documento.
-   **Parâmetros/Corpo da Requisição (`DiffRequest`):**
    ```json
    {
        "document_id": "uuid_ou_int_do_documento",
        "version_old": "tag_versao_antiga", // ex: "v1"
        "version_new": "tag_versao_nova",   // ex: "v2"
        "use_translated_content": false, // opcional, default false
        "language": "pt-br",             // opcional, default "pt-br"
        "use_cache": true                // opcional, default true
    }
    ```
-   **Resposta de Sucesso (200 OK - `DiffResponse`):**
    ```json
    {
        "document_id": "uuid_ou_int_do_documento",
        "version_old": "v1",
        "version_new": "v2",
        "chunks": [
            {"content": "linha inalterada", "change_type": "unchanged", ...},
            {"content": "linha removida", "change_type": "deleted", ...},
            {"content": "linha adicionada", "change_type": "added", ...}
        ],
        "summary": {"total_changes": 5, "lines_added": 2, ...},
        "metadata": {"comparison_method": "full_document", ...},
        "from_cache": false
    }
    ```
-   **Exemplo `curl` (POST):**
    ```bash
    curl -X POST -H "Content-Type: application/json" \
    -d '{"document_id": "ID_DO_DOC", "version_old": "v1", "version_new": "v2"}' \
    http://localhost:8087/diff
    ```
-   **Exemplo `curl` (GET):**
    ```bash
    curl "http://localhost:8087/diff?doc_id=ID_DO_DOC&v1=v1&v2=v2"
    ```

### 5.4. Listar Versões de um Documento

-   **Endpoint:** `GET /document/{document_id}/versions`
-   **Descrição:** Lista todas as versões de um documento específico.
-   **Exemplo `curl`:**
    ```bash
    curl http://localhost:8087/document/ID_DO_DOC/versions
    ```

### 5.5. Listar Todos os Documentos

-   **Endpoint:** `GET /api/documents`
-   **Descrição:** Lista todos os documentos conhecidos pelo banco de dados.
-   **Exemplo `curl`:**
    ```bash
    curl http://localhost:8087/api/documents
    ```

### 5.6. Gerenciamento de Cache

-   `GET /cache/stats`: Retorna estatísticas do cache Redis.
-   `DELETE /cache/document/{document_id}`: Invalida o cache para um documento específico.
-   `DELETE /cache/all`: Limpa todo o cache de diffs do M7.
-   `GET /cache/documents`: Lista informações sobre documentos em cache.

### 5.7. Diff em Nível de Caractere

-   **Endpoint:** `POST /diff/character-level`
-   **Descrição:** Compara duas strings (linhas) em nível de caractere.
-   **Corpo da Requisição (JSON):**
    ```json
    {
        "line_old": "texto da linha antiga",
        "line_new": "texto da linha nova"
    }
    ```
-   **Exemplo `curl`:**
    ```bash
    curl -X POST -H "Content-Type: application/json" \
    -d '{"line_old": "hello world", "line_new": "hello_world!"}' \
    http://localhost:8087/diff/character-level
    ```

## 6. Configurações Importantes

Variáveis de ambiente (ou valores padrão em `config.py`):
-   `DATABASE_URL`: URL de conexão com o PostgreSQL.
-   `REDIS_URL`: URL de conexão com o Redis.
-   `REDIS_TTL`: Tempo de vida padrão para itens no cache Redis (default: 3600s).
-   `HOST`, `PORT`: Para a API do M7 (default: `0.0.0.0:8087`).
-   `CACHE_MAX_SIZE`: (Não parece ser usado ativamente no código fornecido).
-   `DOCUMENT_CACHE_TTL`: TTL específico para cache de documentos (default: 1800s).
-   `MAX_DIFF_SIZE`: Tamanho máximo de documento para diff completo antes de usar chunking (default: 1MB).
-   `CHUNK_SIZE`: Tamanho do chunk para documentos grandes (default: 10000 caracteres).
-   `EXPORT_TEMP_DIR`: Diretório temporário para exportações (default: `/tmp/diff_exports`).
-   `LOG_LEVEL`.

## 7. Importância para o Sistema

O M7 Diff Engine é crucial para identificar e visualizar as alterações entre diferentes versões de um mesmo documento. Isso é essencial para:
-   Controle de versão e auditoria de mudanças.
-   Entender a evolução do conteúdo de um documento.
-   Fornecer uma base para revisões e aprovações de alterações.
-   Permitir que usuários finais ou outros sistemas compreendam rapidamente o que mudou entre duas iterações de um documento.

## 8. Interdependências

-   **Consome de:**
    -   Banco de Dados PostgreSQL: Lê o conteúdo textual (original ou traduzido) das versões dos documentos.
    -   Redis Cache: Para armazenar e recuperar resultados de diff.
-   **Utilizado por:**
    -   M8 API Gateway: Para expor a funcionalidade de diff.
    -   Frontend M9 (potencialmente): Para exibir as diferenças aos usuários.
-   **Depende de:**
    -   PostgreSQL.
    -   Redis.
