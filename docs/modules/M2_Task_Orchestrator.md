# M2: Task Orchestrator

## 1. Propósito do Módulo

O Módulo 2 (M2) - Task Orchestrator é o cérebro do pipeline de processamento de documentos. Sua principal responsabilidade é:
1.  Consumir mensagens do M1 (Drive Connector) que indicam novos arquivos baixados.
2.  Criar e gerenciar "tarefas" (jobs) no banco de dados para cada etapa do processamento de um arquivo (extração, tradução, etc.).
3.  Decidir qual o próximo módulo deve processar um arquivo com base em seu tipo ou outras regras.
4.  Publicar novas mensagens no RabbitMQ para direcionar a tarefa ao módulo apropriado.
5.  Fornecer uma API para monitorar e gerenciar o status dessas tarefas.

Ele atua como um intermediário que transforma um "arquivo baixado" em uma série de "tarefas de processamento" e as encaminha pelo sistema.

## 2. Funcionalidades Chave

-   **Consumo de Mensagens do M1:** Escuta o tópico `drive.ingest` do RabbitMQ para receber notificações sobre novos arquivos baixados pelo M1.
-   **Criação de Tarefas no Banco de Dados:** Para cada arquivo recebido, cria um registro na tabela `tasks` do banco de dados, atribuindo um `job_id` único e definindo o módulo de destino inicial (ex: "extractor").
-   **Roteamento de Tarefas:** Com base no `mime_type` do arquivo (ou outras lógicas futuras), determina qual módulo (M3, M4, etc.) deve processar a tarefa a seguir.
-   **Publicação de Tarefas para Módulos de Processamento:** Publica uma nova mensagem no tópico `tasks.queue` do RabbitMQ, contendo o `job_id` e o payload original do M1, direcionada ao módulo de processamento determinado.
-   **API RESTful para Gerenciamento de Tarefas:** Expõe endpoints para:
    -   Listar tarefas com filtros (status, módulo) e paginação.
    -   Obter detalhes de uma tarefa específica.
    -   Obter estatísticas agregadas sobre as tarefas.
    -   Criar tarefas manualmente (para testes ou reprocessamento).
    -   Cancelar tarefas.
    -   Tentar novamente tarefas que falharam (reenfileiramento).
    -   Permitir que módulos de processamento atualizem o status de uma tarefa.
-   **Health Check:** Endpoint para verificar a saúde do serviço M2, incluindo conectividade com o banco de dados e RabbitMQ.

## 3. Entradas e Saídas

-   **Entradas:**
    -   **RabbitMQ (Consumidor):** Mensagens do tópico `drive.ingest` (publicadas pelo M1). Payload esperado:
        ```json
        {
            "task_id": "string (UUID da tarefa de importação do M1)",
            "drive_file_id": "string",
            "file_name": "string",
            "relative_path": "string",
            "mime_type": "string",
            "size_bytes": "integer | null",
            "download_path": "string",
            "checksum_sha256": "string"
        }
        ```
    -   **Requisições API:** Para os endpoints de gerenciamento de tarefas.
    -   **Banco de Dados:** Lê a tabela `tasks` para listar, obter detalhes e estatísticas.
    -   **Variáveis de Ambiente:** Configurações do RabbitMQ e PostgreSQL.

-   **Saídas:**
    -   **Banco de Dados:** Cria e atualiza registros na tabela `tasks`.
    -   **RabbitMQ (Produtor):** Publica mensagens no tópico `tasks.queue` (exchange `message_exchange`). Payload da mensagem:
        ```json
        {
            "job_id": "string (UUID gerado pelo M2)",
            "module_to_process": "string (ex: extractor)",
            "data": { /* payload original do M1 */ }
        }
        ```
    -   **Respostas API (JSON):** Para os endpoints da API de gerenciamento de tarefas.

## 4. Principais Componentes Internos

-   `app/main.py`: Aplicação FastAPI, define os endpoints da API para gerenciamento e monitoramento de tarefas.
-   `app/consumer.py`: Contém a lógica do consumidor RabbitMQ que escuta o tópico `drive.ingest`, processa as mensagens, cria tarefas no banco e publica novas mensagens para o tópico `tasks.queue`.
-   `app/db_handler.py`: Encapsula a lógica de interação com a tabela `tasks` no banco de dados PostgreSQL (ex: `create_task_in_db`).
-   `app/config.py`: Carrega e fornece as configurações do módulo (RabbitMQ, PostgreSQL).

## 5. Endpoints da API

A API do M2 é acessível em `http://localhost:8082` (quando rodando localmente via Docker Compose).
A documentação interativa da API (Swagger UI) está disponível em `http://localhost:8082/docs`.

### 5.1. Health Check

-   **Endpoint:** `GET /healthz`
-   **Descrição:** Verifica a saúde do M2, incluindo conexões com DB e RabbitMQ.
-   **Resposta de Sucesso (200 OK Exemplo):**
    ```json
    {
        "status": "healthy",
        "version": "1.0.0",
        "database": "healthy",
        "database_message": "Conexão com banco de dados OK",
        "rabbitmq": "healthy",
        "rabbitmq_message": "Conexão com RabbitMQ OK"
    }
    ```
-   **Exemplo `curl`:**
    ```bash
    curl http://localhost:8082/healthz
    ```

### 5.2. Listar Tarefas

-   **Endpoint:** `GET /tasks`
-   **Descrição:** Lista tarefas com filtros opcionais e paginação.
-   **Parâmetros de Query:**
    -   `status` (opcional): `PENDING`, `PROCESSING`, `COMPLETED`, `FAILED`, `RETRY`, `CANCELLED`
    -   `module` (opcional): Nome do módulo (ex: `extractor`)
    -   `limit` (opcional, padrão 100): Número de tarefas a retornar.
    -   `offset` (opcional, padrão 0): Número de tarefas a pular.
-   **Resposta de Sucesso (200 OK Exemplo):**
    ```json
    [
        {
            "job_id": "uuid",
            "module": "extractor",
            "payload": { "...dados do arquivo..." },
            "status": "PENDING",
            "retries": 0,
            "last_error": null,
            "created_at": "timestamp",
            "updated_at": "timestamp",
            "completed_at": null
        }
    ]
    ```
-   **Exemplo `curl` (listar tarefas pendentes do módulo extractor):**
    ```bash
    curl "http://localhost:8082/tasks?status=PENDING&module=extractor&limit=10"
    ```

### 5.3. Obter Estatísticas das Tarefas

-   **Endpoint:** `GET /tasks/stats`
-   **Descrição:** Retorna estatísticas agregadas sobre as tarefas.
-   **Resposta de Sucesso (200 OK Exemplo):**
    ```json
    {
        "total_tasks": 150,
        "pending_tasks": 20,
        "processing_tasks": 5,
        "completed_tasks": 120,
        "failed_tasks": 3,
        "retry_tasks": 2,
        "cancelled_tasks": 0,
        "tasks_by_module": {
            "extractor": 75,
            "translator": 70
        }
    }
    ```
-   **Exemplo `curl`:**
    ```bash
    curl http://localhost:8082/tasks/stats
    ```

### 5.4. Obter Detalhes da Tarefa

-   **Endpoint:** `GET /tasks/{job_id}`
-   **Descrição:** Retorna detalhes de uma tarefa específica.
-   **Resposta de Sucesso (200 OK Exemplo):** (Similar a um item da lista de `/tasks`)
-   **Exemplo `curl`:**
    ```bash
    curl http://localhost:8082/tasks/SEU_JOB_ID
    ```

### 5.5. Criar Tarefa Manualmente

-   **Endpoint:** `POST /tasks`
-   **Descrição:** Cria uma nova tarefa manualmente (útil para testes).
-   **Corpo da Requisição (JSON):**
    ```json
    {
        "module": "extractor",
        "payload": {"file_path": "data/raw/task_id/file.pdf", "original_m1_task_id": "uuid-m1"}
    }
    ```
-   **Resposta de Sucesso (201 Created):**
    ```json
    {
        "job_id": "novo_uuid_gerado",
        "message": "Tarefa criada com sucesso no banco de dados e publicada na fila."
    }
    ```
-   **Exemplo `curl`:**
    ```bash
    curl -X POST -H "Content-Type: application/json" \
    -d '{"module": "extractor", "payload": {"file_path": "test.pdf"}}' \
    http://localhost:8082/tasks
    ```

### 5.6. Cancelar Tarefa

-   **Endpoint:** `PUT /tasks/{job_id}/cancel`
-   **Descrição:** Define o status de uma tarefa para `CANCELLED`.
-   **Resposta de Sucesso (200 OK):** Retorna a tarefa atualizada.
-   **Exemplo `curl`:**
    ```bash
    curl -X PUT http://localhost:8082/tasks/SEU_JOB_ID/cancel
    ```

### 5.7. Tentar Novamente Tarefa Falha

-   **Endpoint:** `PUT /tasks/{job_id}/retry`
-   **Descrição:** Reenfileira uma tarefa com status `FAILED`, mudando seu status para `PENDING` e incrementando o contador de `retries`.
-   **Resposta de Sucesso (200 OK):** Retorna a tarefa atualizada.
-   **Exemplo `curl`:**
    ```bash
    curl -X PUT http://localhost:8082/tasks/SEU_JOB_ID/retry
    ```

### 5.8. Atualizar Status da Tarefa (por Módulos)

-   **Endpoint:** `PUT /tasks/{job_id}/status`
-   **Descrição:** Usado por módulos de processamento (M3, M4, etc.) para atualizar o status de uma tarefa.
-   **Corpo da Requisição (JSON):**
    ```json
    {
        "status": "PROCESSING", // ou COMPLETED, FAILED
        "last_error": "mensagem de erro se status for FAILED" // opcional
    }
    ```
-   **Resposta de Sucesso (200 OK):** Retorna a tarefa atualizada.

### 5.9. Atualização Parcial da Tarefa (PATCH)

-   **Endpoint:** `PATCH /tasks/{job_id}`
-   **Descrição:** Permite atualizar campos específicos de uma tarefa.
-   **Corpo da Requisição (JSON, exemplo):**
    ```json
    {
        "status": "COMPLETED",
        "last_error": null 
    }
    ```
-   **Resposta de Sucesso (200 OK):** Retorna a tarefa atualizada.

## 6. Configurações Importantes

Variáveis de ambiente (geralmente em `.env`):
-   **RabbitMQ:**
    -   `RABBITMQ_HOST`
    -   `RABBITMQ_PORT`
    -   `RABBITMQ_USER`
    -   `RABBITMQ_PASS`
    -   `DRIVE_INGEST_TOPIC` (default: `drive.ingest`) - Tópico que o M2 consome.
    -   `TASKS_QUEUE_TOPIC` (default: `tasks.queue`) - Tópico para onde o M2 publica.
-   **PostgreSQL:**
    -   `POSTGRES_USER`
    -   `POSTGRES_PASSWORD`
    -   `POSTGRES_DB`
    -   `POSTGRES_HOST`
    -   `POSTGRES_PORT`

## 7. Importância para o Sistema

O M2 Task Orchestrator é vital para o fluxo de trabalho do sistema. Ele desacopla o M1 (ingestão) dos vários módulos de processamento (M3, M4, etc.). Ele centraliza a lógica de criação e rastreamento de tarefas, permitindo que o sistema saiba o que precisa ser feito, qual o próximo passo e o status de cada etapa do processamento de um arquivo. Sem o M2, não haveria uma maneira organizada de passar os arquivos baixados pelo pipeline de processamento.

## 8. Interdependências

-   **Consome de:**
    -   RabbitMQ (tópico `drive.ingest`): Recebe mensagens do M1.
-   **Produz para:**
    -   RabbitMQ (tópico `tasks.queue`): Envia tarefas para os módulos de processamento (M3, M4, etc.).
    -   Banco de Dados PostgreSQL: Cria e atualiza registros na tabela `tasks`.
-   **Depende de:**
    -   RabbitMQ: Para comunicação baseada em mensagens.
    -   PostgreSQL: Para persistência do estado das tarefas.
-   **Utilizado por:**
    -   Módulos de processamento (M3, M4, etc.): Para atualizar o status das tarefas via API (`PUT /tasks/{job_id}/status`).
    -   M8 API Gateway: Para expor a API de gerenciamento de tarefas.
    -   Interface de Monitoramento (ex: `m1_drive_connector/static/m2_monitor.html`): Para visualizar o status das tarefas.
