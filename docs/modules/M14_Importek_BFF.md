# M14: Importek BFF (Backend-For-Frontend)

## 1. Propósito do Módulo

O Módulo 14 (M14) - Importek BFF atua como um Backend-For-Frontend especializado, servindo como uma camada intermediária inteligente entre o ecossistema de microsserviços backend e interfaces de usuário. Sua principal responsabilidade é agregar, transformar e otimizar dados de múltiplos microsserviços através do M8 API Gateway e integração direta com serviços quando necessário, fornecendo endpoints específicos e otimizados para diferentes contextos de uso.

**Características Principais:**
- **Integração Híbrida:** Utiliza M8 API Gateway como rota primária com fallback inteligente para comunicação direta com microsserviços (ex: M2 Task Orchestrator)
- **Arquitetura Resiliente:** Implementa tolerância a falhas e recuperação automática quando serviços intermediários estão indisponíveis
- **Especialização por Contexto:** Oferece APIs otimizadas para Kanban, Diff Viewer, Dashboard, Projetos, Tradução, Checklist, Dossiê e Portal do Fornecedor
- **Dados Reais:** Consome e transforma dados reais do pipeline de processamento, eliminando dependências de dados mock
- **Comunicação Bidirecional:** Suporte a WebSocket para atualizações em tempo real

## 2. Funcionalidades Chave

-   **Agregação de Dados de Dashboard:** Coleta métricas em tempo real de múltiplos serviços via M8 e fornece um endpoint consolidado (`/api/dashboard/metrics`) com dados como total de documentos, tarefas ativas de tradução, saúde geral do sistema, documentos processados hoje e tempo médio de processamento.
-   **HTTPClientService:** Serviço interno assíncrono que realiza chamadas HTTP otimizadas para o M8 API Gateway, implementando connection pooling, timeout management e tratamento robusto de erros.
-   **WebSocket Server:** Servidor de WebSocket para comunicação bidirecional em tempo real com o frontend, incluindo gerenciamento de conexões, broadcasting de eventos e notificações push.
-   **Endpoints Especializados por Contexto:**
    -   **Projetos:** Gerenciamento de projetos de importação e processamento
    -   **Kanban Tasks:** Interface otimizada para visualização de tarefas em formato Kanban
    -   **Diff Viewer:** Endpoints para visualização de diferenças entre documentos
    -   **Translation:** Funcionalidades específicas para gerenciamento de traduções
    -   **Checklist:** Sistema de checklist para validação de processos
    -   **Dossiê:** Agregação de informações em formato de dossiê
    -   **Supplier Portal:** Interface especializada para fornecedores
-   **Otimização de Performance:** Cache inteligente, compressão de respostas e agregação eficiente de dados para reduzir latência entre frontend e backend.
-   **Transformação de Dados:** Converte estruturas de dados dos microsserviços backend em formatos otimizados para consumo pelo frontend React.
-   **Interface de Monitoramento:** Serve uma UI de monitoramento estática em `/monitor` para visualização das métricas do dashboard.

## 3. Entradas e Saídas

-   **Entradas:**
    -   Requisições HTTP do M9 Frontend UI (quando implementado) e ferramentas de desenvolvimento
    -   Dados agregados do M8 API Gateway provenientes de M1, M2, M5, M6, M7
    -   Conexões WebSocket de clientes frontend
    -   Variáveis de Ambiente: URL do M8 API Gateway, configurações de cache e timeouts

-   **Saídas:**
    -   Respostas JSON otimizadas e formatadas especificamente para o frontend
    -   Métricas de dashboard em tempo real via endpoint REST
    -   Eventos em tempo real via WebSocket para atualizações live da UI
    -   Interface de monitoramento estática servida em `/monitor`

## 4. Principais Componentes Internos

-   `app/main.py`: Aplicação FastAPI principal que define endpoints de dashboard, health check e monta os routers especializados e arquivos estáticos.
-   `app/services/http_client_service.py`: Classe `HTTPClientService` que gerencia conexões HTTP assíncronas com o M8, implementa métodos para buscar métricas específicas e realiza transformação de dados.
-   `app/routers/`: Diretório contendo routers especializados por contexto:
    -   `projects.py`: Endpoints para gerenciamento de projetos
    -   `kanban_tasks.py`: APIs otimizadas para interface Kanban
    -   `diff_viewer.py`: Endpoints para visualização de diferenças
    -   `translation.py`: Funcionalidades de tradução
    -   `checklist.py`: Sistema de checklist
    -   `dossier.py`: Agregação de dossiês
    -   `supplier_portal.py`: Portal do fornecedor
    -   `websockets.py`: Gerenciamento de conexões WebSocket
-   `app/websockets/connection_manager.py`: Classe `ConnectionManager` para gerenciar conexões WebSocket ativas, broadcasting e cleanup de conexões.
-   `app/models/`: Modelos Pydantic especializados para cada contexto de uso, garantindo validação e serialização consistente de dados.
-   `app/static/monitor/`: Interface de monitoramento estática com dashboard visual das métricas do sistema.

## 5. Endpoints da API

A API do M14 é acessível em `http://localhost:8090` (quando rodando localmente via Docker Compose).
A documentação interativa da API (Swagger UI) está disponível em `http://localhost:8090/docs`.

### 5.1. Dashboard de Métricas (Implementado - Tasks 36.1 e 36.2)

-   **Endpoint:** `GET /api/dashboard/metrics`
-   **Descrição:** Retorna métricas agregadas em tempo real do sistema, coletadas de múltiplos serviços via M8.
-   **Resposta de Sucesso (200 OK):**
    ```json
    {
        "total_documents": 156,
        "documents_processed_today": 23,
        "active_translation_tasks": 7,
        "system_health_overall": "healthy",
        "average_processing_time_minutes": 4.2
    }
    ```
-   **Tratamento de Erros:** Retorna "Error" para métricas específicas se o serviço correspondente estiver indisponível.
-   **Exemplo `curl`:**
    ```bash
    curl http://localhost:8090/api/dashboard/metrics
    ```

### 5.2. Health Check

-   **Endpoint:** `GET /healthz`
-   **Descrição:** Verifica a saúde do serviço M14 BFF.
-   **Resposta de Sucesso (200 OK):**
    ```json
    {
        "status": "ok",
        "module": "m14_importek_bff"
    }
    ```

### 5.3. Tarefas Kanban (✅ Implementado - Task 35.3)

-   **Endpoint Base:** `/api/kanban` e `/api/projects/{id}/tasks`
-   **Descrição:** APIs otimizadas para interface Kanban com integração real ao M2 Task Orchestrator.
-   **Funcionalidades:**
    -   **GET `/api/kanban/task-types`:** Lista tipos de tarefas disponíveis (Import, Triar, Traduzir, Checklist, Dossiê)
    -   **GET `/api/projects/{id}/tasks`:** Busca tarefas de um projeto específico via M8→M2
    -   **POST `/api/projects/{id}/tasks`:** Cria nova tarefa com fallback inteligente (M8 → M2 direto)
    -   **PATCH `/api/kanban/tasks/{id}/status`:** Atualiza status de tarefa com mapeamento Kanban↔M2
-   **Integração Real:** Consome dados reais do M2 Task Orchestrator sem dependência de mocks
-   **Arquitetura Resiliente:** Fallback automático M8 → M2 direto quando M8 não suporta endpoint
-   **Mapeamentos Implementados:**
    -   Kanban Status → M2 Modules: `"Import" → "extractor"`, `"Traduzir" → "translator"`
    -   Status Bidirecionais: 15+ mapeamentos entre Kanban e M2 (PENDING, PROCESSING, COMPLETED, etc.)
-   **Exemplo de Uso:**
    ```bash
    # Listar tipos de tarefas
    curl http://localhost:8090/api/kanban/task-types
    
    # Buscar tarefas de um projeto
    curl http://localhost:8090/api/projects/ad1bfa7f-3fe7-4af8-b042-8cddf55ac320/tasks
    
    # Criar nova tarefa
    curl -X POST http://localhost:8090/api/projects/{id}/tasks \
      -H "Content-Type: application/json" \
      -d '{"title": "Nova Tarefa", "status": "Import", "assignee": "User"}'
    ```

### 5.4. Visualizador de Diferenças (✅ Implementado - Task 35.3)

-   **Endpoint Base:** `/api/diff-viewer` e `/api/projects/{id}/document_pairs`
-   **Descrição:** Sistema completo para visualização de diferenças entre documentos com integração M7 Diff Engine.
-   **Funcionalidades:**
    -   **GET `/api/projects/{id}/document_pairs`:** Lista pares de documentos disponíveis para comparação
    -   **GET `/api/diff-viewer/versions/{id}`:** Obtém dados detalhados de diff para uma versão específica
    -   **PATCH `/api/document_versions/{id}`:** Marca versão como final com fallback para dados mock
-   **Integração com M7:** Utiliza M7 Diff Engine via M8 Gateway para processamento de diferenças
-   **Fallback Inteligente:** Retorna dados mock quando serviços backend estão indisponíveis
-   **Estrutura de Dados:**
    ```json
    {
      "pair_id": "uuid",
      "document_id": "uuid",
      "version1_id": "uuid",
      "version2_id": "uuid",
      "diff_summary": {"added_lines": 245, "deleted_lines": 12},
      "sections": [{"section_id": "intro", "status": "modified"}]
    }
    ```
-   **Exemplo de Uso:**
    ```bash
    # Listar pares de documentos
    curl http://localhost:8090/api/projects/{id}/document_pairs
    
    # Obter dados de diff
    curl http://localhost:8090/api/diff-viewer/versions/{version_id}
    
    # Marcar versão como final
    curl -X PATCH http://localhost:8090/api/document_versions/{id} \
      -H "Content-Type: application/json" \
      -d '{"is_final": true}'
    ```

### 5.5. Projetos (Parcialmente Implementado)

-   **Endpoint Base:** `/api/projects`
-   **Descrição:** Endpoints para gerenciamento de projetos integrados com sistema Kanban e Diff Viewer.
-   **Status:** Implementado via endpoints de tarefas e documentos, expansão planejada.

### 5.6. Tradução (✅ Implementado - Task 35.4)

-   **Endpoint Base:** `/api/projects/{id}/translation_queue` e `/api/projects/{id}/layout_translate`
-   **Descrição:** Sistema completo para gerenciamento de traduções com integração M4 Translator.
-   **Funcionalidades:**
    -   **GET `/api/projects/{id}/translation_queue`:** Obtém fila de tradução para um projeto específico
    -   **POST `/api/projects/{id}/layout_translate`:** Solicita tradução de documento com opções de layout
-   **Integração com M4:** Utiliza M4 Translator via M8 Gateway para processamento de traduções
-   **Fallback Inteligente:** Retorna dados mock quando serviços backend estão indisponíveis
-   **Exemplo de Uso:**
    ```bash
    # Obter fila de tradução
    curl http://localhost:8090/api/projects/{id}/translation_queue
    
    # Solicitar tradução
    curl -X POST http://localhost:8090/api/projects/{id}/layout_translate \
      -H "Content-Type: application/json" \
      -d '{"document_id": "uuid", "layout_type": "technical_manual", "priority": 5}'
    ```

### 5.7. Checklist (✅ Implementado - Task 35.4)

-   **Endpoint Base:** `/api/projects/{id}/checklist`
-   **Descrição:** Sistema completo de checklist regulatório integrado com M12 Reg-Extractor.
-   **Funcionalidades:**
    -   **GET `/api/projects/{id}/checklist`:** Obtém checklist regulatório completo para um projeto
-   **Integração com M12:** Utiliza M12 Reg-Extractor via M8 Gateway para análise regulatória
-   **Estrutura de Dados:**
    ```json
    {
      "project_id": "uuid",
      "checklist_id": "ISO_13485_V2023",
      "overall_coverage_percentage": 87.3,
      "items_ok_count": 142,
      "items_attention_count": 18,
      "items_critical_count": 3,
      "items": [{"id": "iso_4.1.1", "status": "verde", "name": "Requisitos Gerais"}]
    }
    ```
-   **Exemplo de Uso:**
    ```bash
    # Obter checklist regulatório
    curl http://localhost:8090/api/projects/ad1bfa7f-3fe7-4af8-b042-8cddf55ac320/checklist
    ```

### 5.8. Dossiê (✅ Implementado - Task 35.4)

-   **Endpoint Base:** `/api/projects/{id}/dossier/timeline`, `/api/projects/{id}/dossier/build`
-   **Descrição:** Sistema completo para agregação e construção de dossiês integrado com M13 Dossier Builder.
-   **Funcionalidades:**
    -   **GET `/api/projects/{id}/dossier/timeline`:** Obtém histórico de builds de dossiê
    -   **POST `/api/projects/{id}/dossier/build`:** Inicia nova construção de dossiê
    -   **GET `/api/projects/{id}/dossier/builds/{id}/download`:** Download de dossiê construído
-   **Integração com M13:** Utiliza M13 Dossier Builder via M8 Gateway para construção de dossiês
-   **Estrutura de Timeline:**
    ```json
    [{
      "build_id": "uuid",
      "status": "completed",
      "started_at": "2025-05-29T14:54:29",
      "completed_at": "2025-05-29T15:54:29",
      "download_url": "/api/projects/{id}/dossier/builds/{id}/download"
    }]
    ```
-   **Exemplo de Uso:**
    ```bash
    # Obter timeline de builds
    curl http://localhost:8090/api/projects/{id}/dossier/timeline
    
    # Iniciar novo build
    curl -X POST http://localhost:8090/api/projects/{id}/dossier/build \
      -H "Content-Type: application/json" \
      -d '{"include_translations": true, "format": "pdf"}'
    ```

### 5.9. Portal do Fornecedor (Placeholder)

-   **Endpoint Base:** `/api/supplier`
-   **Descrição:** Interface especializada para fornecedores.
-   **Status:** Implementação planejada nas próximas iterações.

### 5.10. WebSocket

-   **Endpoint:** `WS /ws`
-   **Descrição:** Conexão WebSocket para comunicação em tempo real com o frontend.
-   **Funcionalidades:** Broadcasting de eventos, notificações push, atualizações live de métricas.
-   **Status:** Implementação planejada nas próximas iterações.

### 5.11. Interface de Monitoramento

-   **Endpoint:** `GET /monitor`
-   **Descrição:** Serve interface estática de monitoramento do dashboard.
-   **Acesso:** `http://localhost:8090/monitor`

## 6. Configurações Importantes

Variáveis de ambiente essenciais:

-   `M8_API_GATEWAY_URL`: URL do M8 API Gateway (padrão: `http://mastigador_m8_api_gateway:8080`)
-   `HTTPX_TIMEOUT`: Timeout para requisições HTTP em segundos (padrão: `30.0`)
-   `CACHE_TTL`: Time-to-live para cache de dados em segundos
-   `WEBSOCKET_HEARTBEAT_INTERVAL`: Intervalo de heartbeat para WebSocket em segundos
-   `LOG_LEVEL`: Nível de logging (INFO, DEBUG, WARNING, ERROR)

## 7. Importância para o Sistema

O M14 é crucial como camada de abstração entre o complexo ecossistema de microsserviços backend e o frontend React. Ele:

-   **Simplifica o Frontend:** Reduz a complexidade do M9 ao fornecer endpoints agregados e otimizados
-   **Melhora Performance:** Implementa cache inteligente e reduz o número de requisições do frontend
-   **Facilita Manutenção:** Centraliza a lógica de integração com backend, facilitando mudanças futuras
-   **Habilita Tempo Real:** Fornece capacidades WebSocket para atualizações live da interface
-   **Otimiza UX:** Transforma dados backend em formatos ideais para componentes React específicos
-   **Monitora Sistema:** Oferece visibilidade em tempo real do estado e performance do sistema

## 8. Interdependências

-   **Consome de:**
    -   M8 API Gateway: Todas as métricas e dados do sistema via endpoints REST
    -   Indiretamente via M8: M1 (documentos), M2 (tarefas), M5 (dados), M6 (busca), M7 (diferenças)

-   **Utilizado por:**
    -   M9 Frontend UI (futuro): Consumidor principal de todos os endpoints
    -   Ferramentas de desenvolvimento e monitoramento
    -   Scripts de automação e integração

-   **Produz para:**
    -   Interface de usuário otimizada via endpoints REST especializados
    -   Eventos em tempo real via WebSocket
    -   Métricas agregadas para dashboards de monitoramento

## 9. Status de Implementação

### ✅ Concluído (Tasks 35.3, 35.4, 36.1 e 36.2)

**Dashboard e Métricas:**
-   ✅ Estrutura base FastAPI e roteamento
-   ✅ HTTPClientService com dados reais do sistema
-   ✅ Endpoint `/api/dashboard/metrics` funcional
-   ✅ Integração com M8 API Gateway
-   ✅ Métricas: total de documentos, documentos processados hoje, tarefas ativas, saúde do sistema, tempo médio de processamento
-   ✅ Tratamento robusto de erros e logging
-   ✅ Interface de monitoramento estática

**Sistema Kanban:**
-   ✅ APIs completas para gerenciamento de tarefas Kanban
-   ✅ Integração real com M2 Task Orchestrator (sem mocks)
-   ✅ Fallback inteligente M8 → M2 direto
-   ✅ Mapeamentos bidirecionais Status Kanban ↔ M2
-   ✅ Criação, listagem e atualização de tarefas funcionais
-   ✅ Modelos Pydantic especializados para tarefas

**Diff Viewer:**
-   ✅ Sistema completo de visualização de diferenças
-   ✅ Integração com M7 Diff Engine via M8 Gateway
-   ✅ Endpoints para pares de documentos e versões
-   ✅ Fallback para dados mock quando serviços indisponíveis
-   ✅ Modelos Pydantic para estruturas de diff

**Translation, Checklist e Dossier (Task 35.4):**
-   ✅ **Translation APIs:** Sistema completo de gerenciamento de traduções
    -   ✅ Integração com M4 Translator via M8 Gateway
    -   ✅ Endpoints para fila de tradução e solicitação de tradução
    -   ✅ Fallback inteligente quando serviços indisponíveis
-   ✅ **Checklist System:** APIs para sistema de checklist regulatório
    -   ✅ Integração com M12 Reg-Extractor via M8 Gateway
    -   ✅ Endpoint para obter checklist completo de projetos
    -   ✅ Estruturas de dados detalhadas com status e métricas
-   ✅ **Dossier Aggregation:** Sistema de construção de dossiês
    -   ✅ Integração com M13 Dossier Builder via M8 Gateway
    -   ✅ Timeline de builds, criação de novos builds e download
    -   ✅ Gerenciamento de status e histórico de builds

**Validação e Testes:**
-   ✅ Scripts de validação específicos para cada funcionalidade
-   ✅ `scripts/validate_task_35_4_apis.py` - Validação completa das APIs Task 35.4
-   ✅ Testes de integração com dados reais
-   ✅ Documentação técnica detalhada e atualizada

### 🚧 Próximas Implementações (Tasks 35.5-35.7)
-   🔄 **WebSocket Server:** Comunicação em tempo real
-   🔄 **Supplier Portal:** Interface especializada para fornecedores
-   🔄 **Frontend Monitoring:** Interface avançada de monitoramento
-   🔄 **Authentication & Authorization:** Sistema de autenticação
-   🔄 **Advanced Caching:** Cache inteligente e otimizações

### 📋 Roadmap Futuro
-   Autenticação e autorização
-   Rate limiting e throttling
-   Cache inteligente e otimizações de performance
-   Métricas avançadas e analytics
-   Integração com sistema de notificações
-   Logs estruturados e observabilidade completa

### 🧪 Scripts de Validação

**Localização:** `m14_importek_bff/scripts/`

-   `test_integration_real_data.py`: Teste completo de integração com dados reais
-   `validate_kanban_endpoints.py`: Validação específica do sistema Kanban
-   `validate_diff_viewer.py`: Validação do Diff Viewer
-   `validate_dashboard_metrics.py`: Validação das métricas de dashboard
-   `validate_task_35_4_apis.py`: Validação completa das APIs Translation, Checklist e Dossier

**Execução:**
```bash
# Teste completo do sistema
cd m14_importek_bff
python scripts/test_integration_real_data.py

# Validação das APIs Task 35.4 (Translation, Checklist, Dossier)
python scripts/validate_task_35_4_apis.py

# Testes específicos por funcionalidade
python scripts/validate_kanban_endpoints.py
python scripts/validate_diff_viewer.py
python scripts/validate_dashboard_metrics.py
```
