# M10: AI Orchestrator (RAG)

## 1. Propósito do Módulo

O M10 AI Orchestrator é o cérebro inteligente do sistema, responsável por interpretar consultas em linguagem natural, recuperar informações relevantes de múltiplas fontes de dados e gerar respostas precisas e contextualizadas usando Large Language Models (LLM). Este módulo implementa a arquitetura RAG (Retrieval-Augmented Generation), combinando busca semântica avançada com geração de texto por IA para fornecer respostas inteligentes sobre documentos regulatórios e técnicos.

**Características Principais:**
- **Processamento de Linguagem Natural:** Interpreta consultas complexas em português e outros idiomas
- **Busca Semântica Híbrida:** Combina busca vetorial (M6) com metadados estruturados (M5) e análise de diferenças (M7)
- **Contextualização Inteligente:** Monta contexto relevante agregando informações de múltiplas fontes
- **Geração Aumentada por Recuperação:** Utiliza GPT-4o para gerar respostas precisas com citações verificáveis
- **Rastreabilidade Completa:** Mantém ligação entre respostas e fontes originais para auditoria
- **Processamento Multimodal:** Suporte a textos, tabelas, diagramas e layouts complexos

## 2. Funcionalidades Chave

### 2.1. Query Processor (Processador de Consultas)
- **Análise de Intenção:** Identifica o tipo de consulta (busca factual, comparação, síntese, análise regulatória)
- **Extração de Entidades:** Reconhece produtos, regulamentações, datas, requisitos e conceitos técnicos
- **Classificação de Complexidade:** Determina estratégia de busca baseada na complexidade da consulta
- **Preprocessamento de Linguagem:** Normalização, correção ortográfica e expandir abreviações técnicas

### 2.2. Context Assembler (Montador de Contexto)
- **Busca Vetorial Semântica:** Integração com M6 Vector Indexer para recuperação por similaridade
- **Busca por Metadados:** Consultas estruturadas no M5 para informações específicas (datas, tipos, status)
- **Análise de Diferenças:** Integração com M7 Diff Engine para comparações entre versões
- **Agregação Inteligente:** Combina resultados de múltiplas fontes em contexto coerente
- **Filtragem de Relevância:** Remove informações redundantes ou irrelevantes do contexto

### 2.3. LLM Engine (Motor de LLM)
- **Integração GPT-4o:** Configuração otimizada para documentos técnicos e regulatórios
- **Prompt Engineering:** Templates especializados para diferentes tipos de consulta
- **Controle de Temperatura:** Ajuste automático para balancear criatividade e precisão
- **Validação de Respostas:** Verificação de consistência e aderência ao contexto fornecido
- **Gestão de Tokens:** Otimização de uso de tokens para consultas complexas

### 2.4. Response Formatter (Formatador de Respostas)
- **Estruturação de Respostas:** Organização em seções lógicas com hierarquia clara
- **Sistema de Citações:** Referências precisas aos documentos e seções originais
- **Formatação para UI:** Adaptação para exibição em interfaces web e mobile
- **Suporte a Formatos:** Texto, listas, tabelas, links e elementos visuais
- **Metadados de Resposta:** Confiabilidade, fontes consultadas, data de processamento

### 2.5. Cache Inteligente
- **Cache de Consultas:** Armazenamento de respostas para consultas frequentes
- **Cache de Contexto:** Reutilização de contextos montados para consultas similares
- **Invalidação Automática:** Atualização quando documentos são modificados
- **Estratégias de TTL:** Diferentes tempos de vida baseados no tipo de informação

## 3. Entradas e Saídas

### 3.1. Entradas
- **Consultas de Usuário:** Perguntas em linguagem natural via M14 BFF ou APIs diretas
- **Parâmetros de Busca:** Filtros de projeto, tipo de documento, período temporal
- **Configurações de Consulta:** Nível de detalhe, idioma preferido, formato de resposta
- **Dados de M6:** Embeddings vetoriais e resultados de busca semântica
- **Dados de M5:** Metadados estruturados, taxonomias e informações de documentos
- **Dados de M7:** Análises de diferenças e comparações entre versões
- **Variáveis de Ambiente:** Configurações de LLM, chaves de API, parâmetros de busca

### 3.2. Saídas
- **Respostas Estruturadas:** JSON com resposta, citações, metadados e confiabilidade
- **Respostas Formatadas:** HTML/Markdown para exibição em interfaces de usuário
- **Logs de Auditoria:** Registro completo do processo de geração de cada resposta
- **Métricas de Performance:** Tempos de resposta, qualidade das respostas, uso de recursos
- **Notificações de Erro:** Alertas para problemas na recuperação de informações ou geração

## 4. Principais Componentes Internos

### 4.1. Aplicação Principal
- `app/main.py`: Aplicação FastAPI com endpoints de consulta, configuração e monitoramento
- `app/config.py`: Configurações do módulo carregadas de variáveis de ambiente
- `app/models/`: Modelos Pydantic para requisições, respostas e estruturas de dados internas

### 4.2. Processamento de Consultas
- `app/query/processor.py`: Análise e classificação de consultas de entrada
- `app/query/intent_classifier.py`: Identificação de intenção usando modelos de NLP
- `app/query/entity_extractor.py`: Extração de entidades e conceitos técnicos
- `app/query/query_optimizer.py`: Otimização de consultas para melhor recuperação

### 4.3. Recuperação de Contexto
- `app/retrieval/context_assembler.py`: Orquestração da busca em múltiplas fontes
- `app/retrieval/vector_search.py`: Interface com M6 Vector Indexer
- `app/retrieval/metadata_search.py`: Interface com M5 DB Browser
- `app/retrieval/diff_analyzer.py`: Interface com M7 Diff Engine
- `app/retrieval/relevance_scorer.py`: Pontuação e ranqueamento de resultados

### 4.4. Geração com LLM
- `app/llm/gpt_client.py`: Cliente otimizado para OpenAI GPT-4o
- `app/llm/prompt_templates.py`: Templates de prompt para diferentes tipos de consulta
- `app/llm/response_validator.py`: Validação de qualidade e consistência das respostas
- `app/llm/token_manager.py`: Gestão eficiente de tokens e custos

### 4.5. Formatação e Cache
- `app/formatting/response_formatter.py`: Formatação de respostas para diferentes interfaces
- `app/formatting/citation_manager.py`: Sistema de citações e referências
- `app/cache/intelligent_cache.py`: Cache multi-camada com invalidação automática
- `app/cache/context_cache.py`: Cache especializado para contextos montados

### 4.6. Integração e Monitoramento
- `app/integrations/m6_client.py`: Cliente para comunicação com M6 Vector Indexer
- `app/integrations/m5_client.py`: Cliente para comunicação com M5 DB Browser
- `app/integrations/m7_client.py`: Cliente para comunicação com M7 Diff Engine
- `app/monitoring/metrics_collector.py`: Coleta de métricas de performance e qualidade
- `app/monitoring/audit_logger.py`: Logging detalhado para auditoria e debugging

## 5. Endpoints da API

### 5.1. Consultas Principais

#### **POST /query**
- **Descrição:** Endpoint principal para consultas em linguagem natural
- **Request Body:**
  ```json
  {
    "query": "Quais são os requisitos de segurança para dispositivos médicos classe II?",
    "project_id": "uuid-optional",
    "response_format": "structured|markdown|html",
    "language": "pt-BR",
    "max_results": 10,
    "include_sources": true
  }
  ```
- **Response (200 OK):**
  ```json
  {
    "response": "Os requisitos de segurança para dispositivos médicos classe II incluem...",
    "sources": [
      {
        "document_id": "uuid",
        "document_name": "ISO 13485 - Requisitos Regulatórios",
        "section": "4.2.3",
        "relevance_score": 0.95,
        "excerpt": "Texto relevante do documento..."
      }
    ],
    "confidence_score": 0.87,
    "processing_time_ms": 1250,
    "query_id": "uuid",
    "timestamp": "2025-05-29T15:00:00Z"
  }
  ```

#### **POST /query/compare**
- **Descrição:** Comparação inteligente entre documentos ou versões
- **Request Body:**
  ```json
  {
    "query": "Compare os requisitos entre versão 2022 e 2023 da norma ISO 13485",
    "document_ids": ["uuid1", "uuid2"],
    "comparison_type": "requirements|changes|additions|deletions"
  }
  ```

#### **POST /query/summarize**
- **Descrição:** Sumarização inteligente de documentos ou seções
- **Request Body:**
  ```json
  {
    "query": "Resuma os principais riscos identificados no documento",
    "document_id": "uuid",
    "summary_length": "brief|detailed|comprehensive",
    "focus_areas": ["safety", "compliance", "technical"]
  }
  ```

### 5.2. Configuração e Controle

#### **GET /health**
- **Descrição:** Verificação de saúde do M10 e suas dependências
- **Response:** Status de conexão com M6, M5, M7 e serviços de LLM

#### **GET /config**
- **Descrição:** Configurações atuais do sistema
- **Response:** Modelos ativos, parâmetros de busca, configurações de cache

#### **POST /config/update**
- **Descrição:** Atualização de configurações em runtime
- **Request Body:** Novos parâmetros de configuração

### 5.3. Monitoramento e Métricas

#### **GET /metrics**
- **Descrição:** Métricas de performance e uso
- **Response:**
  ```json
  {
    "queries_today": 156,
    "average_response_time_ms": 1200,
    "cache_hit_rate": 0.73,
    "confidence_score_avg": 0.85,
    "active_connections": 12,
    "llm_tokens_used_today": 125000
  }
  ```

#### **GET /audit/queries**
- **Descrição:** Log de auditoria de consultas (com filtros)
- **Query Parameters:** `start_date`, `end_date`, `user_id`, `project_id`

### 5.4. Cache e Otimização

#### **DELETE /cache/clear**
- **Descrição:** Limpeza manual de cache
- **Query Parameters:** `type=all|queries|context|embeddings`

#### **POST /cache/warm**
- **Descrição:** Pré-aquecimento de cache com consultas frequentes

## 6. Configurações Importantes

### 6.1. Variáveis de Ambiente Principais

```bash
# Configurações de LLM
OPENAI_API_KEY=sk-...
OPENAI_MODEL=gpt-4o
OPENAI_MAX_TOKENS=4000
OPENAI_TEMPERATURE=0.3

# Configurações de Busca
MAX_SEARCH_RESULTS=20
SIMILARITY_THRESHOLD=0.7
CONTEXT_MAX_TOKENS=8000
RESPONSE_MAX_TOKENS=1000

# Configurações de Cache
CACHE_TTL_SECONDS=3600
CACHE_MAX_SIZE_MB=500
ENABLE_CONTEXT_CACHE=true

# URLs de Integração
M2_API_URL=http://m2_task_orchestrator:8082  # IMPORTANTE: Use o nome do serviço Docker, não localhost
M4_API_URL=http://m4_translator:8084         # IMPORTANTE: Use o nome do serviço Docker, não localhost
M5_DB_BROWSER_URL=http://m5_db_browser:8085  # Use o nome do serviço Docker em ambiente containerizado
M6_VECTOR_INDEXER_URL=http://m6_vector_indexer:8086  # Use o nome do serviço Docker em ambiente containerizado
M7_DIFF_ENGINE_URL=http://m7_diff_engine:8087  # Use o nome do serviço Docker em ambiente containerizado

# Configurações de Performance
MAX_CONCURRENT_QUERIES=10
QUERY_TIMEOUT_SECONDS=30
ENABLE_ASYNC_PROCESSING=true

# Logging e Auditoria
LOG_LEVEL=INFO
ENABLE_AUDIT_LOGGING=true
AUDIT_RETENTION_DAYS=90
```

### 6.2. Configurações Avançadas

- **Prompt Templates:** Configuráveis por tipo de consulta e domínio
- **Scoring Weights:** Pesos para combinação de diferentes fontes de dados
- **Language Detection:** Configuração de detecção automática de idioma
- **Response Filtering:** Filtros de qualidade e relevância para respostas

## 7. Arquitetura e Fluxo de Dados

### 7.1. Diagrama de Arquitetura

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   M9 Frontend   │───▶│   M14 BFF      │───▶│  M10 AI Orch.   │
│   (React UI)    │    │   (REST API)   │    │   (RAG Engine)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                                              ┌─────────▼─────────┐
                                              │ Query Processor   │
                                              │ - Intent Analysis │
                                              │ - Entity Extract  │
                                              └─────────┬─────────┘
                                                        │
                                              ┌─────────▼─────────┐
                                              │ Context Assembler │
                                              └─────────┬─────────┘
                                                        │
                     ┌──────────────────────────────────┼──────────────────────────────────┐
                     │                                  │                                  │
           ┌─────────▼─────────┐              ┌─────────▼─────────┐              ┌─────────▼─────────┐
           │   M6 Vector       │              │   M5 DB          │              │   M7 Diff        │
           │   Indexer         │              │   Browser        │              │   Engine         │
           │ (Semantic Search) │              │ (Metadata)       │              │ (Comparisons)    │
           └─────────┬─────────┘              └─────────┬─────────┘              └─────────┬─────────┘
                     │                                  │                                  │
                     └──────────────────────────────────┼──────────────────────────────────┘
                                                        │
                                              ┌─────────▼─────────┐
                                              │   LLM Engine      │
                                              │ - GPT-4o Client   │
                                              │ - Prompt Templates│
                                              │ - Response Valid. │
                                              └─────────┬─────────┘
                                                        │
                                              ┌─────────▼─────────┐
                                              │Response Formatter │
                                              │ - Citations       │
                                              │ - UI Formatting   │
                                              │ - Audit Logging   │
                                              └───────────────────┘
```

### 7.2. Fluxo de Processamento

1. **Recepção de Consulta:** M14 BFF encaminha consulta do usuário para M10
2. **Análise de Intenção:** Query Processor analisa e classifica a consulta
3. **Busca Multi-fonte:** Context Assembler consulta M6, M5 e M7 em paralelo
4. **Agregação de Contexto:** Resultados são combinados e filtrados por relevância
5. **Geração com LLM:** GPT-4o gera resposta baseada no contexto montado
6. **Formatação e Citação:** Response Formatter estrutura a resposta final
7. **Cache e Auditoria:** Resposta é armazenada em cache e logada para auditoria
8. **Retorno ao Usuário:** M14 BFF recebe e encaminha resposta para o frontend

## 8. Casos de Uso Práticos

### 8.1. Consultas Factuais
- **"Qual é a dose máxima permitida do ingrediente ativo X?"**
  - Busca semântica em documentos técnicos
  - Extração de informações específicas sobre dosagem
  - Citação precisa das fontes regulatórias

### 8.2. Análise Comparativa
- **"Compare os requisitos de validação entre FDA e EMA"**
  - Recuperação de documentos de ambas as agências
  - Análise de diferenças usando M7 Diff Engine
  - Síntese das principais divergências e semelhanças

### 8.3. Sumarização Complexa
- **"Resuma os riscos de segurança identificados em todos os estudos clínicos"**
  - Agregação de informações de múltiplos documentos
  - Categorização automática por tipo de risco
  - Síntese com priorização por severidade

### 8.4. Consultas Regulatórias
- **"Quais documentos são necessários para submissão 510(k)?"**
  - Busca em guidelines regulatórios
  - Extração de listas de requisitos
  - Referência a versões mais recentes das normas

### 8.5. Análise Temporal
- **"Como mudaram os requisitos de biocompatibilidade nos últimos 5 anos?"**
  - Busca temporal em versões históricas
  - Análise de tendências usando M7
  - Síntese de evolução regulatória

## 9. Importância para o Sistema

O M10 AI Orchestrator é o componente que transforma o sistema de "armazenamento inteligente de documentos" em "assistente especialista em regulamentação". Sua importância estratégica inclui:

### 9.1. Para Usuários Finais
- **Democratização do Conhecimento:** Permite que usuários não especialistas acessem informações complexas
- **Eficiência Operacional:** Reduz drasticamente o tempo de busca e análise manual
- **Qualidade das Decisões:** Fornece informações completas e contextualizadas para tomada de decisão
- **Redução de Erros:** Minimiza interpretações incorretas através de citações precisas

### 9.2. Para Compliance e Auditoria
- **Rastreabilidade Completa:** Cada resposta é ligada às fontes originais
- **Consistência de Interpretação:** Respostas baseadas nos mesmos documentos fonte
- **Histórico de Consultas:** Log completo para auditoria e verificação
- **Versionamento de Respostas:** Acompanha mudanças nas fontes de dados

### 9.3. Para o Negócio
- **Aceleração de Processos:** Reduz tempo de desenvolvimento e submissão regulatória
- **Redução de Custos:** Menor dependência de consultores externos especializados
- **Vantagem Competitiva:** Capacidade de análise mais rápida e precisa que concorrentes
- **Escalabilidade:** Atende múltiplos usuários e projetos simultaneamente

### 9.4. Para o Ecossistema Técnico
- **Integração Central:** Utiliza capacidades de todos os módulos anteriores (M1-M7)
- **Preparação para IA:** Base para funcionalidades futuras de IA generativa
- **API Reutilizável:** Permite integração com outras ferramentas e sistemas
- **Observabilidade:** Métricas detalhadas para otimização contínua

## 10. Interdependências

### 10.1. Consome de
- **M2 Task Orchestrator:** Gerenciamento de tarefas e fluxos de trabalho (requer configuração de `M2_API_URL` no docker-compose.yml)
- **M4 Translator:** Serviços de tradução e processamento de idiomas (requer configuração de `M4_API_URL` no docker-compose.yml)
- **M5 DB Browser:** Metadados estruturados e informações de documentos
- **M6 Vector Indexer:** Busca semântica em embeddings de documentos
- **M7 Diff Engine:** Análises de diferenças e comparações entre versões
- **OpenAI API:** Capacidades de geração de texto via GPT-4o
- **PostgreSQL:** Armazenamento de cache, logs de auditoria e configurações

> **⚠️ IMPORTANTE:** Em ambiente Docker, todas as URLs de integração devem usar os nomes dos serviços Docker (ex: `m2_task_orchestrator:8082`) ao invés de `localhost`. Certifique-se de que as variáveis `M2_API_URL` e `M4_API_URL` estejam corretamente configuradas no arquivo `docker-compose.yml` para evitar falhas de conexão.

### 10.2. Utilizado por
- **M14 Importek BFF:** Consumidor principal via endpoints REST
- **M9 Frontend UI:** Interface de usuário para consultas RAG
- **APIs Externas:** Integração com outros sistemas através de webhooks
- **Ferramentas de Monitoramento:** Dashboards e alertas baseados em métricas

### 10.3. Produz para
- **Respostas Inteligentes:** Conteúdo gerado contextualizado para usuários finais
- **Logs de Auditoria:** Registros detalhados para compliance e debugging
- **Métricas de Sistema:** Dados para monitoramento de performance e qualidade
- **Cache Inteligente:** Otimização de respostas para consultas futuras

## 11. Roadmap de Implementação

### 11.1. Fase 1: MVP (Funcionalidades Básicas)
- ✅ **Estrutura base FastAPI** com endpoints principais
- ✅ **Query Processor** básico com análise de intenção
- ✅ **Integração com M6** para busca semântica
- ✅ **LLM Engine** com GPT-4o e prompt templates básicos
- ✅ **Response Formatter** com citações simples

### 11.2. Fase 2: Integração Completa
- ✅ **Sistema de monitoramento** completo e funcional
- ✅ **Métricas de sistema** operacionais
- ✅ **Health checks** de todos os serviços integrados
- ✅ **Modelos SQLAlchemy** corrigidos e funcionais
- ✅ **Interface web** de monitoramento acessível
- 🔄 **Context Assembler** com múltiplas fontes (M5, M7)
- 🔄 **Cache inteligente** multi-camada
- 🔄 **Sistema de auditoria** completo
- 🔄 **Tratamento de erros** robusto

### 11.3. Fase 3: Otimização e Escala
- ⏳ **Processamento assíncrono** de consultas complexas
- ⏳ **Machine Learning** para otimização de busca
- ⏳ **Interface de administração** web
- ⏳ **API de configuração** dinâmica
- ⏳ **Integração com ferramentas** externas

### 11.4. Fase 4: Funcionalidades Avançadas
- ⏳ **Consultas multi-idioma** com tradução automática
- ⏳ **Análise de sentimento** em feedback
- ⏳ **Recomendações proativas** baseadas em histórico
- ⏳ **Integração com workflow** de aprovação
- ⏳ **APIs para desenvolvedores** externos

## 12. Testes e Validação

### 12.1. Estratégia de Testes
- **Testes Unitários:** Cada componente isoladamente
- **Testes de Integração:** Comunicação entre M6, M5, M7
- **Testes End-to-End:** Fluxo completo de consulta a resposta
- **Testes de Performance:** Carga, latência e throughput
- **Testes de Qualidade:** Precisão e relevância das respostas

### 12.2. Métricas de Qualidade
- **Precisão:** Porcentagem de respostas factualmente corretas
- **Relevância:** Aderência da resposta à consulta original
- **Completude:** Cobertura de todos os aspectos da consulta
- **Citações:** Precisão e qualidade das referências fornecidas
- **Tempo de Resposta:** Latência aceitável para diferentes tipos de consulta

### 12.3. Scripts de Validação
```bash
# Localização: m10_ai_orchestrator/scripts/
validate_query_processing.py     # Testa análise de consultas
validate_context_assembly.py     # Testa agregação de contexto  
validate_llm_integration.py      # Testa integração com GPT-4o
validate_response_quality.py     # Testa qualidade das respostas
validate_performance.py          # Testes de performance
validate_end_to_end.py          # Testes completos E2E
```

## 13. Considerações de Segurança

### 13.1. Proteção de Dados
- **Sanitização de Entrada:** Validação rigorosa de consultas de usuário
- **Controle de Acesso:** Autenticação e autorização para endpoints sensíveis
- **Audit Trail:** Log completo de todas as consultas e acessos
- **Anonimização:** Remoção de dados pessoais em logs e cache

### 13.2. Segurança de API
- **Rate Limiting:** Proteção contra abuso de endpoints
- **Validação de Input:** Prevenção de ataques de injeção
- **HTTPS Obrigatório:** Criptografia de comunicação
- **Chaves de API:** Rotação automática e gestão segura

### 13.3. Compliance
- **GDPR Compliance:** Direito ao esquecimento e portabilidade
- **SOX Compliance:** Auditoria e controles internos
- **FDA 21 CFR Part 11:** Assinaturas eletrônicas e registros
- **ISO 27001:** Gestão de segurança da informação

---

**Status:** 🟡 PARCIALMENTE OPERACIONAL - Problemas de conexão com M2 e M4
**Última Atualização:** 23/06/2025
**Responsável:** Kilo Code
**Prioridade:** Alta - Componente crítico para funcionalidade RAG

## 14. Status Atual do Sistema (23/06/2025)

### ✅ Funcionalidades Operacionais
- **Sistema de Monitoramento**: Totalmente funcional com dashboard web
- **API de Métricas**: Endpoint `/monitor/api/metrics` operacional
- **Health Checks**: Verificação de status de todos os serviços integrados
- **Banco de Dados**: Modelos SQLAlchemy corrigidos, enums funcionando
- **Interface Web**: Dashboard acessível em `http://localhost:8010/monitor`
- **Integração com Serviços**: Comunicação estabelecida com M1-M8, M14
- **Conexão OpenAI**: API da OpenAI funcionando corretamente.

### ⚠️ Problemas Identificados
- **Conexão com M2 (Task Orchestrator)**: Falha na conexão, embora o M2 esteja saudável. **Causa raiz**: Tentativa de conexão usando `localhost` ao invés do nome do serviço Docker. Variável de ambiente `M2_API_URL` não configurada no `docker-compose.yml`.
- **Conexão com M4 (Translator)**: Falha na conexão, embora o M4 esteja saudável. **Causa raiz**: Tentativa de conexão usando `localhost` ao invés do nome do serviço Docker. Variável de ambiente `M4_API_URL` não configurada no `docker-compose.yml`.
- **M5, M6, M7**: Status de verificação pendente no health check do M10.

### 🔧 Correções Implementadas
- **Modelos SQLAlchemy**: Corrigida configuração de enums com `values_callable`
- **ChatSession Model**: Enum SessionStatus funcionando corretamente
- **QueryLog Model**: Enums QueryType e QueryStatus operacionais
- **Docker Integration**: Imagem reconstruída e serviço estabilizado

### 📊 Métricas de Sistema Disponíveis
- CPU, memória e disco do sistema
- Status de conexão com PostgreSQL, Redis, RabbitMQ
- Verificação de APIs externas (OpenAI)
- Status de saúde de todos os módulos M1-M8, M14
- Métricas de conversas e queries (quando disponíveis)

### 🚀 Próximos Passos
- **Investigar e corrigir problemas de conexão do M10 com M2 e M4.**
- Implementação completa do Context Assembler
- Integração com M5, M6 e M7 para busca multi-fonte
- Sistema de cache inteligente
- Funcionalidades RAG completas
