# M4: Translator

## 1. Propósito do Módulo

O Módulo 4 (M4) - Translator é responsável por traduzir os chunks de texto (extraídos pelo M3) para o idioma alvo (Português do Brasil, pt-BR, por padrão). Ele utiliza a API da OpenAI (GPT-4o) para realizar as traduções, implementa um sistema de cache com Redis para evitar retraduções desnecessárias e armazena as traduções no banco de dados PostgreSQL. Após a tradução (ou recuperação do cache/banco), publica os resultados para o próximo módulo no pipeline.

## 2. Funcionalidades Chave

-   **Consumo de Resultados da Extração:** Escuta o tópico `extraction.results` do RabbitMQ para receber chunks de texto extraídos pelo M3.
-   **Detecção de Idioma:** Utiliza `langdetect` para identificar o idioma do texto original de cada chunk.
-   **Tradução com OpenAI:**
    -   Usa o modelo GPT-4o (configurável) da OpenAI para traduzir textos.
    -   Implementa batching para enviar múltiplos chunks em uma única requisição à API, otimizando custos e performance.
    -   Formata prompts específicos para tradução técnica e contextual.
    -   Inclui retry automático para chamadas à API da OpenAI.
-   **Cache de Traduções (Redis):**
    -   Antes de traduzir, verifica se a tradução para um determinado texto (e par de idiomas) já existe no cache Redis.
    -   Se encontrada (cache hit), utiliza a tradução do cache.
    -   Se não encontrada (cache miss), traduz e armazena o resultado no cache para uso futuro, com TTL (Time-To-Live).
    -   Usa hash SHA-256 do texto e idiomas como chave de cache.
-   **Persistência no Banco de Dados:**
    -   Armazena cada tradução realizada (ou recuperada do cache, ou o texto original se não precisar de tradução) na tabela `translations` do PostgreSQL.
    -   Associa a tradução ao `version_id` e `chunk_id` (UUID real) corretos.
-   **Publicação de Resultados:** Publica os chunks (traduzidos ou originais, se não necessitaram tradução) no tópico `translation.results` do RabbitMQ para o M6 (Vector Indexer).
-   **API RESTful:** Expõe endpoints para:
    -   Tradução de texto avulso (para testes).
    -   Health check do módulo e suas dependências (OpenAI, Redis, DB).
    -   Obter estatísticas de tradução, cache e banco.
    -   Gerenciar o cache (limpar, ver stats).
    -   Consultar traduções armazenadas por `version_id`.
-   **Interface Web de Teste:** Fornece uma UI simples (em `/`) para testar a funcionalidade de tradução.

## 3. Entradas e Saídas

-   **Entradas:**
    -   **RabbitMQ (Consumidor):** Mensagens do tópico `extraction.results` (publicadas pelo M3). Payload esperado:
        ```json
        {
            "chunk_id": "string (ID construído: {document_id}_chunk_{chunk_index})",
            "content": "string (texto do chunk)",
            "document_id": "string (UUID do documento)",
            "version_id": "string (UUID da versão)", // Crucial
            "sequence_number": "integer (índice do chunk)",
            "job_id": "string (job_id original do M2)",
            "metadata": { /* metadados do chunk do M3 */ }
        }
        ```
    -   **Requisições API:** Para os endpoints de tradução avulsa, stats, cache, etc.
    -   **Banco de Dados:** O `TranslationOrchestrator` (via `DatabaseService`) lê da tabela `chunks_parent` para obter o `chunk_id` UUID real.
    -   **Redis Cache:** Verifica se traduções já existem.
    -   **OpenAI API:** Para realizar traduções.
    -   **Variáveis de Ambiente:** Configurações do RabbitMQ, PostgreSQL, Redis, OpenAI, M2 API.

-   **Saídas:**
    -   **RabbitMQ (Produtor):** Publica mensagens no tópico `translation.results`. Payload da mensagem:
        ```json
        {
            "chunk_id": "string (ID construído original do M3)",
            "document_id": "string (UUID do documento)",
            "version_id": "string (UUID da versão)",
            "sequence_number": "integer (índice do chunk)",
            "original_content": "string",
            "source_language": "string",
            "target_language": "string (pt-BR)",
            "translated_content": "string (texto traduzido ou original)",
            "model_used": "string (ex: gpt-4o, cached, skipped_translation_same_language)",
            "confidence": "float",
            "timestamp": "float (timestamp do processamento)",
            "metadata": { /* metadados originais e de processamento */ }
        }
        ```
    -   **Banco de Dados:** Insere registros na tabela `translations`.
    -   **Redis Cache:** Armazena novas traduções.
    -   **Respostas API (JSON):** Para os endpoints da API.

## 4. Principais Componentes Internos

-   `app/main.py`: Aplicação FastAPI, define os endpoints da API.
-   `app/consumer.py`: Contém a lógica do consumidor RabbitMQ que escuta `extraction.results` e dispara o `TranslationOrchestrator`.
-   `app/translation_orchestrator.py`: Classe `TranslationOrchestrator` que coordena todo o fluxo de tradução de uma mensagem/chunk.
-   `app/translation_service.py`: Classe `TranslationService` responsável pela lógica de tradução em si, incluindo batching e chamadas à API da OpenAI.
-   `app/cache_service.py`: Classe `TranslationCacheService` para interagir com o Redis (get, set, stats, clear).
-   `app/database_service.py`: Classe `TranslationDatabaseService` para interagir com a tabela `translations` e `chunks_parent` no PostgreSQL.
-   `app/language_detector.py`: Classe `LanguageDetector` para identificar o idioma do texto.
-   `app/config.py`: Carrega e fornece as configurações do módulo.

## 5. Endpoints da API

A API do M4 é acessível em `http://localhost:8084` (quando rodando localmente via Docker Compose).
A documentação interativa da API (Swagger UI) está disponível em `http://localhost:8084/docs`.

### 5.1. Interface de Teste Web

-   **Endpoint:** `GET /`
-   **Descrição:** Serve uma página HTML simples para testar a funcionalidade de tradução de texto avulso.
-   **Acesso:** Abra `http://localhost:8084/` em um navegador.

### 5.2. Traduzir Texto

-   **Endpoint:** `POST /translate`
-   **Descrição:** Traduz um texto fornecido.
-   **Corpo da Requisição (JSON):**
    ```json
    {
        "text": "Texto para traduzir.",
        "source_language": "en", // Opcional
        "target_language": "pt-BR" // Padrão pt-BR
    }
    ```
-   **Resposta de Sucesso (200 OK):**
    ```json
    {
        "original_text": "Texto para traduzir.",
        "translated_text": "Texto traduzido.",
        "source_language": "en",
        "target_language": "pt-BR",
        "model_used": "gpt-4o",
        "confidence": 0.9,
        "processing_time": 1.23,
        "cached": false
    }
    ```
-   **Exemplo `curl`:**
    ```bash
    curl -X POST -H "Content-Type: application/json" \
    -d '{"text": "Hello world", "target_language": "pt-BR"}' \
    http://localhost:8084/translate
    ```

### 5.3. Health Check

-   **Endpoint:** `GET /healthz` (ou `GET /health`)
-   **Descrição:** Verifica a saúde do M4 e suas dependências (OpenAI, Redis, DB).
-   **Resposta de Sucesso (200 OK Exemplo):**
    ```json
    {
        "status": "healthy",
        "components": {
            "orchestrator": true,
            "translation_service": true,
            "cache_service": true,
            "database_service": {"status": "healthy", ...},
            "rabbitmq_connected": true 
        },
        "timestamp": **********.0
    }
    ```
-   **Exemplo `curl`:**
    ```bash
    curl http://localhost:8084/healthz
    ```

### 5.4. Estatísticas do Sistema

-   **Endpoint:** `GET /stats`
-   **Descrição:** Retorna estatísticas detalhadas de tradução, cache e banco de dados.
-   **Resposta de Sucesso (200 OK Exemplo):**
    ```json
    {
        "translation_stats": {"cache_hit_rate": 50.0, "total_requests": 100, ...},
        "cache_stats": {"connected": true, "total_translation_keys": 50, ...},
        "database_stats": {"total_translations": 100, ...}
    }
    ```
-   **Exemplo `curl`:**
    ```bash
    curl http://localhost:8084/stats
    ```

### 5.5. Estatísticas do Cache

-   **Endpoint:** `GET /cache/stats`
-   **Descrição:** Retorna estatísticas específicas do Redis cache.
-   **Exemplo `curl`:**
    ```bash
    curl http://localhost:8084/cache/stats
    ```

### 5.6. Limpar Cache

-   **Endpoint:** `GET /cache/clear`
-   **Descrição:** Remove todas as traduções do cache Redis.
-   **Exemplo `curl`:**
    ```bash
    curl http://localhost:8084/cache/clear
    ```

### 5.7. Obter Traduções por Versão

-   **Endpoint:** `GET /translations/{version_id}`
-   **Descrição:** Busca todas as traduções armazenadas para uma `version_id` específica.
-   **Parâmetros de Query:**
    -   `language_code` (opcional): Filtra por idioma de destino.
-   **Exemplo `curl`:**
    ```bash
    curl http://localhost:8084/translations/SEU_VERSION_ID?language_code=pt-BR
    ```

## 6. Configurações Importantes

Variáveis de ambiente (geralmente em `.env`):
-   **RabbitMQ:** `RABBITMQ_HOST`, `RABBITMQ_PORT`, `RABBITMQ_USER`, `RABBITMQ_PASS`. Tópicos: `EXTRACTION_RESULTS_TOPIC`, `TRANSLATION_RESULTS_TOPIC`.
-   **PostgreSQL:** `POSTGRES_HOST`, `POSTGRES_PORT`, `POSTGRES_DB`, `POSTGRES_USER`, `POSTGRES_PASSWORD`.
-   **Redis:** `REDIS_HOST`, `REDIS_PORT`, `REDIS_DB`, `REDIS_PASSWORD` (opcional), `REDIS_TTL`.
-   **OpenAI:** `OPENAI_API_KEY`, `OPENAI_MODEL`, `OPENAI_MAX_TOKENS`, `OPENAI_TEMPERATURE`.
-   **M2 API:** `M2_API_URL` (para atualizações de status, embora o M4 não pareça usar isso diretamente no código fornecido, mas é uma dependência comum).
-   **Tradução:** `TARGET_LANGUAGE`, `BATCH_SIZE`, `MAX_BATCH_TOKENS`.
-   **Detecção de Idioma:** `LANGUAGE_DETECTION_THRESHOLD`.
-   **Retry:** `MAX_RETRIES`, `RETRY_DELAY`, `BACKOFF_MULTIPLIER`.
-   `LOG_LEVEL`.

## 7. Importância para o Sistema

O M4 Translator garante que todo o conteúdo textual seja padronizado para o idioma alvo (Português do Brasil) antes da indexação semântica pelo M6. Isso é crucial para a qualidade da busca e das respostas geradas por RAG, pois assegura que as comparações de similaridade e o processamento pelo LLM ocorram em um único idioma consistente. O uso de cache otimiza custos e performance, evitando retraduções.

## 8. Interdependências

-   **Consome de:**
    -   RabbitMQ (tópico `extraction.results`): Recebe chunks extraídos do M3.
-   **Produz para:**
    -   RabbitMQ (tópico `translation.results`): Envia chunks traduzidos (ou originais) para o M6.
    -   Banco de Dados PostgreSQL: Armazena traduções na tabela `translations`.
    -   Redis: Armazena e lê traduções do cache.
-   **Depende de:**
    -   RabbitMQ.
    -   PostgreSQL.
    -   Redis.
    -   OpenAI API.
    -   M2 Task Orchestrator API (indiretamente, pois o `job_id` é passado adiante, mas o M4 não parece atualizar o M2 diretamente).
-   **Utilizado por (indiretamente):**
    -   M6 Vector Indexer: Consome as mensagens do tópico `translation.results`.
    -   M8 API Gateway: Expõe os endpoints do M4.
