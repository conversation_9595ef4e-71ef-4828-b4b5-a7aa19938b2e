# 🐳 Docker Setup - Mastigador de Dados

Guia completo para configuração, deployment e manutenção do ambiente Docker.

## 🎯 Visão Geral

O **Mastigador de Dados** utiliza **Docker Compose** para orquestrar **10 containers**:
- **7 módulos** de aplicação (M1, M2, M4, M5, M6, M7, M8)
- **3 serviços** de infraestrutura (PostgreSQL, Redis, RabbitMQ)

## 📋 Pré-requisitos

### Sistemas Suportados
- **Linux**: Ubuntu 20.04+, CentOS 7+, RHEL 8+
- **macOS**: 10.15+ (Catalina)
- **Windows**: 10 Pro/Enterprise com WSL2

### Software Necessário
```bash
# Docker Engine 20.10+
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# Docker Compose 2.0+
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Verificar instalação
docker --version
docker-compose --version
```

### Recursos de Sistema
```
Mínimo:
- CPU: 4 cores
- RAM: 8GB
- Disco: 50GB livres

Recomendado:
- CPU: 8 cores
- RAM: 16GB
- Disco: 100GB livres (SSD preferido)
```

## ⚙️ Configuração Inicial

### 1. Configurar Variáveis de Ambiente
```bash
# Copiar template
cp .env.example .env

# Editar configurações
nano .env
```

### Arquivo .env Completo
```bash
# Database
DB_PASSWORD=mastigador_secure_2024
POSTGRES_DB=mastigador
POSTGRES_USER=postgres
POSTGRES_PASSWORD=mastigador_secure_2024

# OpenAI (obrigatório para M4 Translator)
OPENAI_API_KEY=sk-your-openai-key-here

# Google Drive (obrigatório para M1 Drive Connector)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# RabbitMQ
RABBITMQ_DEFAULT_USER=mastigador
RABBITMQ_DEFAULT_PASS=rabbitmq_secure_2024

# Redis
REDIS_PASSWORD=redis_secure_2024

# Aplicação
ENVIRONMENT=production
LOG_LEVEL=INFO
DEBUG=false

# Networking
DOCKER_NETWORK=mastigador_network
```

### 2. Criar Rede Docker
```bash
# Criar rede personalizada
docker network create mastigador_network

# Verificar rede criada
docker network ls | grep mastigador
```

### 3. Preparar Volumes Persistentes
```bash
# Criar diretórios para volumes
mkdir -p ./data/postgres
mkdir -p ./data/redis
mkdir -p ./data/rabbitmq
mkdir -p ./logs
mkdir -p ./m1_drive_connector/data/raw

# Configurar permissões
sudo chown -R $USER:$USER ./data
sudo chown -R $USER:$USER ./logs
chmod -R 755 ./data
chmod -R 755 ./logs
```

## 🚀 Executar o Sistema

### Primeira Execução
```bash
# Build de todas as imagens
docker-compose build

# Subir infraestrutura primeiro
docker-compose up -d postgres redis rabbitmq

# Aguardar serviços ficarem prontos (30-60s)
sleep 60

# Subir módulos da aplicação
docker-compose up -d

# Verificar status
docker-compose ps
```

### Verificação de Saúde
```bash
# Status de todos os containers
docker-compose ps

# Logs de inicialização
docker-compose logs --tail=50

# Health check agregado
curl -s http://localhost:8080/healthz | jq
```

### Resultado Esperado
```json
{
  "status": "healthy",
  "version": "1.0.0",
  "modules": {
    "m1_drive_connector": true,
    "m2_task_orchestrator": true,
    "m4_translator": true,
    "m5_db_browser": true,
    "m6_vector_indexer": true,
    "m7_diff_engine": true
  },
  "infrastructure": {
    "database": "healthy",
    "redis": "healthy",
    "rabbitmq": "healthy"
  }
}
```

## 📊 Monitoramento

### Verificar Status dos Containers
```bash
# Status geral
docker-compose ps

# Containers ativos
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

# Uso de recursos
docker stats --no-stream

# Health checks individuais
curl -s http://localhost:8081/healthz | jq  # M1
curl -s http://localhost:8082/healthz | jq  # M2
curl -s http://localhost:8084/healthz | jq  # M4
curl -s http://localhost:8093/healthz | jq  # M5
curl -s http://localhost:8086/healthz | jq  # M6
curl -s http://localhost:8087/healthz | jq  # M7
curl -s http://localhost:8080/healthz | jq  # M8
```

### Monitoramento de Logs
```bash
# Logs de todos os serviços
docker-compose logs -f

# Logs de serviço específico
docker-compose logs -f m2_task_orchestrator

# Logs com timestamp
docker-compose logs -f -t

# Últimas 100 linhas
docker-compose logs --tail=100

# Filtrar por nível de log
docker-compose logs -f | grep ERROR
docker-compose logs -f | grep WARNING
```

### Monitoramento de Performance
```bash
# Uso de CPU e memória
docker stats

# Espaço em disco
docker system df

# Informações detalhadas dos containers
docker inspect m2_task_orchestrator

# Processos dentro do container
docker-compose exec m2_task_orchestrator ps aux
```

## 🔧 Operações de Manutenção

### Atualizações
```bash
# Atualizar código (git pull)
git pull origin main

# Rebuild apenas módulos alterados
docker-compose build m2_task_orchestrator

# Restart com nova imagem
docker-compose up -d m2_task_orchestrator

# Verificar atualização
curl http://localhost:8082/healthz
```

### Backup
```bash
#!/bin/bash
# Script de backup

BACKUP_DIR="./backups/$(date +%Y%m%d_%H%M%S)"
mkdir -p $BACKUP_DIR

# Backup PostgreSQL
docker-compose exec postgres pg_dump -U postgres mastigador > $BACKUP_DIR/database.sql

# Backup Redis
docker-compose exec redis redis-cli SAVE
docker cp $(docker-compose ps -q redis):/data/dump.rdb $BACKUP_DIR/redis.rdb

# Backup arquivos importados
tar -czf $BACKUP_DIR/drive_data.tar.gz ./m1_drive_connector/data/

# Backup logs
tar -czf $BACKUP_DIR/logs.tar.gz ./logs/

echo "Backup criado em: $BACKUP_DIR"
```

### Restore
```bash
#!/bin/bash
# Script de restore

BACKUP_DIR=$1
if [ -z "$BACKUP_DIR" ]; then
    echo "Uso: ./restore.sh /path/to/backup"
    exit 1
fi

# Parar serviços
docker-compose down

# Restore PostgreSQL
docker-compose up -d postgres
sleep 30
docker-compose exec postgres psql -U postgres -c "DROP DATABASE IF EXISTS mastigador;"
docker-compose exec postgres psql -U postgres -c "CREATE DATABASE mastigador;"
cat $BACKUP_DIR/database.sql | docker-compose exec -T postgres psql -U postgres mastigador

# Restore Redis
docker cp $BACKUP_DIR/redis.rdb $(docker-compose ps -q redis):/data/dump.rdb
docker-compose restart redis

# Restore arquivos
tar -xzf $BACKUP_DIR/drive_data.tar.gz

# Subir todos os serviços
docker-compose up -d
```

### Limpeza
```bash
# Parar e remover containers
docker-compose down

# Remover volumes (CUIDADO: apaga dados!)
docker-compose down -v

# Limpar imagens não utilizadas
docker image prune -f

# Limpar system completo
docker system prune -f

# Limpar tudo (CUIDADO: apaga tudo!)
docker system prune -a -f --volumes
```

## 🔍 Troubleshooting

### Problemas Comuns

#### 1. Container não inicia
```bash
# Verificar logs de erro
docker-compose logs container_name

# Verificar configuração
docker-compose config

# Rebuild da imagem
docker-compose build --no-cache container_name
docker-compose up -d container_name
```

#### 2. Erro de conectividade entre containers
```bash
# Verificar rede
docker network inspect mastigador_network

# Testar conectividade
docker-compose exec m8_api_gateway ping postgres
docker-compose exec m4_translator ping redis

# Verificar DNS interno
docker-compose exec m2_task_orchestrator nslookup postgres
```

#### 3. Performance lenta
```bash
# Verificar recursos
docker stats

# Verificar I/O de disco
iotop

# Verificar logs de erro
docker-compose logs | grep -i "error\|timeout\|slow"

# Ajustar recursos no docker-compose.yml
version: '3.8'
services:
  postgres:
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
```

#### 4. Problemas de permissão
```bash
# Verificar permissões dos volumes
ls -la ./data/

# Corrigir permissões
sudo chown -R $USER:$USER ./data
sudo chown -R $USER:$USER ./logs

# Verificar contexto SELinux (CentOS/RHEL)
ls -Z ./data/
sudo setsebool -P container_manage_cgroup true
```

#### 5. Banco de dados corrompido
```bash
# Verificar integridade
docker-compose exec postgres pg_dump -U postgres mastigador > test_dump.sql

# Se falhar, tentar recuperação
docker-compose exec postgres psql -U postgres -c "REINDEX DATABASE mastigador;"

# Último recurso: restore do backup
./restore.sh ./backups/ultimo_backup/
```

## 🔒 Segurança

### Configurações de Segurança
```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  postgres:
    environment:
      - POSTGRES_PASSWORD_FILE=/run/secrets/db_password
    secrets:
      - db_password
    
  redis:
    command: redis-server --requirepass ${REDIS_PASSWORD}
    
  rabbitmq:
    environment:
      - RABBITMQ_DEFAULT_USER_FILE=/run/secrets/rabbitmq_user
      - RABBITMQ_DEFAULT_PASS_FILE=/run/secrets/rabbitmq_pass
    secrets:
      - rabbitmq_user
      - rabbitmq_pass

secrets:
  db_password:
    file: ./secrets/db_password.txt
  rabbitmq_user:
    file: ./secrets/rabbitmq_user.txt
  rabbitmq_pass:
    file: ./secrets/rabbitmq_pass.txt
```

### Hardening
```bash
# Criar usuário não-root nos containers
# Dockerfile
RUN groupadd -r appuser && useradd -r -g appuser appuser
USER appuser

# Remover packages desnecessários
RUN apt-get remove -y curl wget && apt-get autoremove -y

# Configurar firewall (iptables)
sudo iptables -A INPUT -p tcp --dport 8080 -j ACCEPT  # API Gateway
sudo iptables -A INPUT -p tcp --dport 5432 -j DROP    # PostgreSQL (apenas interno)
sudo iptables -A INPUT -p tcp --dport 6379 -j DROP    # Redis (apenas interno)
```

## 📈 Otimização de Performance

### Configurações Recomendadas

#### PostgreSQL
```yaml
# docker-compose.yml
postgres:
  environment:
    - POSTGRES_INITDB_ARGS=--auth-host=scram-sha-256
  command: >
    postgres 
    -c shared_buffers=256MB
    -c effective_cache_size=1GB
    -c maintenance_work_mem=64MB
    -c checkpoint_completion_target=0.7
    -c wal_buffers=16MB
    -c default_statistics_target=100
```

#### Redis
```yaml
redis:
  command: >
    redis-server
    --maxmemory 512mb
    --maxmemory-policy allkeys-lru
    --appendonly yes
    --appendfsync everysec
```

#### Aplicação
```yaml
# Limites de recursos para cada módulo
m2_task_orchestrator:
  deploy:
    resources:
      limits:
        memory: 512M
        cpus: '0.5'
      reservations:
        memory: 256M
        cpus: '0.25'
```

### Configurações de Rede
```yaml
# docker-compose.yml
networks:
  mastigador_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
          gateway: **********
```

## 🌐 Deployment em Produção

### Ambiente de Produção
```yaml
# docker-compose.prod.yml
version: '3.8'
services:
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - m8_api_gateway
    restart: unless-stopped
    
  m8_api_gateway:
    environment:
      - ENVIRONMENT=production
      - LOG_LEVEL=WARNING
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
```

### Configuração do Nginx
```nginx
# nginx.conf
upstream api_gateway {
    server m8_api_gateway:8080;
}

server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    
    location / {
        proxy_pass http://api_gateway;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### CI/CD com GitHub Actions
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: Deploy to server
        uses: appleboy/ssh-action@v0.1.5
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          key: ${{ secrets.SSH_KEY }}
          script: |
            cd /opt/mastigador-de-dados
            git pull origin main
            docker-compose -f docker-compose.prod.yml build
            docker-compose -f docker-compose.prod.yml up -d
            
      - name: Health check
        run: |
          sleep 60
          curl -f https://your-domain.com/healthz
```

---

**🐳 Este guia cobre todos os aspectos do deployment Docker** desde configuração inicial até produção. Use as seções relevantes para seu ambiente específico.
