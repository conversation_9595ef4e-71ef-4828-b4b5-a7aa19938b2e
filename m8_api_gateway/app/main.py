import os
import httpx
from fastapi import FastAP<PERSON>, Request, HTTPException
from fastapi.responses import StreamingResponse, JSONResponse
from sse_starlette.sse import EventSourceResponse
import asyncio
import aio_pika
import json
from typing import Dict, Any, List

# --- Configuration ---
M1_DRIVE_CONNECTOR_URL = os.getenv("M1_DRIVE_CONNECTOR_URL", "http://mastigador_m1_drive:8081")
M2_TASK_ORCHESTRATOR_URL = os.getenv("M2_TASK_ORCHESTRATOR_URL", "http://mastigador_m2_api:8082")
M5_DB_BROWSER_URL = os.getenv("M5_DB_BROWSER_URL", "http://mastigador_m5_db_browser:8093")
M6_VECTOR_INDEXER_URL = os.getenv("M6_VECTOR_INDEXER_URL", "http://mastigador_m6_indexer:8086")
M7_DIFF_ENGINE_URL = os.getenv("M7_DIFF_ENGINE_URL", "http://mastigador_m7_diff:8087")
M10_IA_ORCHESTRATOR_URL = os.getenv("M10_IA_ORCHESTRATOR_URL", "http://mastigador_m10_ai_orchestrator:8010")

RABBITMQ_HOST = os.getenv("RABBITMQ_HOST", "rabbitmq")
RABBITMQ_USER = os.getenv("RABBITMQ_USER", "user")
RABBITMQ_PASS = os.getenv("RABBITMQ_PASS", "password")
RABBITMQ_URL = f"amqp://{RABBITMQ_USER}:{RABBITMQ_PASS}@{RABBITMQ_HOST}/"

app = FastAPI(
    title="M8 API Gateway",
    version="1.0.0",
    description="API Gateway for the Mastigador de Dados em Lote project."
)

http_client: httpx.AsyncClient

@app.on_event("startup")
async def startup_event():
    global http_client
    http_client = httpx.AsyncClient(timeout=30.0) # Increased timeout for potentially long operations

@app.on_event("shutdown")
async def shutdown_event():
    await http_client.aclose()

# --- Helper for Proxying ---
async def _proxy_request(request: Request, target_url: str):
    try:
        # Determine the path for the target service by stripping /v1 prefix if present
        target_path = request.url.path
        if target_path.startswith("/v1"):
            target_path = target_path[len("/v1"):] # Remove /v1
        
        # Ensure target_path starts with a / if it's not empty after stripping
        if not target_path.startswith("/") and target_path != "":
            target_path = "/" + target_path
        elif target_path == "" or target_path is None: # If path becomes empty or None
            target_path = "/"

        # Construct the final URL for the backend service
        # Start with the base target_url
        dest_url = httpx.URL(target_url)
        
        # Update path and query parameters
        # request.url.query is already a string, httpx.URL can parse it.
        # If request.url.query is empty, it will be handled correctly.
        final_url_str = f"{dest_url.scheme}://{dest_url.host}:{dest_url.port}{target_path}"
        if request.url.query:
             final_url_str += f"?{request.url.query}"
        
        final_httpx_url = httpx.URL(final_url_str)

        # Build the request for the target service using the fully constructed URL
        rp_req = http_client.build_request(
            request.method, 
            final_httpx_url, # Use the fully constructed httpx.URL object
            headers=request.headers.raw, 
            content=await request.body()
        )
        
        print(f"M8: Attempting to proxy request: {request.method} {request.url} to {rp_req.url}") # DEBUG LOG
        
        rp_resp = await http_client.send(rp_req, stream=True)
        
        print(f"M8: Received response {rp_resp.status_code} from {rp_req.url}") # DEBUG LOG
        
        return StreamingResponse(
            rp_resp.aiter_raw(),
            status_code=rp_resp.status_code,
            headers=rp_resp.headers,
            background=rp_resp.aclose,
        )
    except httpx.RequestError as e:
        print(f"M8: httpx.RequestError while proxying to {rp_req.url if 'rp_req' in locals() else 'UNKNOWN TARGET'}: {e}") # DEBUG LOG
        # More specific error handling could be added here (e.g., for timeouts, connection errors)
        raise HTTPException(status_code=503, detail=f"Service unavailable: {str(e)}")
    except Exception as e_generic:
        import traceback
        print(f"M8: Unexpected error in _proxy_request for {request.url}: {e_generic}") # DEBUG LOG
        print(f"M8: Traceback: {traceback.format_exc()}") # DEBUG LOG FOR TRACEBACK
        raise HTTPException(status_code=500, detail=f"Internal proxy error: {str(e_generic)}")


# --- Endpoints ---

@app.get("/healthz", summary="Aggregated Health Check")
async def health_check():
    # In a real scenario, this would check health of M1, M2, M5, M6, M7 etc.
    # For now, a simple health check for the gateway itself.
    # Later, we can add checks for upstream services.
    services_health = {}
    service_urls = {
        "m1_drive_connector": f"{M1_DRIVE_CONNECTOR_URL}/healthz",
        "m2_task_orchestrator": f"{M2_TASK_ORCHESTRATOR_URL}/healthz",
        "m5_db_browser": f"{M5_DB_BROWSER_URL}/healthz",
        "m6_vector_indexer": f"{M6_VECTOR_INDEXER_URL}/healthz",
        "m7_diff_engine": f"{M7_DIFF_ENGINE_URL}/healthz",
        "m10_ai_orchestrator": f"{M10_IA_ORCHESTRATOR_URL}/health",
    }
    
    async def check_service(name: str, url: str):
        try:
            response = await http_client.get(url, timeout=5.0)
            if response.status_code == 200:
                services_health[name] = response.json()
            else:
                services_health[name] = {"status": "error", "detail": f"Status code {response.status_code}"}
        except Exception as e:
            services_health[name] = {"status": "unreachable", "detail": str(e)}

    await asyncio.gather(*(check_service(name, url) for name, url in service_urls.items()))
    
    all_services_ok = True
    for name, s_health in services_health.items():
        if not isinstance(s_health, dict):
            all_services_ok = False
            break
        
        service_is_ok = False
        if name == "m1_drive_connector":
            service_is_ok = s_health.get("status") == "ok" and s_health.get("drive_service_status") == "ok"
        elif name == "m2_task_orchestrator":
            # PRD M2: { "rabbit":"ok", "db":"ok" }
            # Atual M2: { "status":"healthy", "database":"healthy", "rabbitmq":"healthy" }
            service_is_ok = s_health.get("status") == "healthy" and s_health.get("database") == "healthy" and s_health.get("rabbitmq") == "healthy"
        elif name == "m5_db_browser":
            # PRD M5: { "db":"ok" }
            # Atual M5: { "status":"ok", "database":"ok" }
            service_is_ok = s_health.get("status") == "ok" and s_health.get("database") == "ok"
        elif name == "m6_vector_indexer":
            # PRD M6: { "db":"ok", "openai":"ok" }
            # Atual M6: { "db":"ok", "openai":"ok", "m6_status":"ok" }
            service_is_ok = s_health.get("db") == "ok" and s_health.get("openai") == "ok"
        elif name == "m7_diff_engine":
            # PRD M7: { "db":"ok" }
            # Atual M7: { "db":"ok", "m7_status":"ok" }
            service_is_ok = s_health.get("db") == "ok"
        elif name == "m10_ai_orchestrator":
            # M10: { "status":"healthy" } ou { "status":"unhealthy" }
            service_is_ok = s_health.get("status") == "healthy"

        if not service_is_ok:
            all_services_ok = False
            break
            
    overall_status = "ok" if all_services_ok else "error"
    
    return {"gateway_status": "ok", "overall_backend_status": overall_status, "services": services_health}


# --- M1 Drive Connector Routes ---
@app.post("/v1/import", summary="Import files from Google Drive")
async def import_drive_folder(request: Request):
    return await _proxy_request(request, M1_DRIVE_CONNECTOR_URL)

# --- M2 Task Orchestrator Routes ---
@app.get("/v1/tasks", summary="List tasks")
async def list_tasks(request: Request):
    return await _proxy_request(request, M2_TASK_ORCHESTRATOR_URL)

@app.get("/v1/tasks/{job_id}", summary="Get task details")
async def get_task_details(request: Request, job_id: str): # job_id is part of path, handled by proxy
    return await _proxy_request(request, M2_TASK_ORCHESTRATOR_URL)

@app.patch("/v1/tasks/{job_id}", summary="Update task status")
async def update_task_status(request: Request, job_id: str): # job_id is part of path
    return await _proxy_request(request, M2_TASK_ORCHESTRATOR_URL)


# --- M5 DB Browser Routes ---
@app.get("/v1/documents", summary="List documents and versions")
async def list_m5_documents(request: Request):
    return await _proxy_request(request, M5_DB_BROWSER_URL)

@app.get("/v1/documents/{document_id_str}", summary="Get document details or HTML")
async def get_m5_document_details(request: Request, document_id_str: str): # document_id_str is part of path
    return await _proxy_request(request, M5_DB_BROWSER_URL)

@app.get("/v1/query", summary="Execute a SELECT SQL query")
async def query_m5_database(request: Request):
    return await _proxy_request(request, M5_DB_BROWSER_URL)


# --- M6 Vector Indexer Routes ---
@app.post("/v1/index", summary="Index document chunks")
async def index_document_m6(request: Request):
    return await _proxy_request(request, M6_VECTOR_INDEXER_URL)

@app.post("/v1/search", summary="Perform semantic search")
async def search_documents(request: Request):
    # This one requires aggregation as per PRD:
    # 1. Proxy to M6 /search
    # 2. For each doc_id in M6 result, get original_name from M5 /documents/{doc_id} (or a dedicated M5 endpoint)
    # 3. Combine and return.
    
    # Step 1: Proxy to M6
    try:
        m6_url = httpx.URL(path="/search", query=request.url.query.encode("utf-8"))
        rp_req_m6 = http_client.build_request(
            "POST", m6_url, headers=request.headers.raw, content=await request.body()
        )
        rp_req_m6.url = httpx.URL(M6_VECTOR_INDEXER_URL + "/search") # Query params are not automatically passed here, need to fix if any
        
        m6_response = await http_client.send(rp_req_m6)
        m6_response.raise_for_status() # Raise exception for 4xx/5xx status
        m6_full_response = m6_response.json() 
        # O M6 retorna um objeto com uma chave "results" que contém a lista
        # Ex: {"query": "...", "results": [...], ...}
        m6_results_list = m6_full_response.get("results")

    except httpx.HTTPStatusError as e:
        return JSONResponse(status_code=e.response.status_code, content={"detail": f"Error from M6: {e.response.text}"})
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"Error calling M6 service: {str(e)}")

    # Step 2 & 3: Aggregate with M5 data (original_name)
    aggregated_results = []
    if isinstance(m6_results_list, list): # Verificar se m6_results_list é uma lista
        async def get_original_name(doc_id_str: str):
            try:
                # M5 endpoint /v1/documents/{document_id} (sem version) deve retornar metadados básicos
                # incluindo original_name, conforme PRD M5.
                # O proxy _proxy_request já remove o /v1, então o path para M5 será /documents/{document_id}
                m5_doc_url = f"{M5_DB_BROWSER_URL}/documents/{doc_id_str}"
                
                # Não podemos usar _proxy_request diretamente aqui pois ele retorna StreamingResponse.
                # Precisamos fazer uma chamada direta e pegar o JSON.
                m5_response = await http_client.get(m5_doc_url, timeout=10.0) # Adicionado timeout
                
                if m5_response.status_code == 200:
                    m5_data = m5_response.json()
                    return m5_data.get("original_name", "N/A (original_name not found in M5 response)")
                else:
                    m5_error_detail = f"M5 error {m5_response.status_code}"
                    try: # Tentar obter mais detalhes do erro do M5
                        m5_error_detail += f" - {m5_response.json().get('detail', m5_response.text)}"
                    except:
                        m5_error_detail += f" - {m5_response.text}"
                    print(f"M8: Error fetching original_name from M5 for {doc_id_str}. Detail: {m5_error_detail}")
                    return f"N/A ({m5_error_detail})"
            except httpx.TimeoutException as e_timeout:
                print(f"M8: Timeout fetching original_name from M5 for {doc_id_str}: {e_timeout}")
                return "N/A (M5 timeout)"
            except Exception as e_m5_detail:
                import traceback
                print(f"M8: Exception fetching original_name from M5 for {doc_id_str}: {e_m5_detail}\n{traceback.format_exc()}")
                return "N/A (Exception fetching M5 data)"

        # Criar tarefas para buscar todos os original_names em paralelo
        tasks = []
        for item in m6_results_list: # Corrigido de m6_results para m6_results_list
            doc_id = item.get("document_id")
            item_copy = item.copy() # Trabalhar com uma cópia para não modificar o original durante a iteração
            if doc_id:
                tasks.append(get_original_name(str(doc_id))) # Adiciona a corotina à lista de tarefas
                item_copy["_doc_id_for_later_mapping"] = str(doc_id) # Para mapear de volta
            else:
                item_copy["original_name"] = "N/A (no document_id from M6)"
            aggregated_results.append(item_copy) # Adiciona a cópia (com ou sem _doc_id_for_later_mapping)

        # Executar todas as chamadas ao M5 em paralelo
        original_names_results = []
        if tasks:
            try:
                original_names_results = await asyncio.gather(*tasks, return_exceptions=True)
            except Exception as e_gather:
                import traceback
                print(f"M8: Exception during asyncio.gather for M5 calls: {e_gather}\n{traceback.format_exc()}")
                # Preencher todos os nomes como erro se o gather falhar catastroficamente
                for i in range(len(aggregated_results)):
                    if "_doc_id_for_later_mapping" in aggregated_results[i]:
                        aggregated_results[i]["original_name"] = "N/A (M5 batch call failed)"
                        del aggregated_results[i]["_doc_id_for_later_mapping"]
                return JSONResponse(status_code=500, content={"detail": "Error during M5 data aggregation", "m6_response": m6_full_response, "aggregation_error": str(e_gather)})
        
            # Mapear os resultados de volta para os itens corretos
            doc_id_to_name_map = {}
            # Iterar sobre os m6_results_list originais para manter a ordem e o mapeamento correto
            current_task_idx = 0
            for original_item in m6_results_list:
                doc_id_for_map = original_item.get("document_id")
                if doc_id_for_map: # Se este item tinha um doc_id e uma tarefa foi criada para ele
                    if current_task_idx < len(original_names_results):
                        result_or_exc = original_names_results[current_task_idx]
                        if isinstance(result_or_exc, Exception):
                            print(f"M8: Exception occurred while fetching name for {doc_id_for_map}: {result_or_exc}")
                            doc_id_to_name_map[str(doc_id_for_map)] = "N/A (Exception during M5 call)"
                        else:
                            doc_id_to_name_map[str(doc_id_for_map)] = result_or_exc
                        current_task_idx +=1
                    else: # Fallback, should not happen
                        doc_id_to_name_map[str(doc_id_for_map)] = "N/A (Result index out of bounds)"


            # Atualizar os resultados agregados com os nomes obtidos
            # A lista aggregated_results já foi populada com cópias dos itens do m6_results_list
            for final_item in aggregated_results:
                # O _doc_id_for_later_mapping foi adicionado antes, agora usamos para buscar o nome
                temp_doc_id = final_item.get("_doc_id_for_later_mapping") 
                if temp_doc_id:
                    final_item["original_name"] = doc_id_to_name_map.get(temp_doc_id, "N/A (mapping failed or no name found)")
                    del final_item["_doc_id_for_later_mapping"] # Limpar o campo temporário
    
    elif m6_results_list is None: # Caso a chave "results" não exista na resposta do M6
        print(f"M8: 'results' key not found in M6 response. M6_full_response: {m6_full_response}")
        return JSONResponse(status_code=500, content={"detail": "'results' key not found in M6 response", "m6_response": m6_full_response})
    else: # Se m6_results_list não for uma lista (e não for None)
        print(f"M8: M6 'results' is not a list. M6_full_response: {m6_full_response}")
        return JSONResponse(status_code=500, content={"detail": "M6 'results' is not a list", "m6_response": m6_full_response})

    return JSONResponse(content=aggregated_results)

@app.post("/v1/search/vector", summary="Perform search by vector") # Corrigido de /by-vector para /vector
async def search_documents_by_vector(request: Request):
    return await _proxy_request(request, M6_VECTOR_INDEXER_URL)

@app.get("/v1/index-status/{task_id}", summary="Get indexing status for a task")
async def get_index_status_m6(request: Request, task_id: str): # task_id is part of path
    return await _proxy_request(request, M6_VECTOR_INDEXER_URL)


# --- M7 Diff Engine Routes ---
@app.post("/v1/diff", summary="Get differences between document versions") # Alterado de GET para POST
async def get_document_diff(request: Request): # Nome da função mantido por simplicidade, mas agora é POST
    return await _proxy_request(request, M7_DIFF_ENGINE_URL)

@app.post("/v1/export/diff", summary="Export document differences")
async def export_document_diff(request: Request):
    return await _proxy_request(request, M7_DIFF_ENGINE_URL)

# --- M10 IA Orchestrator ---
@app.post("/v1/chat", summary="Chat with IA Orchestrator")
async def chat_with_ia(request: Request):
    return await _proxy_request(request, M10_IA_ORCHESTRATOR_URL)


# --- Server-Sent Events ---
async def rabbitmq_event_generator(topics: List[str], stop_event: asyncio.Event):
    connection = None
    sse_queue = asyncio.Queue() # Queue to send events to the client

    async def consume_from_rabbitmq(topic: str, channel: aio_pika.abc.AbstractChannel, sse_q: asyncio.Queue):
        try:
            exchange = await channel.declare_exchange(name=topic, type=aio_pika.ExchangeType.FANOUT, durable=True)
            queue = await channel.declare_queue(name="", exclusive=True) # Anonymous, exclusive queue for this consumer
            await queue.bind(exchange=exchange, routing_key="")
            print(f"M8 SSE: Subscribed to RabbitMQ topic: {topic}")
            async with queue.iterator() as queue_iter:
                async for message in queue_iter:
                    if stop_event.is_set():
                        print(f"M8 SSE: Stop event received for topic {topic}, exiting consumer.")
                        break
                    async with message.process():
                        try:
                            data = json.loads(message.body.decode())
                            event_type = message.exchange if message.exchange else "unknown_event"
                            await sse_q.put({"event": event_type, "data": json.dumps(data)})
                        except json.JSONDecodeError:
                            print(f"M8 SSE: Error decoding JSON from RabbitMQ topic {topic}: {message.body.decode()}")
                        except Exception as e:
                            print(f"M8 SSE: Error processing RabbitMQ message from topic {topic}: {e}")
        except asyncio.CancelledError:
            print(f"M8 SSE: Consumer for topic {topic} cancelled.")
        except Exception as e:
            print(f"M8 SSE: RabbitMQ consumer error for topic {topic}: {e}")
            # Optionally put an error event onto the sse_queue
            await sse_q.put({"event": "error", "data": json.dumps({"topic": topic, "message": "Consumer error", "detail": str(e)})})
        finally:
            print(f"M8 SSE: Consumer for topic {topic} finished.")


    consumer_tasks = []
    try:
        connection = await aio_pika.connect_robust(RABBITMQ_URL)
        async with connection:
            channel = await connection.channel()
            await channel.set_qos(prefetch_count=10) # Adjust as needed

            for topic in topics:
                task = asyncio.create_task(consume_from_rabbitmq(topic, channel, sse_queue))
                consumer_tasks.append(task)
            
            print(f"M8 SSE: All RabbitMQ consumers started for topics: {topics}")

            # Loop to yield events from the sse_queue
            while not stop_event.is_set():
                try:
                    # Wait for an event from the queue, with a timeout to check stop_event
                    event_data = await asyncio.wait_for(sse_queue.get(), timeout=1.0)
                    yield event_data
                    sse_queue.task_done()
                except asyncio.TimeoutError:
                    # Timeout is normal, just means no message in 1s, check stop_event and continue
                    continue
                except asyncio.CancelledError: # If the client disconnects, this generator is cancelled
                    print("M8 SSE: rabbitmq_event_generator cancelled by client disconnect.")
                    stop_event.set() # Signal consumers to stop
                    break
                except Exception as e:
                    print(f"M8 SSE: Error in main event generator loop: {e}")
                    # Optionally yield an error to the client
                    yield {"event": "error", "data": json.dumps({"message": "Main SSE loop error", "detail": str(e)})}
                    # Potentially break or continue based on error severity
                    await asyncio.sleep(1) # Avoid tight loop on persistent error

    except asyncio.CancelledError: # Handles cancellation if client disconnects during setup
        print("M8 SSE: rabbitmq_event_generator cancelled during setup.")
        stop_event.set()
    except Exception as e:
        print("M8 SSE: Overall RabbitMQ connection or setup error: {e}")
        yield {"event": "error", "data": json.dumps({"message": "SSE stream setup error", "detail": str(e)})}
    finally:
        print("M8 SSE: Cleaning up SSE generator...")
        stop_event.set() # Ensure stop_event is set on any exit path
        if consumer_tasks:
            print(f"M8 SSE: Waiting for {len(consumer_tasks)} consumer tasks to finish...")
            # Wait for tasks to finish, but with a timeout
            done, pending = await asyncio.wait(consumer_tasks, timeout=5.0, return_when=asyncio.ALL_COMPLETED)
            for task_to_cancel in pending:
                print(f"M8 SSE: Cancelling a still pending consumer task...")
                task_to_cancel.cancel()
            # Aguardar que todas as tarefas (incluindo as canceladas) realmente terminem
            if consumer_tasks: # Verificar se a lista não está vazia
                await asyncio.gather(*consumer_tasks, return_exceptions=True)
            print("M8 SSE: All consumer tasks processed during cleanup.")
        
        if connection and not connection.is_closed:
            print("M8 SSE: Closing RabbitMQ connection.")
            await connection.close()
        print("M8 SSE: SSE Stream and RabbitMQ connection fully closed.")


@app.get("/v1/events", summary="Stream system events via SSE")
async def stream_events(request: Request):
    stop_event = asyncio.Event()
    # It's important that EventSourceResponse handles client disconnects
    # by cancelling the generator. The generator then sets stop_event.
    # FastAPI/Starlette should handle this.
    
    async def event_publisher_wrapper():
        try:
            async for event in rabbitmq_event_generator(event_topics, stop_event):
                # Check if client disconnected (FastAPI/Starlette specific check)
                if await request.is_disconnected():
                    print("M8 SSE: Client disconnected, stopping event publisher.")
                    stop_event.set() # Signal the generator to stop
                    break
                yield event
        except asyncio.CancelledError:
            print("M8 SSE: event_publisher_wrapper cancelled by client disconnect.")
            stop_event.set() 
        except Exception as e_wrapper:
            import traceback
            print(f"M8 SSE: Unhandled exception in event_publisher_wrapper: {e_wrapper}\n{traceback.format_exc()}")
            # Este log é para depuração no servidor. O cliente receberá 500.
            # Não podemos fazer yield aqui se o stream já falhou ou não iniciou.
            stop_event.set()
            raise # Re-levantar para que o FastAPI lide como um erro interno e retorne 500
        finally:
            print("M8 SSE: event_publisher_wrapper finished.")
            stop_event.set() # Final assurance

    # PRD: `tasks.queue`, `drive.ingest`, `chat.responses`
    # `chat.responses` is from M10, so we'll omit for now.
    # `extraction.results`, `translation.results` could also be useful.
    # Let's start with `drive.ingest` and `tasks.queue`
    # Note: The PRD for M2 says it consumes `drive.ingest` and produces `tasks.queue`.
    # The PRD for M3 says it consumes `tasks.queue` and produces `extraction.results`.
    # The PRD for M4 says it consumes `extraction.results` and produces `translation.results`.
    # So, `drive.ingest`, `tasks.queue`, `extraction.results`, `translation.results` are all relevant.
    
    # For simplicity in this initial sketch, let's just listen to one or two.
    # A full implementation would need to merge streams from multiple topics.
    # The current rabbitmq_event_generator is simplified to listen to the first topic in the list.
    # This needs to be made more robust.
    
    # Topics to listen to (as per PRD for M8 /events)
    # For now, let's try 'drive.ingest' and 'tasks.queue'
    # The current generator only handles the first one in the list effectively.
    # This is a known limitation of this initial sketch.
    event_topics = ["drive.ingest", "tasks.queue", "extraction.results", "translation.results"]
    # Passar o stop_event para o gerador principal que é consumido pelo event_publisher_wrapper
    # O event_publisher_wrapper já recebe o stop_event através do closure da função stream_events.
    # A chamada correta é para o event_publisher_wrapper, que internamente usa o rabbitmq_event_generator.
    # A definição do event_publisher_wrapper já usa o stop_event do escopo de stream_events.
    # A chamada para EventSourceResponse deve ser o event_publisher_wrapper.
    return EventSourceResponse(event_publisher_wrapper()) # Chamada correta para o wrapper


# --- Frontend Compatibility Routes (without /v1 prefix) ---
@app.get("/health", summary="Frontend Health Check")
async def frontend_health_check():
    """Health check endpoint for frontend compatibility"""
    return await health_check()

@app.get("/tasks", summary="List tasks (frontend compatibility)")
async def frontend_list_tasks(request: Request):
    """Frontend-compatible endpoint that proxies to /v1/tasks"""
    return await list_tasks(request)

@app.get("/tasks/{job_id}", summary="Get task details (frontend compatibility)")
async def frontend_get_task_details(request: Request, job_id: str):
    """Frontend-compatible endpoint that proxies to /v1/tasks/{job_id}"""
    return await get_task_details(request, job_id)

@app.patch("/tasks/{job_id}", summary="Update task status (frontend compatibility)")
async def frontend_update_task_status(request: Request, job_id: str):
    """Frontend-compatible endpoint that proxies to /v1/tasks/{job_id}"""
    return await update_task_status(request, job_id)

@app.get("/documents", summary="List documents (frontend compatibility)")
async def frontend_list_documents(request: Request):
    """Frontend-compatible endpoint that proxies to /v1/documents"""
    return await list_m5_documents(request)

@app.get("/documents/{document_id_str}", summary="Get document details (frontend compatibility)")
async def frontend_get_document_details(request: Request, document_id_str: str):
    """Frontend-compatible endpoint that proxies to /v1/documents/{document_id_str}"""
    return await get_m5_document_details(request, document_id_str)

@app.post("/search", summary="Search documents (frontend compatibility)")
async def frontend_search_documents(request: Request):
    """Frontend-compatible endpoint that proxies to /v1/search"""
    return await search_documents(request)

@app.post("/import", summary="Import files (frontend compatibility)")
async def frontend_import_drive_folder(request: Request):
    """Frontend-compatible endpoint that proxies to /v1/import"""
    return await import_drive_folder(request)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8888)  # Changed to port 8888 for frontend compatibility
