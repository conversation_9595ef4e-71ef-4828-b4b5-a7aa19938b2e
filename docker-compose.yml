services:
  db:
    image: pgvector/pgvector:pg16
    container_name: mastigador_db
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-password}
      POSTGRES_DB: ${POSTGRES_DB:-mastigador_db}
    volumes:
      - mastigador_pg_data:/var/lib/postgresql/data
      # Para inicializar com scripts SQL (ex: criar extensรฃo se necessรกrio, ou schema inicial)
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U $${POSTGRES_USER:-user} -d $${POSTGRES_DB:-mastigador_db}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    restart: unless-stopped
    networks:
      - mastigador_net

  rabbitmq:
    image: rabbitmq:3.13-management
    container_name: mastigador_rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: ${RABBITMQ_USER:-user}
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_PASS:-password}
    volumes:
      - mastigador_rabbitmq_data:/var/lib/rabbitmq
    ports:
      - "${RABBITMQ_AMQP_PORT:-5672}:5672"  # AMQP
      - "${RABBITMQ_MGMT_PORT:-15672}:15672" # Management UI
    healthcheck:
      test: ["CMD-SHELL", "rabbitmq-diagnostics -q ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    restart: unless-stopped
    networks:
      - mastigador_net

  redis:
    image: redis:7-alpine
    container_name: mastigador_redis
    command: redis-server --appendonly yes
    volumes:
      - mastigador_redis_data:/data
    ports:
      - "${REDIS_PORT:-6379}:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    restart: unless-stopped
    networks:
      - mastigador_net

  m2_orchestrator_api:
    build:
      context: ./m2_task_orchestrator
      dockerfile: Dockerfile
    container_name: mastigador_m2_api
    command: uvicorn app.main:app --host 0.0.0.0 --port 8082 --reload
    volumes:
      - ./m2_task_orchestrator/app:/app/app # Monta o cรณdigo para hot-reloading
      - ./token.json:/app/token.json:ro # Monta o token do Google se necessรกrio (verificar se M2 precisa)
      - ./.env:/app/.env # Monta o arquivo .env
    ports:
      - "8082:8082"
    depends_on:
      db:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    environment:
      - PYTHONUNBUFFERED=1 # Para logs aparecerem imediatamente
      - DATABASE_URL=postgresql://${POSTGRES_USER:-user}:${POSTGRES_PASSWORD:-password}@db:${POSTGRES_PORT:-5432}/${POSTGRES_DB:-mastigador_db}
      - POSTGRES_HOST=db # Adicionado para que config.py use o host correto
      - RABBITMQ_HOST=rabbitmq # Adicionado para que a API M2 use o host correto do RabbitMQ
      # Outras variรกveis de ambiente podem ser herdadas do .env ou definidas aqui
    networks:
      - mastigador_net
    restart: unless-stopped

  m2_orchestrator_consumer:
    build:
      context: ./m2_task_orchestrator
      dockerfile: Dockerfile
    container_name: mastigador_m2_consumer
    command: python -m app.consumer
    volumes:
      - ./m2_task_orchestrator/app:/app/app
      - ./data:/app/data
      - ./token.json:/app/token.json:ro
      - ./.env:/app/.env
    depends_on:
      db:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    environment:
      - PYTHONUNBUFFERED=1
      - DATABASE_URL=postgresql://${POSTGRES_USER:-user}:${POSTGRES_PASSWORD:-password}@db:${POSTGRES_PORT:-5432}/${POSTGRES_DB:-mastigador_db}
      - POSTGRES_HOST=db # Adicionado para que config.py use o host correto
      - RABBITMQ_HOST=rabbitmq # Adicionado para que o consumer use o host correto
    networks:
      - mastigador_net
    restart: unless-stopped

  m1_drive_connector:
    build:
      context: ./m1_drive_connector
      dockerfile: Dockerfile
    container_name: mastigador_m1_drive
    command: uvicorn app.main:app --host 0.0.0.0 --port 8081 --reload
    volumes:
      - ./m1_drive_connector/app:/app/app # Monta o cรณdigo para hot-reloading
      - ./m1_drive_connector/static:/app/static
      - ./data:/app/data
      - ./token.json:/app/token.json # Monta o token do Google se necessรกrio (verificar se M2 precisa) - REMOVIDO :ro
      - ./.env:/app/.env # Monta o arquivo .env
    ports:
      - "8081:8081"
    depends_on:
      rabbitmq:
        condition: service_healthy
      m2_orchestrator_api:
        condition: service_started
    environment:
      - PYTHONUNBUFFERED=1
      - RABBITMQ_HOST=rabbitmq
      - RABBITMQ_PORT=${RABBITMQ_PORT:-5672}
      - RABBITMQ_USER=${RABBITMQ_USER:-user}
      - RABBITMQ_PASS=${RABBITMQ_PASS:-password}
      - M2_API_URL=http://mastigador_m2_api:8082
    networks:
      - mastigador_net
    restart: unless-stopped

  m3_extractor:
    build:
      context: ./m3_extractor
      dockerfile: Dockerfile
    container_name: mastigador_m3_extractor
    command: python -m app.consumer # Roda o consumidor RabbitMQ do M3
    volumes:
      - ./m3_extractor/app:/app/app   # Monta o cรณdigo para hot-reloading
      - ./data:/app/data              # Monta o diretรณrio de dados para acesso aos arquivos baixados pelo M1
      - ./.env:/app/.env              # Monta o arquivo .env
    depends_on:
      db:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      m2_orchestrator_api: # Precisa da API do M2 para atualizar status
        condition: service_started # Ou service_healthy se M2 tiver healthcheck
    environment:
      - PYTHONUNBUFFERED=1
      - POSTGRES_HOST=db
      - POSTGRES_PORT=${POSTGRES_PORT:-5432}
      - POSTGRES_DB=${POSTGRES_DB:-mastigador_db}
      - POSTGRES_USER=${POSTGRES_USER:-user}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-password}
      - RABBITMQ_HOST=rabbitmq
      - RABBITMQ_PORT=${RABBITMQ_PORT:-5672}
      - RABBITMQ_USER=${RABBITMQ_USER:-user}
      - RABBITMQ_PASS=${RABBITMQ_PASS:-password}
      - M2_API_URL=http://mastigador_m2_api:8082
    networks:
      - mastigador_net
    restart: unless-stopped

  m4_translator_api:
    build:
      context: ./m4_translator
      dockerfile: Dockerfile
    container_name: mastigador_m4_api
    command: uvicorn app.main:app --host 0.0.0.0 --port 8084 --reload
    volumes:
      - ./m4_translator/app:/app/app   # Monta o cรณdigo para hot-reloading
      - ./.env:/app/.env               # Monta o arquivo .env
    ports:
      - "8084:8084"
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    environment:
      - PYTHONUNBUFFERED=1
      - POSTGRES_HOST=db
      - POSTGRES_PORT=${POSTGRES_PORT:-5432}
      - POSTGRES_DB=${POSTGRES_DB:-mastigador_db}
      - POSTGRES_USER=${POSTGRES_USER:-user}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-password}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - RABBITMQ_HOST=rabbitmq
      - RABBITMQ_PORT=${RABBITMQ_PORT:-5672}
      - RABBITMQ_USER=${RABBITMQ_USER:-user}
      - RABBITMQ_PASS=${RABBITMQ_PASS:-password}
      - M2_API_URL=http://mastigador_m2_api:8082
    networks:
      - mastigador_net
    restart: unless-stopped

  m4_translator_consumer:
    build:
      context: ./m4_translator
      dockerfile: Dockerfile
    container_name: mastigador_m4_consumer
    command: python -m app.consumer # Roda o consumidor RabbitMQ do M4
    volumes:
      - ./m4_translator/app:/app/app   # Monta o cรณdigo para hot-reloading
      - ./.env:/app/.env               # Monta o arquivo .env
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      m3_extractor: # Depende do M3 para receber mensagens
        condition: service_started
    environment:
      - PYTHONUNBUFFERED=1
      - POSTGRES_HOST=db
      - POSTGRES_PORT=${POSTGRES_PORT:-5432}
      - POSTGRES_DB=${POSTGRES_DB:-mastigador_db}
      - POSTGRES_USER=${POSTGRES_USER:-user}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-password}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - RABBITMQ_HOST=rabbitmq
      - RABBITMQ_PORT=${RABBITMQ_PORT:-5672}
      - RABBITMQ_USER=${RABBITMQ_USER:-user}
      - RABBITMQ_PASS=${RABBITMQ_PASS:-password}
      - M2_API_URL=http://mastigador_m2_api:8082
    networks:
      - mastigador_net
    restart: unless-stopped

  m6_vector_indexer:
    build:
      context: ./m6_vector_indexer
      dockerfile: Dockerfile
    container_name: mastigador_m6_indexer
    command: uvicorn app.main:app --host 0.0.0.0 --port 8086 --reload
    volumes:
      - ./m6_vector_indexer/app:/app/app   # Monta o cรณdigo para hot-reloading
      - ./.env:/app/.env               # Monta o arquivo .env
    ports:
      - "8086:8086"
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      - PYTHONUNBUFFERED=1
      - POSTGRES_HOST=db
      - POSTGRES_PORT=${POSTGRES_PORT:-5432}
      - POSTGRES_DB=${POSTGRES_DB:-mastigador_db}
      - POSTGRES_USER=${POSTGRES_USER:-user}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-password}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - API_HOST=0.0.0.0
      - API_PORT=8086
      - LOG_LEVEL=info
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_MODEL=${OPENAI_MODEL:-gpt-4o}
      - OPENAI_MAX_TOKENS=${OPENAI_MAX_TOKENS:-4000}
      - OPENAI_TEMPERATURE=${OPENAI_TEMPERATURE:-0.1}
    networks:
      - mastigador_net
    restart: unless-stopped

  m7_diff_engine:
    build:
      context: ./m7_diff_engine
      dockerfile: Dockerfile
    container_name: mastigador_m7_diff
    command: uvicorn app.main:app --host 0.0.0.0 --port 8087 --reload
    volumes:
      - ./m7_diff_engine/app:/app/app   # Monta o cรณdigo para hot-reloading
      - ./m7_diff_engine/static:/app/static # Monta os arquivos estรกticos
      - ./.env:/app/.env               # Monta o arquivo .env
    ports:
      - "8087:8087"
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      - PYTHONUNBUFFERED=1
      - POSTGRES_HOST=db
      - POSTGRES_PORT=${POSTGRES_PORT:-5432}
      - POSTGRES_DB=${POSTGRES_DB:-mastigador_db}
      - POSTGRES_USER=${POSTGRES_USER:-user}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-password}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - M7_HOST=0.0.0.0
      - M7_PORT=8087
      - LOG_LEVEL=info
    networks:
      - mastigador_net
    restart: unless-stopped

  m5_db_browser_service:
    build:
      context: ./m5_db_browser
      dockerfile: Dockerfile
    container_name: mastigador_m5_db_browser
    command: uvicorn app.main:app --host 0.0.0.0 --port 8093 --reload
    volumes:
      - ./m5_db_browser/app:/app/app   # Monta o cรณdigo para hot-reloading
      - ./.env:/app/.env               # Monta o arquivo .env para variรกveis de ambiente
    ports:
      - "8093:8093"
    depends_on:
      db:
        condition: service_healthy
    environment:
      - PYTHONUNBUFFERED=1
      - DATABASE_URL=postgresql://${POSTGRES_USER:-user}:${POSTGRES_PASSWORD:-password}@db:${POSTGRES_PORT:-5432}/${POSTGRES_DB:-mastigador_db}
      # Adicionar quaisquer outras variรกveis de ambiente especรญficas do M5 aqui, se necessรกrio
      # Por exemplo, o ADMIN_TOKEN mencionado no PRD para o DB-Browser
      - ADMIN_TOKEN=${M5_ADMIN_TOKEN:-secret} # Exemplo, pode ser definido no .env
    networks:
      - mastigador_net
    restart: unless-stopped

  m8_api_gateway_service:
    build:
      context: ./m8_api_gateway
      dockerfile: Dockerfile
    container_name: mastigador_m8_api_gateway
    command: uvicorn app.main:app --host 0.0.0.0 --port 8080 # --reload removido temporariamente
    volumes:
      - ./m8_api_gateway/app:/app/app
      - ./.env:/app/.env
    ports:
      - "8089:8080" # Alterada a porta do host para 8089
    depends_on:
      db:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      redis:
        condition: service_healthy
      m1_drive_connector:
        condition: service_started
      m2_orchestrator_api:
        condition: service_started
      # m3_extractor doesn't expose a port for direct API calls from gateway usually
      # m4_translator_api exposes 8084, m4_translator_consumer is a worker
      m4_translator_api:
        condition: service_started
      m5_db_browser_service:
        condition: service_started
      m6_vector_indexer:
        condition: service_started
      m7_diff_engine:
        condition: service_started
    environment:
      - PYTHONUNBUFFERED=1
      - M1_DRIVE_CONNECTOR_URL=http://mastigador_m1_drive:8081
      - M2_TASK_ORCHESTRATOR_URL=http://mastigador_m2_api:8082
      - M5_DB_BROWSER_URL=http://mastigador_m5_db_browser:8093
      - M6_VECTOR_INDEXER_URL=http://mastigador_m6_indexer:8086
      - M7_DIFF_ENGINE_URL=http://mastigador_m7_diff:8087
      # M10_IA_ORCHESTRATOR_URL=http://m10_ia_orchestrator:8096 # Placeholder
      - RABBITMQ_HOST=rabbitmq
      - RABBITMQ_USER=${RABBITMQ_USER:-user}
      - RABBITMQ_PASS=${RABBITMQ_PASS:-password}
      # REDIS_URL=redis://redis:6379/0 # If needed by M8 directly
    networks:
      - mastigador_net
    restart: unless-stopped

  m10_ai_orchestrator:
    build:
      context: ./m10_ai_orchestrator
      dockerfile: Dockerfile
    container_name: mastigador_m10_ai_orchestrator
    command: uvicorn app.main:app --host 0.0.0.0 --port 8010 --reload
    volumes:
      - ./m10_ai_orchestrator/app:/app/app # Para hot-reloading durante o desenvolvimento
      - ./.env:/app/.env # Monta o arquivo .env
    ports:
      - "8010:8010"
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
      m2_orchestrator_api: # Adicionado para garantir que M2 esteja pronto se M10 depender dele
        condition: service_started 
      m4_translator_api: # Adicionado para garantir que M4 esteja pronto
        condition: service_started
      m5_db_browser_service:
        condition: service_started
      m6_vector_indexer:
        condition: service_started
      m7_diff_engine:
        condition: service_started
    environment:
      - PYTHONUNBUFFERED=1
      - DATABASE_URL=postgresql://${POSTGRES_USER:-user}:${POSTGRES_PASSWORD:-password}@db:${POSTGRES_PORT:-5432}/${POSTGRES_DB:-mastigador_db}
      - POSTGRES_HOST=db
      - POSTGRES_PORT=${POSTGRES_PORT:-5432}
      - POSTGRES_DB=${POSTGRES_DB:-mastigador_db}
      - POSTGRES_USER=${POSTGRES_USER:-user}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-password}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - M5_DB_BROWSER_URL=http://mastigador_m5_db_browser:8093
      - M6_VECTOR_INDEXER_URL=http://mastigador_m6_indexer:8086
      - M7_DIFF_ENGINE_URL=http://mastigador_m7_diff:8087
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_MODEL=${OPENAI_MODEL:-gpt-4o}
      - OPENAI_MAX_TOKENS=${OPENAI_MAX_TOKENS:-4000}
      - OPENAI_TEMPERATURE=${OPENAI_TEMPERATURE:-0.3}
    networks:
      - mastigador_net
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8010/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  m14_importek_bff:
    build: ./m14_importek_bff
    container_name: mastigador_m14_bff
    command: uvicorn app.main:app --host 0.0.0.0 --port 8090 --reload
    volumes:
      - ./m14_importek_bff/app:/app/app # Para hot-reloading durante o desenvolvimento
      - ./.env:/app/.env # Monta o .env principal para ser lido por python-dotenv dentro do app M14
    ports:
      - "8090:8090" # Mapeia a porta 8090 do host para a 8090 do container
    depends_on:
      rabbitmq:
        condition: service_healthy
      m8_api_gateway_service: # Depende do serviço M8 API Gateway
        condition: service_started 
    environment:
      - PYTHONUNBUFFERED=1
      # A porta interna do container (8090) é definida no Dockerfile do M14.
      # Variáveis como SECRET_KEY, RABBITMQ_URL, etc., serão lidas do .env montado.
      # Definimos explicitamente a URL do M8 para o M14 usar:
      - M8_API_GATEWAY_URL=http://mastigador_m8_api_gateway:8080
      - M10_AI_ORCHESTRATOR_URL=http://mastigador_m10_ai_orchestrator:8010 # Adicionado URL do M10
    networks:
      - mastigador_net
    restart: unless-stopped
    # O healthcheck pode ser adicionado posteriormente, após o endpoint /healthz do M14 estar funcional.
    # healthcheck:
    #   test: ["CMD", "curl", "-f", "http://localhost:8090/healthz"]
    #   interval: 30s
    #   timeout: 10s
    #   retries: 3
    #   start_period: 30s

volumes:
  mastigador_pg_data:
    driver: local
  mastigador_rabbitmq_data:
    driver: local
  mastigador_redis_data:
    driver: local

networks:
  mastigador_net:
    driver: bridge
