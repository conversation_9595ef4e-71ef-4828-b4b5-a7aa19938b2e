#!/usr/bin/env python3
"""
Script para forçar o processamento de todos os chunks pendentes no M4
Simula o comportamento do M4 Consumer para processar chunks sem tradução
"""

import requests
import json
import time
import sys
from urllib.parse import quote_plus

# Configurações
M5_URL = "http://localhost:8093"
M4_URL = "http://localhost:8084"
BATCH_SIZE = 5
DELAY_BETWEEN_BATCHES = 2

def get_pending_chunks(limit=None):
    """Busca chunks que ainda não foram traduzidos"""
    print(f"🔍 Buscando chunks pendentes...")
    
    sql = '''SELECT 
        cp.chunk_id,
        cp.version_id,
        cp.text,
        cp.chunk_index,
        d.document_id,
        d.original_name
    FROM chunks_parent cp
    JOIN versions v ON cp.version_id = v.version_id
    JOIN documents d ON v.document_id = d.document_id
    LEFT JOIN translations t ON cp.chunk_id = t.chunk_id
    WHERE t.translation_id IS NULL
    AND LENGTH(cp.text) > 10'''
    
    if limit:
        sql += f' LIMIT {limit}'
    
    encoded_sql = quote_plus(sql)
    
    try:
        resp = requests.get(f'{M5_URL}/query?sql={encoded_sql}', timeout=10)
        if resp.status_code == 200:
            result = resp.json()
            chunks = result.get('rows', [])
            print(f"✅ Encontrados {len(chunks)} chunks pendentes")
            return chunks
        else:
            print(f"❌ Erro ao buscar chunks: {resp.text}")
            return []
    except Exception as e:
        print(f"❌ Erro: {e}")
        return []

def simulate_m4_consumer_processing(chunk):
    """Simula o processamento do M4 Consumer para um chunk"""
    chunk_id = chunk['chunk_id']
    version_id = chunk['version_id']
    text = chunk['text']
    
    print(f"🔄 Processando chunk {chunk_id[:8]}...")
    print(f"   📄 Documento: {chunk['original_name']}")
    print(f"   📝 Texto: {text[:50]}...")
    
    # Criar payload como o M4 Consumer faria
    message_data = {
        "chunk_id": chunk_id,
        "version_id": version_id,
        "content": text,
        "metadata": {
            "document_id": chunk['document_id'],
            "chunk_index": chunk['chunk_index']
        }
    }
    
    # Simular o processamento via TranslationOrchestrator
    # Vamos usar um endpoint interno se existir, ou criar uma requisição direta
    try:
        # Primeiro, vamos tentar traduzir via API normal
        payload = {
            'text': text,
            'source_language': 'auto',
            'target_language': 'pt'
        }
        
        resp = requests.post(f'{M4_URL}/translate', json=payload, timeout=30)
        if resp.status_code == 200:
            result = resp.json()
            translated_text = result.get('translated_text', '')
            
            print(f"   ✅ Traduzido: {translated_text[:50]}...")
            
            # Agora precisamos salvar no banco manualmente
            # Como não temos acesso direto ao TranslationOrchestrator,
            # vamos verificar se foi salvo
            time.sleep(1)
            
            # Verificar se foi salvo
            sql_check = f"SELECT COUNT(*) as count FROM translations WHERE chunk_id = '{chunk_id}'"
            encoded_check = quote_plus(sql_check)
            check_resp = requests.get(f'{M5_URL}/query?sql={encoded_check}', timeout=5)
            
            if check_resp.status_code == 200:
                check_result = check_resp.json()
                count = check_result.get('rows', [{}])[0].get('count', 0)
                if count > 0:
                    print(f"   💾 ✅ Salvo no banco!")
                    return True
                else:
                    print(f"   💾 ❌ NÃO foi salvo no banco!")
                    return False
            else:
                print(f"   💾 ❓ Erro ao verificar banco")
                return False
        else:
            print(f"   ❌ Erro na tradução: {resp.status_code} - {resp.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Erro: {e}")
        return False

def force_process_all_chunks():
    """Força o processamento de todos os chunks pendentes"""
    print("🚀 INICIANDO PROCESSAMENTO FORÇADO M4")
    print("=" * 50)
    
    # Buscar todos os chunks pendentes
    chunks = get_pending_chunks()
    
    if not chunks:
        print("✅ Nenhum chunk pendente encontrado!")
        return
    
    total_chunks = len(chunks)
    processed = 0
    failed = 0
    
    print(f"\n📊 Total de chunks para processar: {total_chunks}")
    print(f"📦 Processando em lotes de {BATCH_SIZE}")
    
    # Processar em lotes
    for i in range(0, total_chunks, BATCH_SIZE):
        batch = chunks[i:i+BATCH_SIZE]
        batch_num = (i // BATCH_SIZE) + 1
        total_batches = (total_chunks + BATCH_SIZE - 1) // BATCH_SIZE
        
        print(f"\n🔄 Lote {batch_num}/{total_batches} ({len(batch)} chunks)")
        print("-" * 30)
        
        for chunk in batch:
            success = simulate_m4_consumer_processing(chunk)
            if success:
                processed += 1
            else:
                failed += 1
            
            time.sleep(1)  # Pausa entre chunks
        
        # Pausa entre lotes
        if i + BATCH_SIZE < total_chunks:
            print(f"\n⏸️  Pausando {DELAY_BETWEEN_BATCHES}s antes do próximo lote...")
            time.sleep(DELAY_BETWEEN_BATCHES)
    
    # Relatório final
    print(f"\n🎯 PROCESSAMENTO CONCLUÍDO!")
    print("=" * 50)
    print(f"📊 Total processados: {processed}/{total_chunks}")
    print(f"❌ Falhas: {failed}")
    print(f"✅ Taxa de sucesso: {(processed/total_chunks)*100:.1f}%")
    
    if failed > 0:
        print(f"\n⚠️  {failed} chunks falharam no processamento")
        print("   Verifique os logs do M4 para mais detalhes")

def main():
    if len(sys.argv) > 1:
        if sys.argv[1] == "--test":
            # Modo teste: processar apenas 3 chunks
            print("🧪 MODO TESTE - Processando apenas 3 chunks")
            chunks = get_pending_chunks(limit=3)
            if chunks:
                for chunk in chunks:
                    simulate_m4_consumer_processing(chunk)
                    time.sleep(2)
            return
        elif sys.argv[1] == "--help":
            print("Uso:")
            print("  python force_m4_processing.py          # Processar todos os chunks")
            print("  python force_m4_processing.py --test   # Processar apenas 3 chunks (teste)")
            print("  python force_m4_processing.py --help   # Mostrar esta ajuda")
            return
    
    # Modo normal: processar todos
    force_process_all_chunks()

if __name__ == "__main__":
    main()
