"""Configurações e fixtures globais para os testes do M3 Extractor"""

import os
import sys
import pytest
from unittest.mock import Mock, MagicMock
import tempfile
import shutil
from pathlib import Path

# Adicionar o diretório do app ao path para importações
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'app'))

from config import Config


@pytest.fixture
def mock_config():
    """Fixture para configuração mock de teste"""
    config = Config()
    config.RABBITMQ_HOST = 'localhost'
    config.RABBITMQ_PORT = 5673
    config.RABBITMQ_USER = 'test_user'
    config.RABBITMQ_PASS = 'test_pass'
    config.POSTGRES_HOST = 'localhost'
    config.POSTGRES_PORT = 5433
    config.POSTGRES_DB = 'test_db'
    config.POSTGRES_USER = 'test_user'
    config.POSTGRES_PASSWORD = 'test_pass'
    config.M2_API_URL = 'http://localhost:8082'
    config.MAX_CHUNK_SIZE = 1000
    config.DEFAULT_LANGUAGE = 'pt-BR'
    return config


@pytest.fixture
def temp_dir():
    """Fixture para diretório temporário"""
    temp_path = tempfile.mkdtemp()
    yield temp_path
    shutil.rmtree(temp_path)


@pytest.fixture
def sample_pdf_content():
    """Conteúdo de exemplo para PDFs"""
    return "Este é um exemplo de texto extraído de um PDF. Contém múltiplas linhas e parágrafos para testar a funcionalidade de extração de conteúdo."


@pytest.fixture
def sample_excel_content():
    """Conteúdo de exemplo para Excel"""
    return "--- Planilha: Sheet1 ---\nColuna A\tColuna B\nValor 1\tValor 2\n--- Planilha: Sheet2 ---\nDados\tInfo\nTeste\tExemplo"


@pytest.fixture
def sample_text_content():
    """Conteúdo de exemplo para arquivos de texto"""
    return "Exemplo de arquivo de texto UTF-8 com acentuação: ção, ã, é, í, ó, ú"


@pytest.fixture
def sample_chunks():
    """Chunks de exemplo para testes"""
    return [
        {
            "content": "Primeiro chunk de exemplo com conteúdo suficiente para teste",
            "chunk_id": "chunk_1",
            "metadata": {
                "page": 1,
                "chunk_method": "page_break",
                "language": "pt-BR"
            }
        },
        {
            "content": "Segundo chunk de exemplo com mais conteúdo para validação",
            "chunk_id": "chunk_2", 
            "metadata": {
                "page": 2,
                "chunk_method": "page_break",
                "language": "pt-BR"
            }
        }
    ]


@pytest.fixture
def mock_rabbitmq_connection():
    """Mock para conexão RabbitMQ"""
    mock_connection = Mock()
    mock_channel = Mock()
    mock_connection.channel.return_value = mock_channel
    return mock_connection, mock_channel


@pytest.fixture
def mock_postgres_connection():
    """Mock para conexão PostgreSQL"""
    mock_conn = Mock()
    mock_cursor = Mock()
    mock_conn.cursor.return_value = mock_cursor
    return mock_conn, mock_cursor


@pytest.fixture
def mock_m2_api():
    """Mock para API do M2"""
    return Mock()


@pytest.fixture
def valid_rabbitmq_message():
    """Mensagem RabbitMQ válida para testes"""
    return {
        "job_id": "test_job_123",
        "module_to_process": "extractor",
        "data": {
            "file_path": "/path/to/test/file.pdf",
            "document_id": "doc_456",
            "metadata": {
                "filename": "test_file.pdf",
                "upload_date": "2025-06-24T12:00:00Z"
            }
        }
    }


@pytest.fixture
def invalid_rabbitmq_message():
    """Mensagem RabbitMQ inválida para testes"""
    return {
        "job_id": "test_job_invalid",
        # Faltando module_to_process e data
    }


@pytest.fixture
def fixtures_path():
    """Caminho para os arquivos fixture"""
    return Path(__file__).parent / "fixtures"


@pytest.fixture
def mock_file_operations():
    """Mock para operações de arquivo"""
    mock_ops = Mock()
    mock_ops.exists.return_value = True
    mock_ops.read_text.return_value = "Conteúdo mock de arquivo"
    return mock_ops


# Fixtures específicas para cada tipo de teste

@pytest.fixture
def mock_pdfminer():
    """Mock para PDFMiner"""
    with pytest.mock.patch('pdfminer.high_level.extract_text') as mock:
        mock.return_value = "Texto extraído via PDFMiner"
        yield mock


@pytest.fixture  
def mock_tesseract():
    """Mock para Tesseract OCR"""
    with pytest.mock.patch('pytesseract.image_to_string') as mock:
        mock.return_value = "Texto extraído via OCR"
        yield mock


@pytest.fixture
def mock_openpyxl():
    """Mock para OpenPyXL"""
    mock_workbook = Mock()
    mock_worksheet = Mock()
    mock_worksheet.iter_rows.return_value = [
        [Mock(value="Col1"), Mock(value="Col2")],
        [Mock(value="Val1"), Mock(value="Val2")]
    ]
    mock_workbook.worksheets = [mock_worksheet]
    mock_workbook.sheetnames = ["Sheet1"]
    
    with pytest.mock.patch('openpyxl.load_workbook') as mock:
        mock.return_value = mock_workbook
        yield mock


@pytest.fixture
def mock_langdetect():
    """Mock para detecção de idioma"""
    with pytest.mock.patch('langdetect.detect') as mock:
        mock.return_value = 'pt'
        yield mock


# Configurações de ambiente para testes
@pytest.fixture(autouse=True)
def setup_test_environment(monkeypatch):
    """Configurar ambiente de teste automaticamente"""
    monkeypatch.setenv('RABBITMQ_HOST', 'localhost')
    monkeypatch.setenv('RABBITMQ_PORT', '5673')
    monkeypatch.setenv('POSTGRES_HOST', 'localhost')
    monkeypatch.setenv('POSTGRES_PORT', '5433')
    monkeypatch.setenv('LOG_LEVEL', 'DEBUG')