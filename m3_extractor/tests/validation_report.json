{"summary": {"total_validations": 33, "passed_validations": 31, "success_rate": 93.94, "overall_status": "PASS"}, "by_category": {"structure_unit": {"total": 4, "passed": 4, "results": ["ValidationResult(category='structure_unit', test_name='file_test_unit_extraction.py', status=True, message='Arquivo test_unit_extraction.py encontrado', details={'path': '/volume01/devtools/projetos/Projeto Fred/versões/VS Code/v7/m3_extractor/tests/unit/test_unit_extraction.py'})", "ValidationResult(category='structure_unit', test_name='file_test_unit_chunking.py', status=True, message='Arquivo test_unit_chunking.py encontrado', details={'path': '/volume01/devtools/projetos/Projeto Fred/versões/VS Code/v7/m3_extractor/tests/unit/test_unit_chunking.py'})", "ValidationResult(category='structure_unit', test_name='file_test_unit_language.py', status=True, message='Arquivo test_unit_language.py encontrado', details={'path': '/volume01/devtools/projetos/Projeto Fred/versões/VS Code/v7/m3_extractor/tests/unit/test_unit_language.py'})", "ValidationResult(category='structure_unit', test_name='file_test_unit_config.py', status=True, message='Arquivo test_unit_config.py encontrado', details={'path': '/volume01/devtools/projetos/Projeto Fred/versões/VS Code/v7/m3_extractor/tests/unit/test_unit_config.py'})"]}, "structure_integration": {"total": 11, "passed": 11, "results": ["ValidationResult(category='structure_integration', test_name='file_test_rabbitmq_integration.py', status=True, message='Arquivo test_rabbitmq_integration.py encontrado', details={'path': '/volume01/devtools/projetos/Projeto Fred/versões/VS Code/v7/m3_extractor/tests/integration/test_rabbitmq_integration.py'})", "ValidationResult(category='structure_integration', test_name='file_test_rabbitmq_scenarios.py', status=True, message='Arquivo test_rabbitmq_scenarios.py encontrado', details={'path': '/volume01/devtools/projetos/Projeto Fred/versões/VS Code/v7/m3_extractor/tests/integration/test_rabbitmq_scenarios.py'})", "ValidationResult(category='structure_integration', test_name='file_test_postgresql_integration.py', status=True, message='Arquivo test_postgresql_integration.py encontrado', details={'path': '/volume01/devtools/projetos/Projeto Fred/versões/VS Code/v7/m3_extractor/tests/integration/test_postgresql_integration.py'})", "ValidationResult(category='structure_integration', test_name='file_test_postgresql_scenarios.py', status=True, message='Arquivo test_postgresql_scenarios.py encontrado', details={'path': '/volume01/devtools/projetos/Projeto Fred/versões/VS Code/v7/m3_extractor/tests/integration/test_postgresql_scenarios.py'})", "ValidationResult(category='structure_integration', test_name='file_test_end_to_end_pipeline.py', status=True, message='Arquivo test_end_to_end_pipeline.py encontrado', details={'path': '/volume01/devtools/projetos/Projeto Fred/versões/VS Code/v7/m3_extractor/tests/integration/test_end_to_end_pipeline.py'})", "ValidationResult(category='structure_integration', test_name='file_test_end_to_end_scenarios.py', status=True, message='Arquivo test_end_to_end_scenarios.py encontrado', details={'path': '/volume01/devtools/projetos/Projeto Fred/versões/VS Code/v7/m3_extractor/tests/integration/test_end_to_end_scenarios.py'})", "ValidationResult(category='structure_integration', test_name='file_test_m2_api_integration.py', status=True, message='Arquivo test_m2_api_integration.py encontrado', details={'path': '/volume01/devtools/projetos/Projeto Fred/versões/VS Code/v7/m3_extractor/tests/integration/test_m2_api_integration.py'})", "ValidationResult(category='structure_integration', test_name='file_conftest.py', status=True, message='Arquivo conftest.py encontrado', details={'path': '/volume01/devtools/projetos/Projeto Fred/versões/VS Code/v7/m3_extractor/tests/integration/conftest.py'})", "ValidationResult(category='structure_integration', test_name='file_rabbitmq_utils.py', status=True, message='Arquivo rabbitmq_utils.py encontrado', details={'path': '/volume01/devtools/projetos/Projeto Fred/versões/VS Code/v7/m3_extractor/tests/integration/rabbitmq_utils.py'})", "ValidationResult(category='structure_integration', test_name='file_postgresql_utils.py', status=True, message='Arquivo postgresql_utils.py encontrado', details={'path': '/volume01/devtools/projetos/Projeto Fred/versões/VS Code/v7/m3_extractor/tests/integration/postgresql_utils.py'})", "ValidationResult(category='structure_integration', test_name='file_end_to_end_utils.py', status=True, message='Arquivo end_to_end_utils.py encontrado', details={'path': '/volume01/devtools/projetos/Projeto Fred/versões/VS Code/v7/m3_extractor/tests/integration/end_to_end_utils.py'})"]}, "structure_docs": {"total": 3, "passed": 3, "results": ["ValidationResult(category='structure_docs', test_name='doc_README_RABBITMQ.md', status=True, message='Documentação README_RABBITMQ.md encontrada', details=None)", "ValidationResult(category='structure_docs', test_name='doc_README_POSTGRESQL.md', status=True, message='Documentação README_POSTGRESQL.md encontrada', details=None)", "ValidationResult(category='structure_docs', test_name='doc_README_END_TO_END.md', status=True, message='Documentação README_END_TO_END.md encontrada', details=None)"]}, "structure_fixtures": {"total": 3, "passed": 3, "results": ["ValidationResult(category='structure_fixtures', test_name='dir_pdfs', status=True, message='Diretório fixtures/pdfs encontrado', details=None)", "ValidationResult(category='structure_fixtures', test_name='dir_excel', status=True, message='Diretório fixtures/excel encontrado', details=None)", "ValidationResult(category='structure_fixtures', test_name='dir_text', status=True, message='Diretório fixtures/text encontrado', details=None)"]}, "structure_docker": {"total": 1, "passed": 1, "results": ["ValidationResult(category='structure_docker', test_name='config_docker-compose.test.yml', status=True, message='Config Docker docker-compose.test.yml encontrada', details=None)"]}, "config": {"total": 4, "passed": 3, "results": ["ValidationResult(category='config', test_name='pytest_ini', status=False, message='pytest.ini AUSENTE - criar para configuração adequada', details=None)", "ValidationResult(category='config', test_name='requirements_test', status=True, message='requirements-test.txt encontrado', details=None)", "ValidationResult(category='config', test_name='docker_compose', status=True, message='docker-compose.test.yml encontrado', details=None)", "ValidationResult(category='config', test_name='docker_services', status=True, message='Serviços Docker: PostgreSQL=✅, RabbitMQ=✅', details=None)"]}, "documentation": {"total": 6, "passed": 6, "results": ["ValidationResult(category='documentation', test_name='doc_README.md', status=True, message='README principal: ✅ Substancial (13068 bytes)', details=None)", "ValidationResult(category='documentation', test_name='doc_README_RABBITMQ.md', status=True, message='Doc RabbitMQ: ✅ Substancial (8791 bytes)', details=None)", "ValidationResult(category='documentation', test_name='doc_README_POSTGRESQL.md', status=True, message='Doc PostgreSQL: ✅ Substancial (7702 bytes)', details=None)", "ValidationResult(category='documentation', test_name='doc_README_END_TO_END.md', status=True, message='Doc End-to-End: ✅ Substancial (10082 bytes)', details=None)", "ValidationResult(category='documentation', test_name='doc_M3_EXTRACTOR_TEST_PLAN.md', status=True, message='Plano de testes: ✅ Substancial (18014 bytes)', details=None)", "ValidationResult(category='documentation', test_name='doc_M3_EXTRACTOR_TEST_TRACEABILITY.md', status=True, message='Rastreabilidade: ✅ Substancial (12784 bytes)', details=None)"]}, "test_discovery": {"total": 1, "passed": 0, "results": ["ValidationResult(category='test_discovery', test_name='pytest_collect', status=False, message='Falha na descoberta de testes: ', details={'stderr': '', 'returncode': 2})"]}}, "details": [{"category": "structure_unit", "test": "file_test_unit_extraction.py", "status": "PASS", "message": "Arquivo test_unit_extraction.py encontrado", "details": {"path": "/volume01/devtools/projetos/Projeto Fred/versões/VS Code/v7/m3_extractor/tests/unit/test_unit_extraction.py"}}, {"category": "structure_unit", "test": "file_test_unit_chunking.py", "status": "PASS", "message": "Arquivo test_unit_chunking.py encontrado", "details": {"path": "/volume01/devtools/projetos/Projeto Fred/versões/VS Code/v7/m3_extractor/tests/unit/test_unit_chunking.py"}}, {"category": "structure_unit", "test": "file_test_unit_language.py", "status": "PASS", "message": "Arquivo test_unit_language.py encontrado", "details": {"path": "/volume01/devtools/projetos/Projeto Fred/versões/VS Code/v7/m3_extractor/tests/unit/test_unit_language.py"}}, {"category": "structure_unit", "test": "file_test_unit_config.py", "status": "PASS", "message": "Arquivo test_unit_config.py encontrado", "details": {"path": "/volume01/devtools/projetos/Projeto Fred/versões/VS Code/v7/m3_extractor/tests/unit/test_unit_config.py"}}, {"category": "structure_integration", "test": "file_test_rabbitmq_integration.py", "status": "PASS", "message": "Arquivo test_rabbitmq_integration.py encontrado", "details": {"path": "/volume01/devtools/projetos/Projeto Fred/versões/VS Code/v7/m3_extractor/tests/integration/test_rabbitmq_integration.py"}}, {"category": "structure_integration", "test": "file_test_rabbitmq_scenarios.py", "status": "PASS", "message": "Arquivo test_rabbitmq_scenarios.py encontrado", "details": {"path": "/volume01/devtools/projetos/Projeto Fred/versões/VS Code/v7/m3_extractor/tests/integration/test_rabbitmq_scenarios.py"}}, {"category": "structure_integration", "test": "file_test_postgresql_integration.py", "status": "PASS", "message": "Arquivo test_postgresql_integration.py encontrado", "details": {"path": "/volume01/devtools/projetos/Projeto Fred/versões/VS Code/v7/m3_extractor/tests/integration/test_postgresql_integration.py"}}, {"category": "structure_integration", "test": "file_test_postgresql_scenarios.py", "status": "PASS", "message": "Arquivo test_postgresql_scenarios.py encontrado", "details": {"path": "/volume01/devtools/projetos/Projeto Fred/versões/VS Code/v7/m3_extractor/tests/integration/test_postgresql_scenarios.py"}}, {"category": "structure_integration", "test": "file_test_end_to_end_pipeline.py", "status": "PASS", "message": "Arquivo test_end_to_end_pipeline.py encontrado", "details": {"path": "/volume01/devtools/projetos/Projeto Fred/versões/VS Code/v7/m3_extractor/tests/integration/test_end_to_end_pipeline.py"}}, {"category": "structure_integration", "test": "file_test_end_to_end_scenarios.py", "status": "PASS", "message": "Arquivo test_end_to_end_scenarios.py encontrado", "details": {"path": "/volume01/devtools/projetos/Projeto Fred/versões/VS Code/v7/m3_extractor/tests/integration/test_end_to_end_scenarios.py"}}, {"category": "structure_integration", "test": "file_test_m2_api_integration.py", "status": "PASS", "message": "Arquivo test_m2_api_integration.py encontrado", "details": {"path": "/volume01/devtools/projetos/Projeto Fred/versões/VS Code/v7/m3_extractor/tests/integration/test_m2_api_integration.py"}}, {"category": "structure_integration", "test": "file_conftest.py", "status": "PASS", "message": "Arquivo conftest.py encontrado", "details": {"path": "/volume01/devtools/projetos/Projeto Fred/versões/VS Code/v7/m3_extractor/tests/integration/conftest.py"}}, {"category": "structure_integration", "test": "file_rabbitmq_utils.py", "status": "PASS", "message": "Arquivo rabbitmq_utils.py encontrado", "details": {"path": "/volume01/devtools/projetos/Projeto Fred/versões/VS Code/v7/m3_extractor/tests/integration/rabbitmq_utils.py"}}, {"category": "structure_integration", "test": "file_postgresql_utils.py", "status": "PASS", "message": "Arquivo postgresql_utils.py encontrado", "details": {"path": "/volume01/devtools/projetos/Projeto Fred/versões/VS Code/v7/m3_extractor/tests/integration/postgresql_utils.py"}}, {"category": "structure_integration", "test": "file_end_to_end_utils.py", "status": "PASS", "message": "Arquivo end_to_end_utils.py encontrado", "details": {"path": "/volume01/devtools/projetos/Projeto Fred/versões/VS Code/v7/m3_extractor/tests/integration/end_to_end_utils.py"}}, {"category": "structure_docs", "test": "doc_README_RABBITMQ.md", "status": "PASS", "message": "Documentação README_RABBITMQ.md encontrada", "details": null}, {"category": "structure_docs", "test": "doc_README_POSTGRESQL.md", "status": "PASS", "message": "Documentação README_POSTGRESQL.md encontrada", "details": null}, {"category": "structure_docs", "test": "doc_README_END_TO_END.md", "status": "PASS", "message": "Documentação README_END_TO_END.md encontrada", "details": null}, {"category": "structure_fixtures", "test": "dir_pdfs", "status": "PASS", "message": "Diretório fixtures/pdfs encontrado", "details": null}, {"category": "structure_fixtures", "test": "dir_excel", "status": "PASS", "message": "Diretório fixtures/excel encontrado", "details": null}, {"category": "structure_fixtures", "test": "dir_text", "status": "PASS", "message": "Diretório fixtures/text encontrado", "details": null}, {"category": "structure_docker", "test": "config_docker-compose.test.yml", "status": "PASS", "message": "Config <PERSON><PERSON> docker-compose.test.yml encontrada", "details": null}, {"category": "config", "test": "pytest_ini", "status": "FAIL", "message": "pytest.ini AUSENTE - criar para configuração adequada", "details": null}, {"category": "config", "test": "requirements_test", "status": "PASS", "message": "requirements-test.txt encontrado", "details": null}, {"category": "config", "test": "docker_compose", "status": "PASS", "message": "docker-compose.test.yml encontrado", "details": null}, {"category": "config", "test": "docker_services", "status": "PASS", "message": "Serviços Docker: PostgreSQL=✅, RabbitMQ=✅", "details": null}, {"category": "documentation", "test": "doc_README.md", "status": "PASS", "message": "README principal: ✅ Substancial (13068 bytes)", "details": null}, {"category": "documentation", "test": "doc_README_RABBITMQ.md", "status": "PASS", "message": "Doc RabbitMQ: ✅ Substancial (8791 bytes)", "details": null}, {"category": "documentation", "test": "doc_README_POSTGRESQL.md", "status": "PASS", "message": "Doc PostgreSQL: ✅ Substancial (7702 bytes)", "details": null}, {"category": "documentation", "test": "doc_README_END_TO_END.md", "status": "PASS", "message": "Doc End-to-End: ✅ Substancial (10082 bytes)", "details": null}, {"category": "documentation", "test": "doc_M3_EXTRACTOR_TEST_PLAN.md", "status": "PASS", "message": "Plano de testes: ✅ Substancial (18014 bytes)", "details": null}, {"category": "documentation", "test": "doc_M3_EXTRACTOR_TEST_TRACEABILITY.md", "status": "PASS", "message": "Rastreabilidade: ✅ Substancial (12784 bytes)", "details": null}, {"category": "test_discovery", "test": "pytest_collect", "status": "FAIL", "message": "Falha na descoberta de testes: ", "details": {"stderr": "", "returncode": 2}}]}