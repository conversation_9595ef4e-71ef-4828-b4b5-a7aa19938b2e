[tool:pytest]
# Configuração do pytest para testes do M3 Extractor

# Diretórios de teste
testpaths = unit integration

# Padrões de arquivos de teste
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Marcadores personalizados
markers =
    critical: marca testes críticos que devem sempre passar
    integration: marca testes de integração que requerem serviços externos
    unit: marca testes unitários que não dependem de serviços externos
    slow: marca testes que demoram mais para executar
    rabbitmq: marca testes que requerem RabbitMQ
    postgresql: marca testes que requerem PostgreSQL
    end_to_end: marca testes end-to-end completos
    docker: marca testes que requerem Docker
    m2_api: marca testes relacionados à API do M2
    error_handling: marca testes de tratamento de erros
    performance: marca testes de performance
    security: marca testes de segurança

# Configurações de output
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --color=yes
    --durations=10

# Configurações de cobertura
# addopts = --cov=app --cov-report=html --cov-report=term-missing

# Filtros de warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:pika.*

# Configurações de timeout (se pytest-timeout estiver instalado)
timeout = 300
timeout_method = thread

# Configurações para testes assíncronos
asyncio_mode = auto

# Configurações de logging
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Configurações de descoberta de testes
minversion = 7.0
