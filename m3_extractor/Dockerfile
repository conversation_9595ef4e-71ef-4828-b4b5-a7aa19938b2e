# Usar uma imagem Python base leve
FROM python:3.11-slim

# Definir o diretório de trabalho no contêiner
WORKDIR /app

# Copiar o arquivo de dependências primeiro para aproveitar o cache do Docker
COPY requirements.txt .

# Instalar as dependências
# --no-cache-dir reduz o tamanho da imagem
# --trusted-host pypi.python.org -U pip setuptools wheel : pode ser necessário em algumas redes/configurações

# Instalar dependências do sistema para Tesseract OCR e poppler (para pdf2image)
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    tesseract-ocr \
    tesseract-ocr-por \
    tesseract-ocr-eng \
    poppler-utils \
    && apt-get clean && \
    rm -rf /var/lib/apt/lists/*

RUN pip install --no-cache-dir -r requirements.txt

# Copiar o restante do código da aplicação para o diretório de trabalho
# Copia o conteúdo de m3_extractor/app para /app/app no contêiner
COPY ./app /app/app

# Comando para rodar a aplicação (o consumidor RabbitMQ)
# Usamos -m para rodar o módulo app.consumer como um script
CMD ["python", "-m", "app.consumer"]
