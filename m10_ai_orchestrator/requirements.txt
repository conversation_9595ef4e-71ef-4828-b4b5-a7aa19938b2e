# M10 AI Orchestrator - Python Dependencies

# Web Framework
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
python-multipart>=0.0.6
websockets>=12.0

# Database
sqlalchemy>=2.0.0
alembic>=1.12.0
psycopg2-binary>=2.9.0
pydantic-settings>=2.0.0

# Cache
redis>=4.2.0

# HTTP Client for health checks
httpx>=0.25.0

# RabbitMQ
aioamqp>=0.15.0

# Monitoring and Metrics
psutil>=5.9.0
prometheus-client>=0.19.0

# Logging
structlog>=23.0.0

# Environment Variables
python-dotenv>=1.0.0

# LLM Integration
openai>=1.0.0
tiktoken>=0.5.0

# UUID support
uuid>=1.30

# Development and Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
