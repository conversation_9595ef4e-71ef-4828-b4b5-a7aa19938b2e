#!/usr/bin/env python3
"""
Script para criar as tabelas do banco de dados do M10 AI Orchestrator.
"""

import sys
import os

# Adicionar o diretório pai ao path para imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import init_database

if __name__ == "__main__":
    try:
        print("Criando tabelas do banco de dados...")
        init_database()
        print("✅ Tabelas criadas com sucesso!")
    except Exception as e:
        print(f"❌ Erro ao criar tabelas: {e}")
        sys.exit(1)
