#!/usr/bin/env python3
"""
Script para debugar o NLQueryProcessor e verificar como está interpretando as mensagens.
"""

import sys
import os
sys.path.append('/app')

from app.services.nl_query_processor import NLQueryProcessor, QueryType

def test_query_processing():
    """Testa o processamento de consultas específicas."""

    processor = NLQueryProcessor(llm_service=None)
    
    # Mensagens de teste do usuário
    test_queries = [
        "Quais documentos que falam de testes temos na base dedados?",
        "Ok, me traga um resumo geral do documento ChecklistAnchor", 
        "Faça uma análises detalhadas de qualquer produto específico. Basta me dizer qual produto você gostaria de analisar!",
        "o que você pode falar sobre teste?",
        "analise o produto ChecklistAnchor",
        "quais informações temos sobre MEDF?",
        "compare as versões do teste1 e teste2"
    ]
    
    print("=== DEBUG NL QUERY PROCESSOR ===\n")
    
    for i, query in enumerate(test_queries, 1):
        print(f"🔍 TESTE {i}: {query}")
        print("-" * 80)
        
        try:
            result = processor.process_query(query)
            
            print(f"✅ RESULTADO:")
            print(f"   Tipo: {result.query_type}")
            print(f"   Confiança: {result.confidence}")
            print(f"   Entidades: {result.entities}")
            print(f"   Parâmetros: {result.parameters}")
            print(f"   Query Original: {result.original_query}")
            
            # Verificar se a classificação está correta
            expected_types = {
                0: QueryType.DOCUMENT_SEARCH,  # "documentos que falam de testes"
                1: QueryType.PRODUCT_ANALYSIS,  # "resumo do documento ChecklistAnchor"
                2: QueryType.GENERAL_CHAT,      # "Faça uma análises detalhadas..."
                3: QueryType.DOCUMENT_SEARCH,   # "o que você pode falar sobre teste"
                4: QueryType.PRODUCT_ANALYSIS,  # "analise o produto ChecklistAnchor"
                5: QueryType.DOCUMENT_SEARCH,   # "quais informações temos sobre MEDF"
                6: QueryType.VERSION_COMPARISON # "compare as versões"
            }
            
            expected = expected_types.get(i-1)
            if expected and result.query_type == expected:
                print(f"   ✅ CLASSIFICAÇÃO CORRETA!")
            else:
                print(f"   ❌ CLASSIFICAÇÃO INCORRETA! Esperado: {expected}")
                
        except Exception as e:
            print(f"❌ ERRO: {e}")
            
        print("\n" + "="*80 + "\n")

def test_regex_patterns():
    """Testa os padrões regex individualmente."""

    import re

    processor = NLQueryProcessor(llm_service=None)
    
    print("=== TESTE DOS PADRÕES REGEX ===\n")
    
    test_query = "Quais documentos que falam de testes temos na base dedados?"
    
    print(f"Query de teste: {test_query}")
    print("-" * 50)
    
    for pattern_type, patterns in processor.patterns.items():
        print(f"\n🔍 Testando padrões para: {pattern_type}")
        
        for i, pattern in enumerate(patterns):
            try:
                match = re.search(pattern, test_query.lower(), re.IGNORECASE)
                if match:
                    print(f"   ✅ Padrão {i+1} MATCH: {pattern}")
                    print(f"      Grupos: {match.groups()}")
                else:
                    print(f"   ❌ Padrão {i+1} NO MATCH: {pattern}")
            except Exception as e:
                print(f"   💥 Padrão {i+1} ERRO: {e}")

if __name__ == "__main__":
    print("Iniciando debug do NL Query Processor...\n")
    
    test_regex_patterns()
    print("\n" + "="*100 + "\n")
    test_query_processing()
