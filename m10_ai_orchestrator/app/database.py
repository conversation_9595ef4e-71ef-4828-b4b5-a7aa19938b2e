"""
M10 AI Orchestrator - Database Configuration

Database setup and session management for the AI Orchestrator service.
"""

import os
from typing import Generator
from sqlalchemy import create_engine, MetaData
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
import structlog

from .models.base import Base

logger = structlog.get_logger()


class DatabaseConfig:
    """Database configuration and connection management."""
    
    def __init__(self):
        """Initialize database configuration."""
        self.database_url = self._get_database_url()
        self.engine = self._create_engine()
        self.SessionLocal = sessionmaker(
            autocommit=False, 
            autoflush=False, 
            bind=self.engine
        )
        
        logger.info("Database configuration initialized", database_url=self.database_url)
    
    def _get_database_url(self) -> str:
        """Get database URL from environment or use SQLite default."""
        database_url = os.getenv(
            "DATABASE_URL", 
            "sqlite:///./m10_ai_orchestrator.db"
        )
        
        # Handle SQLite URL format
        if database_url.startswith("sqlite:///"):
            # Ensure directory exists
            db_path = database_url.replace("sqlite:///", "")
            db_dir = os.path.dirname(db_path)
            if db_dir:
                os.makedirs(db_dir, exist_ok=True)
        
        return database_url
    
    def _create_engine(self):
        """Create SQLAlchemy engine with appropriate configuration."""
        if self.database_url.startswith("sqlite"):
            # SQLite configuration
            engine = create_engine(
                self.database_url,
                connect_args={"check_same_thread": False},
                poolclass=StaticPool,
                echo=os.getenv("SQL_ECHO", "false").lower() == "true"
            )
        else:
            # PostgreSQL or other database configuration
            # Configure schema search path for ai_orchestrator
            connect_args = {
                "options": "-csearch_path=ai_orchestrator,public"
            }
            engine = create_engine(
                self.database_url,
                pool_pre_ping=True,
                pool_recycle=300,
                connect_args=connect_args,
                echo=os.getenv("SQL_ECHO", "false").lower() == "true"
            )
        
        return engine
    
    def create_all_tables(self):
        """Create all database tables."""
        try:
            Base.metadata.create_all(bind=self.engine)
            logger.info("All database tables created successfully")
        except Exception as e:
            logger.error("Failed to create database tables", error=str(e))
            raise
    
    def drop_all_tables(self):
        """Drop all database tables."""
        try:
            Base.metadata.drop_all(bind=self.engine)
            logger.info("All database tables dropped successfully")
        except Exception as e:
            logger.error("Failed to drop database tables", error=str(e))
            raise
    
    def get_session(self) -> Session:
        """Get a new database session."""
        return self.SessionLocal()
    
    def get_session_generator(self) -> Generator[Session, None, None]:
        """Get a database session generator for dependency injection."""
        session = self.SessionLocal()
        try:
            yield session
        finally:
            session.close()


# Global database configuration instance - será inicializado após carregamento do .env
db_config = None


def init_db_config():
    """Inicializa a configuração do banco após carregamento do .env"""
    global db_config
    db_config = DatabaseConfig()
    logger.info("Database configuration reinitialized after .env loading",
                database_url=db_config._get_database_url())


def get_database_session() -> Generator[Session, None, None]:
    """
    Dependency function to get database session.

    Yields:
        Session: Database session
    """
    global db_config
    if db_config is None:
        db_config = DatabaseConfig()

    session = db_config.SessionLocal()
    try:
        yield session
    finally:
        session.close()


def init_database():
    """Initialize database by creating all tables."""
    db_config.create_all_tables()


def reset_database():
    """Reset database by dropping and recreating all tables."""
    db_config.drop_all_tables()
    db_config.create_all_tables()


def get_db_session() -> Session:
    """Get a new database session (for testing)."""
    return db_config.get_session()
