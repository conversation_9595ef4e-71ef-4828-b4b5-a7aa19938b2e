"""
M10 AI Orchestrator - Base Repository

Base repository class providing common CRUD operations and database utilities
that all specific repositories inherit from.
"""

from typing import Generic, TypeVar, Type, List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import func, and_, or_
import uuid
import structlog

from ..models.base import BaseModel

# Generic type for the model
ModelType = TypeVar("ModelType", bound=BaseModel)

logger = structlog.get_logger()


class BaseRepository(Generic[ModelType]):
    """
    Base repository providing common database operations.
    
    Implements the Repository pattern with generic CRUD operations
    that can be used by all specific model repositories.
    """
    
    def __init__(self, model: Type[ModelType], db_session: Session):
        """
        Initialize the repository.
        
        Args:
            model: The SQLAlchemy model class
            db_session: Database session
        """
        self.model = model
        self.db_session = db_session
        self.logger = logger.bind(repository=self.__class__.__name__, model=model.__name__)
    
    def create(self, **kwargs) -> ModelType:
        """
        Create a new record.
        
        Args:
            **kwargs: Attributes for the new record
            
        Returns:
            ModelType: The created record
            
        Raises:
            SQLAlchemyError: If database operation fails
        """
        try:
            instance = self.model(**kwargs)
            self.db_session.add(instance)
            self.db_session.flush()  # Get the ID without committing
            self.logger.info("Record created", record_id=str(instance.id))
            return instance
        except SQLAlchemyError as e:
            self.logger.error("Failed to create record", error=str(e))
            self.db_session.rollback()
            raise
    
    def get_by_id(self, record_id: uuid.UUID) -> Optional[ModelType]:
        """
        Get a record by its ID.
        
        Args:
            record_id: The record ID
            
        Returns:
            Optional[ModelType]: The record if found, None otherwise
        """
        try:
            record = self.db_session.query(self.model).filter(
                self.model.id == record_id
            ).first()
            
            if record:
                self.logger.debug("Record found", record_id=str(record_id))
            else:
                self.logger.debug("Record not found", record_id=str(record_id))
            
            return record
        except SQLAlchemyError as e:
            self.logger.error("Failed to get record by ID", record_id=str(record_id), error=str(e))
            raise
    
    def get_all(self, limit: int = 100, offset: int = 0) -> List[ModelType]:
        """
        Get all records with pagination.
        
        Args:
            limit: Maximum number of records to return
            offset: Number of records to skip
            
        Returns:
            List[ModelType]: List of records
        """
        try:
            records = (
                self.db_session.query(self.model)
                .offset(offset)
                .limit(limit)
                .all()
            )
            
            self.logger.debug("Retrieved records", count=len(records), limit=limit, offset=offset)
            return records
        except SQLAlchemyError as e:
            self.logger.error("Failed to get all records", error=str(e))
            raise
    
    def update(self, record_id: uuid.UUID, **kwargs) -> Optional[ModelType]:
        """
        Update a record by its ID.
        
        Args:
            record_id: The record ID
            **kwargs: Attributes to update
            
        Returns:
            Optional[ModelType]: The updated record if found, None otherwise
            
        Raises:
            SQLAlchemyError: If database operation fails
        """
        try:
            record = self.get_by_id(record_id)
            if not record:
                self.logger.warning("Record not found for update", record_id=str(record_id))
                return None
            
            for key, value in kwargs.items():
                if hasattr(record, key):
                    setattr(record, key, value)
            
            self.db_session.flush()
            self.logger.info("Record updated", record_id=str(record_id), fields=list(kwargs.keys()))
            return record
        except SQLAlchemyError as e:
            self.logger.error("Failed to update record", record_id=str(record_id), error=str(e))
            self.db_session.rollback()
            raise
    
    def delete(self, record_id: uuid.UUID) -> bool:
        """
        Delete a record by its ID.
        
        Args:
            record_id: The record ID
            
        Returns:
            bool: True if record was deleted, False if not found
            
        Raises:
            SQLAlchemyError: If database operation fails
        """
        try:
            record = self.get_by_id(record_id)
            if not record:
                self.logger.warning("Record not found for deletion", record_id=str(record_id))
                return False
            
            self.db_session.delete(record)
            self.db_session.flush()
            self.logger.info("Record deleted", record_id=str(record_id))
            return True
        except SQLAlchemyError as e:
            self.logger.error("Failed to delete record", record_id=str(record_id), error=str(e))
            self.db_session.rollback()
            raise
    
    def count(self, **filters) -> int:
        """
        Count records with optional filters.
        
        Args:
            **filters: Filter conditions
            
        Returns:
            int: Number of records matching the filters
        """
        try:
            query = self.db_session.query(func.count(self.model.id))
            
            # Apply filters
            for key, value in filters.items():
                if hasattr(self.model, key):
                    query = query.filter(getattr(self.model, key) == value)
            
            count = query.scalar()
            self.logger.debug("Counted records", count=count, filters=filters)
            return count
        except SQLAlchemyError as e:
            self.logger.error("Failed to count records", error=str(e))
            raise
    
    def exists(self, record_id: uuid.UUID) -> bool:
        """
        Check if a record exists by its ID.
        
        Args:
            record_id: The record ID
            
        Returns:
            bool: True if record exists, False otherwise
        """
        try:
            exists = self.db_session.query(
                self.db_session.query(self.model).filter(
                    self.model.id == record_id
                ).exists()
            ).scalar()
            
            self.logger.debug("Checked record existence", record_id=str(record_id), exists=exists)
            return exists
        except SQLAlchemyError as e:
            self.logger.error("Failed to check record existence", record_id=str(record_id), error=str(e))
            raise
    
    def find_by(self, limit: int = 100, offset: int = 0, **filters) -> List[ModelType]:
        """
        Find records by filter conditions.
        
        Args:
            limit: Maximum number of records to return
            offset: Number of records to skip
            **filters: Filter conditions
            
        Returns:
            List[ModelType]: List of records matching the filters
        """
        try:
            query = self.db_session.query(self.model)
            
            # Apply filters
            for key, value in filters.items():
                if hasattr(self.model, key):
                    if isinstance(value, list):
                        query = query.filter(getattr(self.model, key).in_(value))
                    else:
                        query = query.filter(getattr(self.model, key) == value)
            
            records = query.offset(offset).limit(limit).all()
            self.logger.debug("Found records by filters", count=len(records), filters=filters)
            return records
        except SQLAlchemyError as e:
            self.logger.error("Failed to find records by filters", filters=filters, error=str(e))
            raise
    
    def find_one_by(self, **filters) -> Optional[ModelType]:
        """
        Find a single record by filter conditions.
        
        Args:
            **filters: Filter conditions
            
        Returns:
            Optional[ModelType]: The first record matching the filters, or None
        """
        try:
            query = self.db_session.query(self.model)
            
            # Apply filters
            for key, value in filters.items():
                if hasattr(self.model, key):
                    query = query.filter(getattr(self.model, key) == value)
            
            record = query.first()
            if record:
                self.logger.debug("Found record by filters", record_id=str(record.id), filters=filters)
            else:
                self.logger.debug("No record found by filters", filters=filters)
            
            return record
        except SQLAlchemyError as e:
            self.logger.error("Failed to find record by filters", filters=filters, error=str(e))
            raise
    
    def bulk_create(self, records_data: List[Dict[str, Any]]) -> List[ModelType]:
        """
        Create multiple records in a single operation.
        
        Args:
            records_data: List of dictionaries containing record data
            
        Returns:
            List[ModelType]: List of created records
            
        Raises:
            SQLAlchemyError: If database operation fails
        """
        try:
            instances = [self.model(**data) for data in records_data]
            self.db_session.add_all(instances)
            self.db_session.flush()
            
            self.logger.info("Bulk created records", count=len(instances))
            return instances
        except SQLAlchemyError as e:
            self.logger.error("Failed to bulk create records", count=len(records_data), error=str(e))
            self.db_session.rollback()
            raise
    
    def get_or_create(self, defaults: Optional[Dict[str, Any]] = None, **kwargs) -> tuple[ModelType, bool]:
        """
        Get an existing record or create a new one.
        
        Args:
            defaults: Default values for new record creation
            **kwargs: Filter conditions for finding existing record
            
        Returns:
            tuple[ModelType, bool]: (record, created) where created is True if record was created
        """
        try:
            record = self.find_one_by(**kwargs)
            if record:
                self.logger.debug("Found existing record", record_id=str(record.id))
                return record, False
            
            # Create new record with defaults
            create_data = kwargs.copy()
            if defaults:
                create_data.update(defaults)
            
            record = self.create(**create_data)
            self.logger.info("Created new record", record_id=str(record.id))
            return record, True
        except SQLAlchemyError as e:
            self.logger.error("Failed to get or create record", error=str(e))
            raise
    
    def commit(self):
        """Commit the current transaction."""
        try:
            self.db_session.commit()
            self.logger.debug("Transaction committed")
        except SQLAlchemyError as e:
            self.logger.error("Failed to commit transaction", error=str(e))
            self.db_session.rollback()
            raise
    
    def rollback(self):
        """Rollback the current transaction."""
        try:
            self.db_session.rollback()
            self.logger.debug("Transaction rolled back")
        except SQLAlchemyError as e:
            self.logger.error("Failed to rollback transaction", error=str(e))
            raise
