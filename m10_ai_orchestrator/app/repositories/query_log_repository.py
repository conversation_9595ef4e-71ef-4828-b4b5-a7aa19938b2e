"""
M10 AI Orchestrator - Query Log Repository

Repository class for QueryLog model providing specialized database operations
for query logging, performance analytics, and debugging support.
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import func, desc, and_, or_
from datetime import datetime, timedelta
import uuid

from .base_repository import BaseRepository
from ..models.query_log import QueryLog, QueryType, QueryStatus


class QueryLogRepository(BaseRepository[QueryLog]):
    """
    Repository for QueryLog model with specialized methods for
    query logging, performance analysis, and system monitoring.
    """
    
    def __init__(self, db_session: Session):
        """Initialize the QueryLog repository."""
        super().__init__(QueryLog, db_session)
    
    def create_query_log(
        self,
        query_id: str,
        query_type: str,
        original_query: str,
        session_id: Optional[uuid.UUID] = None,
        processed_query: Optional[str] = None,
        detected_intent: Optional[str] = None,
        intent_confidence: Optional[float] = None,
        user_agent: Optional[str] = None,
        ip_address: Optional[str] = None,
        request_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> QueryLog:
        """
        Create a new query log entry.
        
        Args:
            query_id: Unique identifier for the query
            query_type: Type of query being logged
            original_query: The original user query
            session_id: Optional chat session ID
            processed_query: Processed/normalized query
            detected_intent: Detected intent from classification
            intent_confidence: Confidence score for intent
            user_agent: User agent string
            ip_address: IP address of requester
            request_id: Request ID for tracing
            metadata: Additional metadata
            
        Returns:
            QueryLog: The created query log
        """
        log_data = {
            "query_id": query_id,
            "query_type": query_type,
            "original_query": original_query,
            "session_id": session_id,
            "processed_query": processed_query,
            "detected_intent": detected_intent,
            "intent_confidence": intent_confidence,
            "user_agent": user_agent,
            "ip_address": ip_address,
            "request_id": request_id,
            "metadata": metadata or {},
            "status": 'pending'
        }
        
        return self.create(**log_data)
    
    def get_by_session(
        self,
        session_id: uuid.UUID,
        limit: int = 100,
        offset: int = 0
    ) -> List[QueryLog]:
        """
        Get query logs for a specific session.
        
        Args:
            session_id: Session ID
            limit: Maximum number of logs to return
            offset: Number of logs to skip
            
        Returns:
            List[QueryLog]: Query logs for the session
        """
        try:
            logs = (
                self.db_session.query(QueryLog)
                .filter(QueryLog.session_id == session_id)
                .order_by(desc(QueryLog.created_at))
                .offset(offset)
                .limit(limit)
                .all()
            )
            
            self.logger.debug(
                "Retrieved session query logs",
                session_id=str(session_id),
                count=len(logs)
            )
            
            return logs
        except Exception as e:
            self.logger.error(
                "Failed to get session query logs",
                session_id=str(session_id),
                error=str(e)
            )
            raise
    
    def get_by_query_type(
        self,
        query_type: str,
        hours: int = 24,
        limit: int = 100
    ) -> List[QueryLog]:
        """
        Get query logs by type within a time window.
        
        Args:
            query_type: Type of query to filter by
            hours: Hours to look back
            limit: Maximum number of logs
            
        Returns:
            List[QueryLog]: Query logs of the specified type
        """
        try:
            start_time = datetime.utcnow() - timedelta(hours=hours)
            
            logs = (
                self.db_session.query(QueryLog)
                .filter(
                    and_(
                        QueryLog.query_type == query_type,
                        QueryLog.created_at >= start_time
                    )
                )
                .order_by(desc(QueryLog.created_at))
                .limit(limit)
                .all()
            )
            
            self.logger.debug(
                "Retrieved query logs by type",
                query_type=query_type,
                hours=hours,
                count=len(logs)
            )
            
            return logs
        except Exception as e:
            self.logger.error(
                "Failed to get query logs by type",
                query_type=query_type,
                error=str(e)
            )
            raise
    
    def get_failed_queries(
        self,
        hours: int = 24,
        limit: int = 100
    ) -> List[QueryLog]:
        """
        Get queries that failed processing.
        
        Args:
            hours: Hours to look back
            limit: Maximum number of logs
            
        Returns:
            List[QueryLog]: Failed query logs
        """
        try:
            start_time = datetime.utcnow() - timedelta(hours=hours)
            
            failed_logs = (
                self.db_session.query(QueryLog)
                .filter(
                    and_(
                        QueryLog.status.in_([
                            'failed',
                            'timeout'
                        ]),
                        QueryLog.created_at >= start_time
                    )
                )
                .order_by(desc(QueryLog.created_at))
                .limit(limit)
                .all()
            )
            
            self.logger.debug(
                "Retrieved failed queries",
                hours=hours,
                count=len(failed_logs)
            )
            
            return failed_logs
        except Exception as e:
            self.logger.error("Failed to get failed queries", error=str(e))
            raise
    
    def update_routing_info(
        self,
        query_id: str,
        routed_to_service: str,
        routing_reason: str,
        fallback_used: bool = False
    ) -> Optional[QueryLog]:
        """
        Update routing information for a query.
        
        Args:
            query_id: Query ID
            routed_to_service: Service the query was routed to
            routing_reason: Reason for routing decision
            fallback_used: Whether fallback routing was used
            
        Returns:
            Optional[QueryLog]: Updated query log or None if not found
        """
        try:
            log = self.find_one_by(query_id=query_id)
            if not log:
                return None
            
            log.routed_to_service = routed_to_service
            log.routing_reason = routing_reason
            log.fallback_used = fallback_used
            
            self.db_session.flush()
            
            self.logger.info(
                "Updated routing info",
                query_id=query_id,
                routed_to_service=routed_to_service,
                fallback_used=fallback_used
            )
            
            return log
        except Exception as e:
            self.logger.error(
                "Failed to update routing info",
                query_id=query_id,
                error=str(e)
            )
            raise
    
    def update_performance_metrics(
        self,
        query_id: str,
        total_execution_time_ms: int,
        intent_classification_time_ms: Optional[int] = None,
        context_retrieval_time_ms: Optional[int] = None,
        llm_processing_time_ms: Optional[int] = None,
        response_formatting_time_ms: Optional[int] = None
    ) -> Optional[QueryLog]:
        """
        Update performance timing metrics for a query.
        
        Args:
            query_id: Query ID
            total_execution_time_ms: Total execution time
            intent_classification_time_ms: Intent classification time
            context_retrieval_time_ms: Context retrieval time
            llm_processing_time_ms: LLM processing time
            response_formatting_time_ms: Response formatting time
            
        Returns:
            Optional[QueryLog]: Updated query log or None if not found
        """
        try:
            log = self.find_one_by(query_id=query_id)
            if not log:
                return None
            
            log.total_execution_time_ms = total_execution_time_ms
            log.intent_classification_time_ms = intent_classification_time_ms
            log.context_retrieval_time_ms = context_retrieval_time_ms
            log.llm_processing_time_ms = llm_processing_time_ms
            log.response_formatting_time_ms = response_formatting_time_ms
            
            self.db_session.flush()
            
            self.logger.info(
                "Updated performance metrics",
                query_id=query_id,
                total_time_ms=total_execution_time_ms
            )
            
            return log
        except Exception as e:
            self.logger.error(
                "Failed to update performance metrics",
                query_id=query_id,
                error=str(e)
            )
            raise
    
    def update_usage_metrics(
        self,
        query_id: str,
        prompt_tokens: int,
        completion_tokens: int,
        total_tokens: int,
        cost_usd: float,
        context_sources_count: int = 0,
        context_tokens_used: int = 0,
        context_truncated: bool = False,
        response_length: Optional[int] = None,
        citations_count: int = 0
    ) -> Optional[QueryLog]:
        """
        Update usage and cost metrics for a query.
        
        Args:
            query_id: Query ID
            prompt_tokens: Tokens used in prompt
            completion_tokens: Tokens used in completion
            total_tokens: Total tokens used
            cost_usd: Cost in USD
            context_sources_count: Number of context sources
            context_tokens_used: Tokens used for context
            context_truncated: Whether context was truncated
            response_length: Length of response in characters
            citations_count: Number of citations
            
        Returns:
            Optional[QueryLog]: Updated query log or None if not found
        """
        try:
            log = self.find_one_by(query_id=query_id)
            if not log:
                return None
            
            log.prompt_tokens = prompt_tokens
            log.completion_tokens = completion_tokens
            log.total_tokens = total_tokens
            log.cost_usd = cost_usd
            log.context_sources_count = context_sources_count
            log.context_tokens_used = context_tokens_used
            log.context_truncated = context_truncated
            log.response_length = response_length
            log.citations_count = citations_count
            
            self.db_session.flush()
            
            self.logger.info(
                "Updated usage metrics",
                query_id=query_id,
                total_tokens=total_tokens,
                cost_usd=cost_usd
            )
            
            return log
        except Exception as e:
            self.logger.error(
                "Failed to update usage metrics",
                query_id=query_id,
                error=str(e)
            )
            raise
    
    def complete_query(
        self,
        query_id: str,
        response_quality_score: Optional[float] = None
    ) -> bool:
        """
        Mark a query as completed.
        
        Args:
            query_id: Query ID
            response_quality_score: Optional quality score
            
        Returns:
            bool: True if query was completed, False if not found
        """
        try:
            log = self.find_one_by(query_id=query_id)
            if not log:
                return False
            
            log.set_completed()
            if response_quality_score is not None:
                log.response_quality_score = response_quality_score
            
            self.db_session.flush()
            
            self.logger.info("Query completed", query_id=query_id)
            return True
        except Exception as e:
            self.logger.error(
                "Failed to complete query",
                query_id=query_id,
                error=str(e)
            )
            raise
    
    def fail_query(
        self,
        query_id: str,
        error_message: str,
        error_details: Optional[Dict] = None,
        error_service: Optional[str] = None
    ) -> bool:
        """
        Mark a query as failed.
        
        Args:
            query_id: Query ID
            error_message: Error message
            error_details: Detailed error information
            error_service: Service where error occurred
            
        Returns:
            bool: True if query was marked as failed, False if not found
        """
        try:
            log = self.find_one_by(query_id=query_id)
            if not log:
                return False
            
            log.set_failed(error_message, error_details, error_service)
            self.db_session.flush()
            
            self.logger.warning(
                "Query failed",
                query_id=query_id,
                error_message=error_message,
                error_service=error_service
            )
            return True
        except Exception as e:
            self.logger.error(
                "Failed to mark query as failed",
                query_id=query_id,
                error=str(e)
            )
            raise
    
    def get_performance_stats(
        self,
        query_type: Optional[str] = None,
        days: int = 7
    ) -> Dict[str, Any]:
        """
        Get performance statistics for queries.
        
        Args:
            query_type: Optional query type filter
            days: Number of days to analyze
            
        Returns:
            Dict[str, Any]: Performance statistics
        """
        try:
            start_date = datetime.utcnow() - timedelta(days=days)
            
            query = (
                self.db_session.query(QueryLog)
                .filter(QueryLog.created_at >= start_date)
            )
            
            if query_type:
                query = query.filter(QueryLog.query_type == query_type)
            
            logs = query.all()
            
            # Calculate statistics
            total_queries = len(logs)
            completed_queries = [log for log in logs if log.is_completed()]
            failed_queries = [log for log in logs if log.is_failed()]
            
            # Performance metrics
            execution_times = [
                log.total_execution_time_ms for log in completed_queries
                if log.total_execution_time_ms is not None
            ]
            
            # Token and cost metrics
            total_tokens = sum(log.total_tokens for log in completed_queries)
            total_cost = sum(log.cost_usd for log in completed_queries)
            
            # Service distribution
            service_counts = {}
            for log in logs:
                if log.routed_to_service:
                    service_counts[log.routed_to_service] = service_counts.get(log.routed_to_service, 0) + 1
            
            stats = {
                "total_queries": total_queries,
                "completed_queries": len(completed_queries),
                "failed_queries": len(failed_queries),
                "success_rate": (
                    len(completed_queries) / total_queries * 100
                    if total_queries > 0 else 0
                ),
                "performance_metrics": {
                    "execution_times_count": len(execution_times),
                    "average_execution_time_ms": (
                        sum(execution_times) / len(execution_times)
                        if execution_times else 0
                    ),
                    "min_execution_time_ms": min(execution_times) if execution_times else 0,
                    "max_execution_time_ms": max(execution_times) if execution_times else 0,
                    "queries_under_5s": len([t for t in execution_times if t <= 5000]),
                    "queries_over_10s": len([t for t in execution_times if t > 10000])
                },
                "usage_metrics": {
                    "total_tokens": total_tokens,
                    "total_cost_usd": total_cost,
                    "average_tokens_per_query": (
                        total_tokens / len(completed_queries)
                        if completed_queries else 0
                    ),
                    "average_cost_per_query": (
                        total_cost / len(completed_queries)
                        if completed_queries else 0
                    )
                },
                "service_distribution": service_counts,
                "date_range": {
                    "start_date": start_date.isoformat(),
                    "end_date": datetime.utcnow().isoformat(),
                    "days": days
                }
            }
            
            self.logger.debug(
                "Generated performance stats",
                query_type=query_type,
                total_queries=total_queries,
                days=days
            )
            
            return stats
        except Exception as e:
            self.logger.error("Failed to get performance stats", error=str(e))
            raise
    
    def get_error_analysis(
        self,
        hours: int = 24,
        limit: int = 100
    ) -> Dict[str, Any]:
        """
        Get error analysis for debugging.
        
        Args:
            hours: Hours to look back
            limit: Maximum number of errors to analyze
            
        Returns:
            Dict[str, Any]: Error analysis
        """
        try:
            start_time = datetime.utcnow() - timedelta(hours=hours)
            
            failed_logs = (
                self.db_session.query(QueryLog)
                .filter(
                    and_(
                        QueryLog.status.in_([
                            'failed',
                            'timeout'
                        ]),
                        QueryLog.created_at >= start_time
                    )
                )
                .order_by(desc(QueryLog.created_at))
                .limit(limit)
                .all()
            )
            
            # Group errors by service and message
            error_by_service = {}
            error_by_message = {}
            
            for log in failed_logs:
                # By service
                service = log.error_service or "unknown"
                if service not in error_by_service:
                    error_by_service[service] = []
                error_by_service[service].append({
                    "query_id": log.query_id,
                    "error_message": log.error_message,
                    "created_at": log.created_at.isoformat()
                })
                
                # By error message
                message = log.error_message or "Unknown error"
                error_by_message[message] = error_by_message.get(message, 0) + 1
            
            analysis = {
                "total_errors": len(failed_logs),
                "timeframe_hours": hours,
                "errors_by_service": error_by_service,
                "error_frequency": dict(
                    sorted(error_by_message.items(), key=lambda x: x[1], reverse=True)
                ),
                "recent_errors": [
                    {
                        "query_id": log.query_id,
                        "query_type": log.query_type,
                        "error_message": log.error_message,
                        "error_service": log.error_service,
                        "created_at": log.created_at.isoformat()
                    }
                    for log in failed_logs[:10]  # Last 10 errors
                ]
            }
            
            self.logger.debug(
                "Generated error analysis",
                total_errors=len(failed_logs),
                hours=hours
            )
            
            return analysis
        except Exception as e:
            self.logger.error("Failed to get error analysis", error=str(e))
            raise
    
    def cleanup_old_logs(
        self,
        days: int = 30,
        keep_failed: bool = True
    ) -> int:
        """
        Clean up old query logs to manage database size.
        
        Args:
            days: Age threshold in days
            keep_failed: Whether to keep failed queries for debugging
            
        Returns:
            int: Number of logs deleted
        """
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days)
            
            query = (
                self.db_session.query(QueryLog)
                .filter(QueryLog.created_at < cutoff_date)
            )
            
            if keep_failed:
                query = query.filter(QueryLog.status == 'completed')
            
            old_logs = query.all()
            count = len(old_logs)
            
            for log in old_logs:
                self.db_session.delete(log)
            
            if count > 0:
                self.db_session.flush()
            
            self.logger.info(
                "Cleaned up old query logs",
                count=count,
                days=days,
                keep_failed=keep_failed
            )
            
            return count
        except Exception as e:
            self.logger.error("Failed to cleanup old logs", error=str(e))
            raise
