"""
M10 AI Orchestrator - Message Repository

Repository class for Message model providing specialized database operations
for message management, conversation retrieval, and performance analytics.
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import func, desc, and_, or_
from datetime import datetime, timedelta
import uuid

from .base_repository import BaseRepository
from ..models.message import Message, MessageRole, MessageStatus


class MessageRepository(BaseRepository[Message]):
    """
    Repository for Message model with specialized methods for
    message management, conversation handling, and analytics.
    """
    
    def __init__(self, db_session: Session):
        """Initialize the Message repository."""
        super().__init__(Message, db_session)
    
    def create_message(
        self,
        session_id: uuid.UUID,
        role: str,
        content: str,
        sequence_number: int,
        intent_classification: Optional[str] = None,
        intent_confidence: Optional[float] = None,
        context_retrieved: bool = False,
        context_sources: Optional[List[Dict]] = None,
        model_name: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Message:
        """
        Create a new message in a chat session.
        
        Args:
            session_id: ID of the chat session
            role: Message role (user, assistant, system)
            content: Message content
            sequence_number: Sequential order in conversation
            intent_classification: Classified intent
            intent_confidence: Confidence score for intent
            context_retrieved: Whether context was retrieved
            context_sources: Sources used for context
            model_name: LLM model used
            temperature: Temperature setting
            max_tokens: Max tokens setting
            metadata: Additional metadata
            
        Returns:
            Message: The created message
        """
        message_data = {
            "session_id": session_id,
            "role": role,
            "content": content,
            "sequence_number": sequence_number,
            "intent_classification": intent_classification,
            "intent_confidence": intent_confidence,
            "context_retrieved": context_retrieved,
            "context_sources": context_sources or [],
            "model_name": model_name,
            "temperature": temperature,
            "max_tokens": max_tokens,
            "metadata": metadata or {},
            "status": 'pending'
        }
        
        return self.create(**message_data)
    
    def get_by_session(
        self,
        session_id: uuid.UUID,
        limit: int = 50,
        offset: int = 0,
        include_citations: bool = True
    ) -> List[Message]:
        """
        Get messages for a specific chat session.
        
        Args:
            session_id: Session ID
            limit: Maximum number of messages to return
            offset: Number of messages to skip
            include_citations: Whether to include citations
            
        Returns:
            List[Message]: Messages in the session
        """
        try:
            query = (
                self.db_session.query(Message)
                .filter(Message.session_id == session_id)
                .order_by(Message.sequence_number)
            )
            
            if include_citations:
                query = query.options(joinedload(Message.citations))
            
            messages = query.offset(offset).limit(limit).all()
            
            self.logger.debug(
                "Retrieved session messages",
                session_id=str(session_id),
                count=len(messages),
                include_citations=include_citations
            )
            
            return messages
        except Exception as e:
            self.logger.error(
                "Failed to get session messages",
                session_id=str(session_id),
                error=str(e)
            )
            raise
    
    def get_conversation_context(
        self,
        session_id: uuid.UUID,
        max_messages: int = 10,
        max_tokens: int = 4000
    ) -> List[Message]:
        """
        Get recent messages for conversation context.
        
        Args:
            session_id: Session ID
            max_messages: Maximum number of messages
            max_tokens: Maximum total tokens to include
            
        Returns:
            List[Message]: Messages suitable for context
        """
        try:
            messages = (
                self.db_session.query(Message)
                .filter(
                    and_(
                        Message.session_id == session_id,
                        Message.status == 'completed'
                    )
                )
                .order_by(desc(Message.sequence_number))
                .limit(max_messages)
                .all()
            )
            
            # Reverse to get chronological order
            messages.reverse()
            
            # Filter by token count if needed
            if max_tokens > 0:
                total_tokens = 0
                filtered_messages = []
                
                for message in reversed(messages):  # Start from most recent
                    message_tokens = len(message.content) // 4  # Rough estimate
                    if total_tokens + message_tokens <= max_tokens:
                        filtered_messages.insert(0, message)  # Insert at beginning
                        total_tokens += message_tokens
                    else:
                        break
                
                messages = filtered_messages
            
            self.logger.debug(
                "Retrieved conversation context",
                session_id=str(session_id),
                message_count=len(messages),
                estimated_tokens=sum(len(m.content) // 4 for m in messages)
            )
            
            return messages
        except Exception as e:
            self.logger.error(
                "Failed to get conversation context",
                session_id=str(session_id),
                error=str(e)
            )
            raise
    
    def update_processing_status(
        self,
        message_id: uuid.UUID,
        status: str,
        error_message: Optional[str] = None,
        error_details: Optional[Dict] = None
    ) -> Optional[Message]:
        """
        Update message processing status.
        
        Args:
            message_id: Message ID
            status: New status
            error_message: Error message if failed
            error_details: Detailed error information
            
        Returns:
            Optional[Message]: Updated message or None if not found
        """
        try:
            message = self.get_by_id(message_id)
            if not message:
                return None
            
            message.status = status
            if error_message:
                message.error_message = error_message
            if error_details:
                message.error_details = error_details
            
            self.db_session.flush()
            
            self.logger.info(
                "Updated message status",
                message_id=str(message_id),
                status=status,
                has_error=bool(error_message)
            )
            
            return message
        except Exception as e:
            self.logger.error(
                "Failed to update message status",
                message_id=str(message_id),
                error=str(e)
            )
            raise
    
    def update_token_usage(
        self,
        message_id: uuid.UUID,
        prompt_tokens: int,
        completion_tokens: int,
        total_tokens: int,
        cost_usd: float,
        processing_time_ms: Optional[int] = None
    ) -> Optional[Message]:
        """
        Update message token usage and cost information.
        
        Args:
            message_id: Message ID
            prompt_tokens: Tokens used in prompt
            completion_tokens: Tokens used in completion
            total_tokens: Total tokens used
            cost_usd: Cost in USD
            processing_time_ms: Processing time in milliseconds
            
        Returns:
            Optional[Message]: Updated message or None if not found
        """
        try:
            message = self.get_by_id(message_id)
            if not message:
                return None
            
            message.prompt_tokens = prompt_tokens
            message.completion_tokens = completion_tokens
            message.total_tokens = total_tokens
            message.cost_usd = cost_usd
            
            if processing_time_ms is not None:
                message.processing_time_ms = processing_time_ms
            
            self.db_session.flush()
            
            self.logger.info(
                "Updated message token usage",
                message_id=str(message_id),
                total_tokens=total_tokens,
                cost_usd=cost_usd
            )
            
            return message
        except Exception as e:
            self.logger.error(
                "Failed to update message token usage",
                message_id=str(message_id),
                error=str(e)
            )
            raise
    
    def get_failed_messages(
        self,
        session_id: Optional[uuid.UUID] = None,
        hours: int = 24,
        limit: int = 100
    ) -> List[Message]:
        """
        Get messages that failed processing.
        
        Args:
            session_id: Optional session filter
            hours: Hours to look back
            limit: Maximum number of messages
            
        Returns:
            List[Message]: Failed messages
        """
        try:
            start_time = datetime.utcnow() - timedelta(hours=hours)
            
            query = (
                self.db_session.query(Message)
                .filter(
                    and_(
                        Message.status == 'failed',
                        Message.created_at >= start_time
                    )
                )
                .order_by(desc(Message.created_at))
            )
            
            if session_id:
                query = query.filter(Message.session_id == session_id)
            
            failed_messages = query.limit(limit).all()
            
            self.logger.debug(
                "Retrieved failed messages",
                session_id=str(session_id) if session_id else None,
                count=len(failed_messages),
                hours=hours
            )
            
            return failed_messages
        except Exception as e:
            self.logger.error("Failed to get failed messages", error=str(e))
            raise
    
    def get_processing_stats(
        self,
        session_id: Optional[uuid.UUID] = None,
        days: int = 7
    ) -> Dict[str, Any]:
        """
        Get processing statistics for messages.
        
        Args:
            session_id: Optional session filter
            days: Number of days to analyze
            
        Returns:
            Dict[str, Any]: Processing statistics
        """
        try:
            start_date = datetime.utcnow() - timedelta(days=days)
            
            query = (
                self.db_session.query(Message)
                .filter(Message.created_at >= start_date)
            )
            
            if session_id:
                query = query.filter(Message.session_id == session_id)
            
            messages = query.all()
            
            # Calculate statistics
            total_messages = len(messages)
            completed_messages = [m for m in messages if m.is_completed()]
            failed_messages = [m for m in messages if m.is_failed()]
            
            total_tokens = sum(m.total_tokens for m in completed_messages)
            total_cost = sum(m.cost_usd for m in completed_messages)
            
            processing_times = [
                m.processing_time_ms for m in completed_messages 
                if m.processing_time_ms is not None
            ]
            
            stats = {
                "total_messages": total_messages,
                "completed_messages": len(completed_messages),
                "failed_messages": len(failed_messages),
                "success_rate": (
                    len(completed_messages) / total_messages * 100 
                    if total_messages > 0 else 0
                ),
                "total_tokens": total_tokens,
                "total_cost_usd": total_cost,
                "average_tokens_per_message": (
                    total_tokens / len(completed_messages)
                    if completed_messages else 0
                ),
                "average_cost_per_message": (
                    total_cost / len(completed_messages)
                    if completed_messages else 0
                ),
                "processing_time_stats": {
                    "count": len(processing_times),
                    "average_ms": (
                        sum(processing_times) / len(processing_times)
                        if processing_times else 0
                    ),
                    "min_ms": min(processing_times) if processing_times else 0,
                    "max_ms": max(processing_times) if processing_times else 0
                },
                "date_range": {
                    "start_date": start_date.isoformat(),
                    "end_date": datetime.utcnow().isoformat(),
                    "days": days
                }
            }
            
            self.logger.debug(
                "Generated processing stats",
                session_id=str(session_id) if session_id else None,
                total_messages=total_messages,
                days=days
            )
            
            return stats
        except Exception as e:
            self.logger.error("Failed to get processing stats", error=str(e))
            raise
    
    def search_messages(
        self,
        query: str,
        session_id: Optional[uuid.UUID] = None,
        role: Optional[str] = None,
        limit: int = 50
    ) -> List[Message]:
        """
        Search messages by content.
        
        Args:
            query: Search query
            session_id: Optional session filter
            role: Optional role filter
            limit: Maximum number of results
            
        Returns:
            List[Message]: Matching messages
        """
        try:
            db_query = (
                self.db_session.query(Message)
                .filter(Message.content.ilike(f"%{query}%"))
                .order_by(desc(Message.created_at))
            )
            
            if session_id:
                db_query = db_query.filter(Message.session_id == session_id)
            
            if role:
                db_query = db_query.filter(Message.role == role)
            
            messages = db_query.limit(limit).all()
            
            self.logger.debug(
                "Searched messages",
                query=query,
                session_id=str(session_id) if session_id else None,
                role=role,
                results=len(messages)
            )
            
            return messages
        except Exception as e:
            self.logger.error("Failed to search messages", query=query, error=str(e))
            raise
    
    def get_user_messages(
        self,
        session_id: uuid.UUID,
        limit: int = 100
    ) -> List[Message]:
        """
        Get only user messages from a session.
        
        Args:
            session_id: Session ID
            limit: Maximum number of messages
            
        Returns:
            List[Message]: User messages
        """
        try:
            messages = (
                self.db_session.query(Message)
                .filter(
                    and_(
                        Message.session_id == session_id,
                        Message.role == 'user'
                    )
                )
                .order_by(Message.sequence_number)
                .limit(limit)
                .all()
            )
            
            self.logger.debug(
                "Retrieved user messages",
                session_id=str(session_id),
                count=len(messages)
            )
            
            return messages
        except Exception as e:
            self.logger.error(
                "Failed to get user messages",
                session_id=str(session_id),
                error=str(e)
            )
            raise
    
    def get_assistant_messages(
        self,
        session_id: uuid.UUID,
        limit: int = 100,
        include_citations: bool = True
    ) -> List[Message]:
        """
        Get only assistant messages from a session.
        
        Args:
            session_id: Session ID
            limit: Maximum number of messages
            include_citations: Whether to include citations
            
        Returns:
            List[Message]: Assistant messages
        """
        try:
            query = (
                self.db_session.query(Message)
                .filter(
                    and_(
                        Message.session_id == session_id,
                        Message.role == 'assistant'
                    )
                )
                .order_by(Message.sequence_number)
            )
            
            if include_citations:
                query = query.options(joinedload(Message.citations))
            
            messages = query.limit(limit).all()
            
            self.logger.debug(
                "Retrieved assistant messages",
                session_id=str(session_id),
                count=len(messages),
                include_citations=include_citations
            )
            
            return messages
        except Exception as e:
            self.logger.error(
                "Failed to get assistant messages",
                session_id=str(session_id),
                error=str(e)
            )
            raise
