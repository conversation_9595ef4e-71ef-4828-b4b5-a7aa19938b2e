"""
M10 AI Orchestrator - Citation Repository

Repository class for Citation model providing specialized database operations
for citation management, source tracking, and quality analysis.
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import func, desc, and_, or_
from datetime import datetime, timedelta
import uuid

from .base_repository import BaseRepository
from ..models.citation import Citation, SourceType


class CitationRepository(BaseRepository[Citation]):
    """
    Repository for Citation model with specialized methods for
    citation management, source analysis, and quality tracking.
    """
    
    def __init__(self, db_session: Session):
        """Initialize the Citation repository."""
        super().__init__(Citation, db_session)
    
    def create_citation(
        self,
        message_id: uuid.UUID,
        source_id: str,
        source_type: str,
        cited_content: str,
        source_title: Optional[str] = None,
        source_url: Optional[str] = None,
        context_before: Optional[str] = None,
        context_after: Optional[str] = None,
        page_number: Optional[int] = None,
        chunk_index: Optional[int] = None,
        relevance_score: Optional[float] = None,
        confidence_score: Optional[float] = None,
        similarity_score: Optional[float] = None,
        sequence_order: int = 1,
        retrieval_method: Optional[str] = None,
        retrieval_query: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Citation:
        """
        Create a new citation for a message.
        
        Args:
            message_id: ID of the message this citation belongs to
            source_id: Identifier of the source
            source_type: Type of source being cited
            cited_content: The content that was cited
            source_title: Title of the source document
            source_url: URL or path to the source
            context_before: Content before the cited text
            context_after: Content after the cited text
            page_number: Page number in source document
            chunk_index: Index of the chunk
            relevance_score: Relevance score from retrieval
            confidence_score: Confidence score for citation
            similarity_score: Semantic similarity score
            sequence_order: Order for display
            retrieval_method: Method used to retrieve source
            retrieval_query: Query used for retrieval
            metadata: Additional metadata
            
        Returns:
            Citation: The created citation
        """
        citation_data = {
            "message_id": message_id,
            "source_id": source_id,
            "source_type": source_type,
            "cited_content": cited_content,
            "source_title": source_title,
            "source_url": source_url,
            "context_before": context_before,
            "context_after": context_after,
            "page_number": page_number,
            "chunk_index": chunk_index,
            "relevance_score": relevance_score,
            "confidence_score": confidence_score,
            "similarity_score": similarity_score,
            "sequence_order": sequence_order,
            "retrieval_method": retrieval_method,
            "retrieval_query": retrieval_query,
            "retrieval_timestamp": int(datetime.utcnow().timestamp()),
            "metadata": metadata or {}
        }
        
        return self.create(**citation_data)
    
    def get_by_message(
        self,
        message_id: uuid.UUID,
        order_by_sequence: bool = True
    ) -> List[Citation]:
        """
        Get all citations for a specific message.
        
        Args:
            message_id: Message ID
            order_by_sequence: Whether to order by sequence_order
            
        Returns:
            List[Citation]: Citations for the message
        """
        try:
            query = (
                self.db_session.query(Citation)
                .filter(Citation.message_id == message_id)
            )
            
            if order_by_sequence:
                query = query.order_by(Citation.sequence_order)
            else:
                query = query.order_by(Citation.created_at)
            
            citations = query.all()
            
            self.logger.debug(
                "Retrieved message citations",
                message_id=str(message_id),
                count=len(citations)
            )
            
            return citations
        except Exception as e:
            self.logger.error(
                "Failed to get message citations",
                message_id=str(message_id),
                error=str(e)
            )
            raise
    
    def get_by_source(
        self,
        source_id: str,
        source_type: Optional[str] = None,
        limit: int = 100
    ) -> List[Citation]:
        """
        Get citations for a specific source.
        
        Args:
            source_id: Source identifier
            source_type: Optional source type filter
            limit: Maximum number of citations
            
        Returns:
            List[Citation]: Citations from the source
        """
        try:
            query = (
                self.db_session.query(Citation)
                .filter(Citation.source_id == source_id)
                .order_by(desc(Citation.created_at))
            )
            
            if source_type:
                query = query.filter(Citation.source_type == source_type)
            
            citations = query.limit(limit).all()
            
            self.logger.debug(
                "Retrieved source citations",
                source_id=source_id,
                source_type=source_type,
                count=len(citations)
            )
            
            return citations
        except Exception as e:
            self.logger.error(
                "Failed to get source citations",
                source_id=source_id,
                error=str(e)
            )
            raise
    
    def get_high_quality_citations(
        self,
        message_id: Optional[uuid.UUID] = None,
        threshold: float = 0.7,
        limit: int = 50
    ) -> List[Citation]:
        """
        Get high-quality citations based on score thresholds.
        
        Args:
            message_id: Optional message filter
            threshold: Minimum quality threshold
            limit: Maximum number of citations
            
        Returns:
            List[Citation]: High-quality citations
        """
        try:
            query = (
                self.db_session.query(Citation)
                .filter(
                    or_(
                        Citation.relevance_score >= threshold,
                        Citation.confidence_score >= threshold,
                        Citation.similarity_score >= threshold
                    )
                )
                .order_by(desc(Citation.relevance_score))
            )
            
            if message_id:
                query = query.filter(Citation.message_id == message_id)
            
            citations = query.limit(limit).all()
            
            self.logger.debug(
                "Retrieved high-quality citations",
                message_id=str(message_id) if message_id else None,
                threshold=threshold,
                count=len(citations)
            )
            
            return citations
        except Exception as e:
            self.logger.error("Failed to get high-quality citations", error=str(e))
            raise
    
    def update_scores(
        self,
        citation_id: uuid.UUID,
        relevance_score: Optional[float] = None,
        confidence_score: Optional[float] = None,
        similarity_score: Optional[float] = None
    ) -> Optional[Citation]:
        """
        Update quality scores for a citation.
        
        Args:
            citation_id: Citation ID
            relevance_score: New relevance score
            confidence_score: New confidence score
            similarity_score: New similarity score
            
        Returns:
            Optional[Citation]: Updated citation or None if not found
        """
        try:
            citation = self.get_by_id(citation_id)
            if not citation:
                return None
            
            if relevance_score is not None:
                citation.relevance_score = relevance_score
            if confidence_score is not None:
                citation.confidence_score = confidence_score
            if similarity_score is not None:
                citation.similarity_score = similarity_score
            
            self.db_session.flush()
            
            self.logger.info(
                "Updated citation scores",
                citation_id=str(citation_id),
                relevance_score=relevance_score,
                confidence_score=confidence_score,
                similarity_score=similarity_score
            )
            
            return citation
        except Exception as e:
            self.logger.error(
                "Failed to update citation scores",
                citation_id=str(citation_id),
                error=str(e)
            )
            raise
    
    def mark_as_verified(self, citation_id: uuid.UUID) -> bool:
        """
        Mark a citation as verified.
        
        Args:
            citation_id: Citation ID
            
        Returns:
            bool: True if citation was marked as verified, False if not found
        """
        try:
            citation = self.get_by_id(citation_id)
            if not citation:
                return False
            
            citation.mark_as_verified()
            self.db_session.flush()
            
            self.logger.info("Citation marked as verified", citation_id=str(citation_id))
            return True
        except Exception as e:
            self.logger.error(
                "Failed to mark citation as verified",
                citation_id=str(citation_id),
                error=str(e)
            )
            raise
    
    def mark_as_primary(self, citation_id: uuid.UUID) -> bool:
        """
        Mark a citation as a primary source.
        
        Args:
            citation_id: Citation ID
            
        Returns:
            bool: True if citation was marked as primary, False if not found
        """
        try:
            citation = self.get_by_id(citation_id)
            if not citation:
                return False
            
            citation.mark_as_primary()
            self.db_session.flush()
            
            self.logger.info("Citation marked as primary", citation_id=str(citation_id))
            return True
        except Exception as e:
            self.logger.error(
                "Failed to mark citation as primary",
                citation_id=str(citation_id),
                error=str(e)
            )
            raise
    
    def get_citation_stats(
        self,
        days: int = 30,
        source_type: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get citation statistics and analytics.
        
        Args:
            days: Number of days to analyze
            source_type: Optional source type filter
            
        Returns:
            Dict[str, Any]: Citation statistics
        """
        try:
            start_date = datetime.utcnow() - timedelta(days=days)
            
            query = (
                self.db_session.query(Citation)
                .filter(Citation.created_at >= start_date)
            )
            
            if source_type:
                query = query.filter(Citation.source_type == source_type)
            
            citations = query.all()
            
            # Calculate statistics
            total_citations = len(citations)
            verified_citations = len([c for c in citations if c.is_verified])
            primary_citations = len([c for c in citations if c.is_primary])
            
            # Score statistics
            relevance_scores = [c.relevance_score for c in citations if c.relevance_score is not None]
            confidence_scores = [c.confidence_score for c in citations if c.confidence_score is not None]
            similarity_scores = [c.similarity_score for c in citations if c.similarity_score is not None]
            
            # Source type distribution
            source_type_counts = {}
            for citation in citations:
                source_type_counts[citation.source_type] = source_type_counts.get(citation.source_type, 0) + 1
            
            # High quality citations (threshold of 0.7)
            high_quality_citations = len([c for c in citations if c.is_high_quality(0.7)])
            
            stats = {
                "total_citations": total_citations,
                "verified_citations": verified_citations,
                "primary_citations": primary_citations,
                "high_quality_citations": high_quality_citations,
                "verification_rate": (
                    verified_citations / total_citations * 100
                    if total_citations > 0 else 0
                ),
                "primary_rate": (
                    primary_citations / total_citations * 100
                    if total_citations > 0 else 0
                ),
                "quality_rate": (
                    high_quality_citations / total_citations * 100
                    if total_citations > 0 else 0
                ),
                "score_statistics": {
                    "relevance": {
                        "count": len(relevance_scores),
                        "average": sum(relevance_scores) / len(relevance_scores) if relevance_scores else 0,
                        "min": min(relevance_scores) if relevance_scores else 0,
                        "max": max(relevance_scores) if relevance_scores else 0
                    },
                    "confidence": {
                        "count": len(confidence_scores),
                        "average": sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0,
                        "min": min(confidence_scores) if confidence_scores else 0,
                        "max": max(confidence_scores) if confidence_scores else 0
                    },
                    "similarity": {
                        "count": len(similarity_scores),
                        "average": sum(similarity_scores) / len(similarity_scores) if similarity_scores else 0,
                        "min": min(similarity_scores) if similarity_scores else 0,
                        "max": max(similarity_scores) if similarity_scores else 0
                    }
                },
                "source_type_distribution": source_type_counts,
                "date_range": {
                    "start_date": start_date.isoformat(),
                    "end_date": datetime.utcnow().isoformat(),
                    "days": days
                }
            }
            
            self.logger.debug(
                "Generated citation stats",
                total_citations=total_citations,
                days=days,
                source_type=source_type
            )
            
            return stats
        except Exception as e:
            self.logger.error("Failed to get citation stats", error=str(e))
            raise
    
    def search_citations(
        self,
        query: str,
        source_type: Optional[str] = None,
        limit: int = 50
    ) -> List[Citation]:
        """
        Search citations by content or source information.
        
        Args:
            query: Search query
            source_type: Optional source type filter
            limit: Maximum number of results
            
        Returns:
            List[Citation]: Matching citations
        """
        try:
            db_query = (
                self.db_session.query(Citation)
                .filter(
                    or_(
                        Citation.cited_content.ilike(f"%{query}%"),
                        Citation.source_title.ilike(f"%{query}%"),
                        Citation.source_id.ilike(f"%{query}%")
                    )
                )
                .order_by(desc(Citation.created_at))
            )
            
            if source_type:
                db_query = db_query.filter(Citation.source_type == source_type)
            
            citations = db_query.limit(limit).all()
            
            self.logger.debug(
                "Searched citations",
                query=query,
                source_type=source_type,
                results=len(citations)
            )
            
            return citations
        except Exception as e:
            self.logger.error("Failed to search citations", query=query, error=str(e))
            raise
    
    def get_popular_sources(
        self,
        days: int = 30,
        limit: int = 20
    ) -> List[Dict[str, Any]]:
        """
        Get most frequently cited sources.
        
        Args:
            days: Number of days to analyze
            limit: Maximum number of sources to return
            
        Returns:
            List[Dict[str, Any]]: Popular sources with citation counts
        """
        try:
            start_date = datetime.utcnow() - timedelta(days=days)
            
            # Group by source_id and count citations
            result = (
                self.db_session.query(
                    Citation.source_id,
                    Citation.source_type,
                    Citation.source_title,
                    func.count(Citation.id).label('citation_count'),
                    func.avg(Citation.relevance_score).label('avg_relevance'),
                    func.max(Citation.created_at).label('last_cited')
                )
                .filter(Citation.created_at >= start_date)
                .group_by(Citation.source_id, Citation.source_type, Citation.source_title)
                .order_by(desc(func.count(Citation.id)))
                .limit(limit)
                .all()
            )
            
            popular_sources = []
            for row in result:
                popular_sources.append({
                    "source_id": row.source_id,
                    "source_type": row.source_type,
                    "source_title": row.source_title,
                    "citation_count": row.citation_count,
                    "average_relevance": float(row.avg_relevance) if row.avg_relevance else None,
                    "last_cited": row.last_cited.isoformat() if row.last_cited else None
                })
            
            self.logger.debug(
                "Retrieved popular sources",
                count=len(popular_sources),
                days=days
            )
            
            return popular_sources
        except Exception as e:
            self.logger.error("Failed to get popular sources", error=str(e))
            raise
    
    def cleanup_old_citations(
        self,
        days: int = 365,
        keep_verified: bool = True
    ) -> int:
        """
        Clean up old citations to manage database size.
        
        Args:
            days: Age threshold in days
            keep_verified: Whether to keep verified citations
            
        Returns:
            int: Number of citations deleted
        """
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days)
            
            query = (
                self.db_session.query(Citation)
                .filter(Citation.created_at < cutoff_date)
            )
            
            if keep_verified:
                query = query.filter(Citation.is_verified == False)
            
            old_citations = query.all()
            count = len(old_citations)
            
            for citation in old_citations:
                self.db_session.delete(citation)
            
            if count > 0:
                self.db_session.flush()
            
            self.logger.info(
                "Cleaned up old citations",
                count=count,
                days=days,
                keep_verified=keep_verified
            )
            
            return count
        except Exception as e:
            self.logger.error("Failed to cleanup old citations", error=str(e))
            raise
