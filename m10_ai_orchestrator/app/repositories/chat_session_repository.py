"""
M10 AI Orchestrator - Chat Session Repository

Repository class for ChatSession model providing specialized database operations
for chat session management and retrieval.
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import func, desc, and_
from datetime import datetime, timedelta
import uuid

from .base_repository import BaseRepository
from ..models.chat_session import ChatSession, SessionStatus


class ChatSessionRepository(BaseRepository[ChatSession]):
    """
    Repository for ChatSession model with specialized methods for
    chat session management, user sessions, and usage analytics.
    """
    
    def __init__(self, db_session: Session):
        """Initialize the ChatSession repository."""
        super().__init__(ChatSession, db_session)
    
    def create_session(
        self, 
        title: str = "New Chat",
        user_id: Optional[str] = None,
        system_prompt: Optional[str] = None,
        model_name: str = "gpt-4o",
        temperature: float = 0.3,
        max_tokens: int = 1000,
        context_window_size: int = 8000,
        enable_citations: bool = True,
        enable_context_retrieval: bool = True,
        metadata: Optional[Dict[str, Any]] = None
    ) -> ChatSession:
        """
        Create a new chat session with specified configuration.
        
        Args:
            title: Session title
            user_id: User identifier
            system_prompt: System prompt for the session
            model_name: LLM model to use
            temperature: Temperature setting
            max_tokens: Maximum tokens per response
            context_window_size: Context window size in tokens
            enable_citations: Whether to include citations
            enable_context_retrieval: Whether to use RAG
            metadata: Additional metadata
            
        Returns:
            ChatSession: The created session
        """
        session_data = {
            "name": title,  # O modelo usa 'name' em vez de 'title'
            "status": SessionStatus.ACTIVE
        }
        
        return self.create(**session_data)
    
    def get_by_user(
        self, 
        user_id: str, 
        status: Optional[str] = None,
        limit: int = 50,
        offset: int = 0
    ) -> List[ChatSession]:
        """
        Get chat sessions for a specific user.
        
        Args:
            user_id: User identifier
            status: Optional status filter
            limit: Maximum number of sessions to return
            offset: Number of sessions to skip
            
        Returns:
            List[ChatSession]: User's chat sessions
        """
        try:
            query = (
                self.db_session.query(ChatSession)
                .filter(ChatSession.user_id == user_id)
                .order_by(desc(ChatSession.updated_at))
            )
            
            if status:
                query = query.filter(ChatSession.status == status)
            
            sessions = query.offset(offset).limit(limit).all()
            
            self.logger.info(
                "Retrieved user sessions", 
                user_id=user_id, 
                count=len(sessions),
                status=status
            )
            
            return sessions
        except Exception as e:
            self.logger.error(
                "Failed to get user sessions", 
                user_id=user_id, 
                error=str(e)
            )
            raise
    
    def get_active_sessions(
        self, 
        user_id: Optional[str] = None,
        limit: int = 100
    ) -> List[ChatSession]:
        """
        Get active chat sessions, optionally filtered by user.
        
        Args:
            user_id: Optional user filter
            limit: Maximum number of sessions to return
            
        Returns:
            List[ChatSession]: Active chat sessions
        """
        try:
            query = (
                self.db_session.query(ChatSession)
                .filter(ChatSession.status == "active")
                .order_by(desc(ChatSession.updated_at))
            )
            
            if user_id:
                query = query.filter(ChatSession.user_id == user_id)
            
            sessions = query.limit(limit).all()
            
            self.logger.debug(
                "Retrieved active sessions", 
                count=len(sessions),
                user_id=user_id
            )
            
            return sessions
        except Exception as e:
            self.logger.error("Failed to get active sessions", error=str(e))
            raise
    
    def get_with_messages(
        self, 
        session_id: uuid.UUID,
        message_limit: int = 50
    ) -> Optional[ChatSession]:
        """
        Get a chat session with its messages preloaded.
        
        Args:
            session_id: Session ID
            message_limit: Maximum number of messages to load
            
        Returns:
            Optional[ChatSession]: Session with messages, or None if not found
        """
        try:
            session = (
                self.db_session.query(ChatSession)
                .options(
                    joinedload(ChatSession.messages)
                    .joinedload("citations")
                )
                .filter(ChatSession.id == session_id)
                .first()
            )
            
            if session and session.messages:
                # Limit messages if needed
                session.messages = session.messages[-message_limit:]
            
            if session:
                self.logger.debug(
                    "Retrieved session with messages", 
                    session_id=str(session_id),
                    message_count=len(session.messages) if session.messages else 0
                )
            
            return session
        except Exception as e:
            self.logger.error(
                "Failed to get session with messages", 
                session_id=str(session_id), 
                error=str(e)
            )
            raise
    
    def update_usage(
        self, 
        session_id: uuid.UUID,
        tokens_used: int,
        cost_usd: float,
        message_count: int = 1
    ) -> Optional[ChatSession]:
        """
        Update session usage statistics.
        
        Args:
            session_id: Session ID
            tokens_used: Number of tokens used
            cost_usd: Cost in USD
            message_count: Number of messages to add
            
        Returns:
            Optional[ChatSession]: Updated session, or None if not found
        """
        try:
            session = self.get_by_id(session_id)
            if not session:
                return None
            
            session.total_tokens_used += tokens_used
            session.total_cost_usd += cost_usd
            session.total_messages += message_count
            
            self.db_session.flush()
            
            self.logger.info(
                "Updated session usage", 
                session_id=str(session_id),
                tokens_used=tokens_used,
                cost_usd=cost_usd,
                message_count=message_count
            )
            
            return session
        except Exception as e:
            self.logger.error(
                "Failed to update session usage", 
                session_id=str(session_id), 
                error=str(e)
            )
            raise
    
    def archive_session(self, session_id: uuid.UUID) -> bool:
        """
        Archive a chat session.
        
        Args:
            session_id: Session ID
            
        Returns:
            bool: True if session was archived, False if not found
        """
        try:
            session = self.get_by_id(session_id)
            if not session:
                return False
            
            session.archive()
            self.db_session.flush()
            
            self.logger.info("Session archived", session_id=str(session_id))
            return True
        except Exception as e:
            self.logger.error(
                "Failed to archive session", 
                session_id=str(session_id), 
                error=str(e)
            )
            raise
    
    def complete_session(self, session_id: uuid.UUID) -> bool:
        """
        Mark a chat session as completed.
        
        Args:
            session_id: Session ID
            
        Returns:
            bool: True if session was completed, False if not found
        """
        try:
            session = self.get_by_id(session_id)
            if not session:
                return False
            
            session.complete()
            self.db_session.flush()
            
            self.logger.info("Session completed", session_id=str(session_id))
            return True
        except Exception as e:
            self.logger.error(
                "Failed to complete session", 
                session_id=str(session_id), 
                error=str(e)
            )
            raise
    
    def get_usage_stats(
        self, 
        user_id: Optional[str] = None,
        days: int = 30
    ) -> Dict[str, Any]:
        """
        Get usage statistics for sessions.
        
        Args:
            user_id: Optional user filter
            days: Number of days to look back
            
        Returns:
            Dict[str, Any]: Usage statistics
        """
        try:
            start_date = datetime.utcnow() - timedelta(days=days)
            
            query = (
                self.db_session.query(ChatSession)
                .filter(ChatSession.created_at >= start_date)
            )
            
            if user_id:
                query = query.filter(ChatSession.user_id == user_id)
            
            sessions = query.all()
            
            stats = {
                "total_sessions": len(sessions),
                "total_messages": sum(s.total_messages for s in sessions),
                "total_tokens": sum(s.total_tokens_used for s in sessions),
                "total_cost_usd": sum(s.total_cost_usd for s in sessions),
                "active_sessions": len([s for s in sessions if s.is_active()]),
                "average_messages_per_session": 0,
                "average_cost_per_session": 0,
                "date_range": {
                    "start_date": start_date.isoformat(),
                    "end_date": datetime.utcnow().isoformat(),
                    "days": days
                }
            }
            
            if stats["total_sessions"] > 0:
                stats["average_messages_per_session"] = round(
                    stats["total_messages"] / stats["total_sessions"], 2
                )
                stats["average_cost_per_session"] = round(
                    stats["total_cost_usd"] / stats["total_sessions"], 4
                )
            
            self.logger.debug(
                "Generated usage stats", 
                user_id=user_id, 
                days=days,
                total_sessions=stats["total_sessions"]
            )
            
            return stats
        except Exception as e:
            self.logger.error("Failed to get usage stats", error=str(e))
            raise
    
    def cleanup_old_sessions(
        self, 
        days: int = 90,
        status: str = "archived"
    ) -> int:
        """
        Clean up old sessions by moving them to specified status.
        
        Args:
            days: Age threshold in days
            status: Status to set for old sessions
            
        Returns:
            int: Number of sessions cleaned up
        """
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days)
            
            old_sessions = (
                self.db_session.query(ChatSession)
                .filter(
                    and_(
                        ChatSession.updated_at < cutoff_date,
                        ChatSession.status == "completed"
                    )
                )
                .all()
            )
            
            count = 0
            for session in old_sessions:
                session.status = status
                count += 1
            
            if count > 0:
                self.db_session.flush()
            
            self.logger.info(
                "Cleaned up old sessions", 
                count=count, 
                days=days, 
                new_status=status
            )
            
            return count
        except Exception as e:
            self.logger.error("Failed to cleanup old sessions", error=str(e))
            raise
    
    def search_sessions(
        self,
        query: str,
        user_id: Optional[str] = None,
        limit: int = 20
    ) -> List[ChatSession]:
        """
        Search sessions by title or metadata content.
        
        Args:
            query: Search query
            user_id: Optional user filter
            limit: Maximum number of results
            
        Returns:
            List[ChatSession]: Matching sessions
        """
        try:
            db_query = (
                self.db_session.query(ChatSession)
                .filter(
                    ChatSession.title.ilike(f"%{query}%")
                )
                .order_by(desc(ChatSession.updated_at))
            )
            
            if user_id:
                db_query = db_query.filter(ChatSession.user_id == user_id)
            
            sessions = db_query.limit(limit).all()
            
            self.logger.debug(
                "Searched sessions", 
                query=query, 
                user_id=user_id,
                results=len(sessions)
            )
            
            return sessions
        except Exception as e:
            self.logger.error("Failed to search sessions", query=query, error=str(e))
            raise
