"""
M10 AI Orchestrator - Unique ID Generator

Service for generating guaranteed unique UUIDs across the application,
with collision detection and resolution strategies.
"""

import uuid
import time
import threading
from typing import Set, Optional
from datetime import datetime, timedelta
import logging
from collections import deque

logger = logging.getLogger(__name__)


class UniqueIDGenerator:
    """
    Service for generating guaranteed unique UUIDs with collision detection.
    
    Features:
    - Thread-safe UUID generation
    - In-memory collision detection
    - Configurable cache size and TTL
    - Performance monitoring
    - Collision resolution strategies
    """
    
    def __init__(
        self, 
        cache_size: int = 10000,
        cache_ttl_minutes: int = 60,
        max_retries: int = 10
    ):
        """
        Initialize the UniqueIDGenerator.
        
        Args:
            cache_size: Maximum number of UUIDs to keep in cache
            cache_ttl_minutes: Time to live for cached UUIDs in minutes
            max_retries: Maximum retries for collision resolution
        """
        self.cache_size = cache_size
        self.cache_ttl = timedelta(minutes=cache_ttl_minutes)
        self.max_retries = max_retries
        
        # Thread-safe cache for recently generated UUIDs
        self._lock = threading.RLock()
        self._uuid_cache: deque = deque(maxlen=cache_size)
        self._uuid_timestamps: dict = {}
        
        # Performance metrics
        self._generation_count = 0
        self._collision_count = 0
        self._retry_count = 0
        
        logger.info(
            f"UniqueIDGenerator initialized with cache_size={cache_size}, "
            f"cache_ttl={cache_ttl_minutes}min, max_retries={max_retries}"
        )
    
    def generate_uuid(self, namespace: Optional[str] = None) -> uuid.UUID:
        """
        Generate a guaranteed unique UUID.
        
        Args:
            namespace: Optional namespace for UUID generation
            
        Returns:
            uuid.UUID: A guaranteed unique UUID
            
        Raises:
            RuntimeError: If unable to generate unique UUID after max_retries
        """
        with self._lock:
            return self._generate_uuid_with_collision_detection(namespace)
    
    def generate_uuid_string(self, namespace: Optional[str] = None) -> str:
        """
        Generate a guaranteed unique UUID as string.
        
        Args:
            namespace: Optional namespace for UUID generation
            
        Returns:
            str: A guaranteed unique UUID string
        """
        return str(self.generate_uuid(namespace))
    
    def _generate_uuid_with_collision_detection(
        self, 
        namespace: Optional[str] = None
    ) -> uuid.UUID:
        """
        Internal method for UUID generation with collision detection.
        
        Args:
            namespace: Optional namespace for UUID generation
            
        Returns:
            uuid.UUID: A guaranteed unique UUID
            
        Raises:
            RuntimeError: If unable to generate unique UUID after max_retries
        """
        retries = 0
        
        while retries <= self.max_retries:
            # Generate UUID based on namespace or use uuid4
            if namespace:
                # Use namespace-based UUID generation (uuid5)
                namespace_uuid = uuid.uuid5(uuid.NAMESPACE_DNS, namespace)
                # Combine with timestamp for uniqueness
                timestamp_str = str(time.time_ns())
                new_uuid = uuid.uuid5(namespace_uuid, timestamp_str)
            else:
                # Use random UUID generation (uuid4)
                new_uuid = uuid.uuid4()
            
            # Check for collision in cache
            if not self._is_uuid_in_cache(new_uuid):
                # No collision, add to cache and return
                self._add_uuid_to_cache(new_uuid)
                self._generation_count += 1
                
                if retries > 0:
                    self._retry_count += retries
                    logger.debug(f"UUID generated after {retries} retries: {new_uuid}")
                
                return new_uuid
            
            # Collision detected
            self._collision_count += 1
            retries += 1
            
            logger.warning(
                f"UUID collision detected (attempt {retries}/{self.max_retries}): {new_uuid}"
            )
            
            # Add small delay to reduce collision probability
            time.sleep(0.001 * retries)
        
        # Max retries exceeded
        error_msg = (
            f"Failed to generate unique UUID after {self.max_retries} retries. "
            f"Consider increasing cache size or TTL."
        )
        logger.error(error_msg)
        raise RuntimeError(error_msg)
    
    def _is_uuid_in_cache(self, target_uuid: uuid.UUID) -> bool:
        """
        Check if UUID exists in cache.
        
        Args:
            target_uuid: UUID to check
            
        Returns:
            bool: True if UUID exists in cache
        """
        # Clean expired UUIDs first
        self._clean_expired_uuids()
        
        # Check if UUID exists in cache
        return target_uuid in self._uuid_cache
    
    def _add_uuid_to_cache(self, new_uuid: uuid.UUID) -> None:
        """
        Add UUID to cache with timestamp.
        
        Args:
            new_uuid: UUID to add to cache
        """
        current_time = datetime.utcnow()
        
        # Add to cache (deque automatically handles max size)
        self._uuid_cache.append(new_uuid)
        self._uuid_timestamps[new_uuid] = current_time
        
        # Clean up timestamps dict if it gets too large
        if len(self._uuid_timestamps) > self.cache_size * 1.2:
            self._clean_expired_uuids()
    
    def _clean_expired_uuids(self) -> None:
        """Clean expired UUIDs from cache."""
        current_time = datetime.utcnow()
        expired_uuids = []
        
        # Find expired UUIDs
        for cached_uuid, timestamp in self._uuid_timestamps.items():
            if current_time - timestamp > self.cache_ttl:
                expired_uuids.append(cached_uuid)
        
        # Remove expired UUIDs
        for expired_uuid in expired_uuids:
            if expired_uuid in self._uuid_timestamps:
                del self._uuid_timestamps[expired_uuid]
            
            # Remove from deque (this is O(n) but happens infrequently)
            try:
                self._uuid_cache.remove(expired_uuid)
            except ValueError:
                pass  # UUID might have been rotated out of deque already
        
        if expired_uuids:
            logger.debug(f"Cleaned {len(expired_uuids)} expired UUIDs from cache")
    
    def get_statistics(self) -> dict:
        """
        Get generation statistics.
        
        Returns:
            dict: Statistics about UUID generation
        """
        with self._lock:
            cache_size_current = len(self._uuid_cache)
            cache_utilization = (cache_size_current / self.cache_size) * 100
            
            collision_rate = (
                (self._collision_count / self._generation_count) * 100
                if self._generation_count > 0 else 0
            )
            
            avg_retries = (
                self._retry_count / self._generation_count
                if self._generation_count > 0 else 0
            )
            
            return {
                "generation_count": self._generation_count,
                "collision_count": self._collision_count,
                "retry_count": self._retry_count,
                "collision_rate_percent": round(collision_rate, 4),
                "average_retries_per_generation": round(avg_retries, 4),
                "cache_size_current": cache_size_current,
                "cache_size_max": self.cache_size,
                "cache_utilization_percent": round(cache_utilization, 2),
                "cache_ttl_minutes": self.cache_ttl.total_seconds() / 60
            }
    
    def clear_cache(self) -> None:
        """Clear the UUID cache (for testing or maintenance)."""
        with self._lock:
            self._uuid_cache.clear()
            self._uuid_timestamps.clear()
            logger.info("UUID cache cleared")
    
    def validate_uuid_uniqueness(self, test_uuid: uuid.UUID) -> bool:
        """
        Validate that a UUID is unique (not in cache).
        
        Args:
            test_uuid: UUID to validate
            
        Returns:
            bool: True if UUID is unique (not in cache)
        """
        with self._lock:
            return not self._is_uuid_in_cache(test_uuid)


# Global instance for application-wide use
_global_id_generator: Optional[UniqueIDGenerator] = None


def get_unique_id_generator() -> UniqueIDGenerator:
    """
    Get the global UniqueIDGenerator instance.
    
    Returns:
        UniqueIDGenerator: Global instance
    """
    global _global_id_generator
    
    if _global_id_generator is None:
        _global_id_generator = UniqueIDGenerator()
    
    return _global_id_generator


def generate_unique_uuid(namespace: Optional[str] = None) -> uuid.UUID:
    """
    Convenience function to generate a unique UUID.
    
    Args:
        namespace: Optional namespace for UUID generation
        
    Returns:
        uuid.UUID: A guaranteed unique UUID
    """
    return get_unique_id_generator().generate_uuid(namespace)


def generate_unique_uuid_string(namespace: Optional[str] = None) -> str:
    """
    Convenience function to generate a unique UUID string.
    
    Args:
        namespace: Optional namespace for UUID generation
        
    Returns:
        str: A guaranteed unique UUID string
    """
    return get_unique_id_generator().generate_uuid_string(namespace)


# Example usage and testing
if __name__ == "__main__":
    # Basic usage example
    generator = UniqueIDGenerator(cache_size=1000, cache_ttl_minutes=30)
    
    # Generate some UUIDs
    for i in range(10):
        unique_id = generator.generate_uuid()
        print(f"Generated UUID {i+1}: {unique_id}")
    
    # Print statistics
    stats = generator.get_statistics()
    print("\nGeneration Statistics:")
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    # Test collision detection (artificially force collision)
    print("\nTesting collision detection...")
    test_uuid = uuid.uuid4()
    
    # Add to cache manually
    generator._add_uuid_to_cache(test_uuid)
    
    # Try to validate - should return False (not unique)
    is_unique = generator.validate_uuid_uniqueness(test_uuid)
    print(f"Test UUID {test_uuid} is unique: {is_unique}")
