/**
 * Chat Standalone CSS - Estilos para o sistema de chat independente
 */

.chat-container {
    display: flex;
    flex-direction: column;
    height: 600px;
    max-height: 80vh;
    background: var(--bg-secondary, #f8f9fa);
    border: 1px solid var(--border-color, #dee2e6);
    border-radius: 8px;
    overflow: hidden;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: var(--primary-color, #007bff);
    color: white;
    border-bottom: 1px solid var(--border-color, #dee2e6);
}

.chat-header h3 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    background: white;
    scroll-behavior: smooth;
}

.chat-message {
    margin-bottom: 1rem;
    padding: 0.75rem;
    border-radius: 8px;
    max-width: 85%;
    word-wrap: break-word;
}

.chat-message.user {
    background: #e3f2fd;
    border-left: 4px solid #2196f3;
    margin-left: auto;
    margin-right: 0;
}

.chat-message.assistant {
    background: #f1f8e9;
    border-left: 4px solid #4caf50;
    margin-left: 0;
    margin-right: auto;
}

.chat-message.system {
    background: #fff3e0;
    border-left: 4px solid #ff9800;
    margin: 0 auto;
    text-align: center;
    max-width: 70%;
}

.message-content {
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.message-content strong {
    color: var(--text-primary, #333);
}

.message-time {
    font-size: 0.75rem;
    color: var(--text-muted, #6c757d);
    text-align: right;
}

.chat-input-area {
    padding: 1rem;
    background: var(--bg-secondary, #f8f9fa);
    border-top: 1px solid var(--border-color, #dee2e6);
}

.input-group {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.input-group textarea {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid var(--border-color, #dee2e6);
    border-radius: 4px;
    resize: vertical;
    min-height: 60px;
    font-family: inherit;
    font-size: 0.9rem;
    line-height: 1.4;
}

.input-group textarea:focus {
    outline: none;
    border-color: var(--primary-color, #007bff);
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.input-group textarea:disabled {
    background-color: #e9ecef;
    opacity: 0.6;
}

.btn-primary, .btn-secondary {
    padding: 0.75rem 1rem;
    border: none;
    border-radius: 4px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.btn-primary {
    background: var(--primary-color, #007bff);
    color: white;
    min-width: 100px;
}

.btn-primary:hover:not(:disabled) {
    background: #0056b3;
    transform: translateY(-1px);
}

.btn-primary:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
}

.btn-secondary {
    background: #6c757d;
    color: white;
    font-size: 0.8rem;
    padding: 0.5rem 0.75rem;
}

.btn-secondary:hover:not(:disabled) {
    background: #545b62;
    transform: translateY(-1px);
}

.chat-status {
    font-size: 0.8rem;
    color: var(--text-muted, #6c757d);
    text-align: center;
    margin-top: 0.5rem;
    min-height: 1.2rem;
}

/* Scrollbar personalizada */
.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Responsividade */
@media (max-width: 768px) {
    .chat-container {
        height: 500px;
    }
    
    .chat-header {
        padding: 0.75rem;
    }
    
    .chat-header h3 {
        font-size: 1rem;
    }
    
    .chat-messages {
        padding: 0.75rem;
    }
    
    .chat-message {
        max-width: 95%;
        padding: 0.5rem;
    }
    
    .input-group {
        flex-direction: column;
    }
    
    .btn-primary {
        min-width: auto;
    }
}

/* Animações */
.chat-message {
    animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Estados de loading */
.chat-status.loading {
    color: var(--primary-color, #007bff);
    font-weight: 500;
}

.chat-status.error {
    color: #dc3545;
    font-weight: 500;
}

.chat-status.success {
    color: #28a745;
    font-weight: 500;
}
