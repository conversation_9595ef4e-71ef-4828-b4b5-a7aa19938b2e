/**
 * 🧠 Chat RAG - Estilos Elipsys.ai Design System
 * Design moderno e responsivo para interface de chat inteligente
 */

/* Container principal do chat */
.chat-container {
    display: flex;
    flex-direction: column;
    height: 75vh;
    max-height: 800px;
    background: var(--bg-gradient-card);
    border-radius: 20px;
    border: 1px solid var(--border-color);
    overflow: hidden;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.12),
        0 2px 6px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    position: relative;
}

.chat-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--bg-gradient);
}

/* Layout com sidebar para navegação */
.chat-main-area {
    display: flex;
    flex: 1;
    position: relative;
}

/* Sidebar de navegação para respostas longas */
.chat-navigation-sidebar {
    width: 250px;
    background: var(--bg-sidebar);
    border-right: 1px solid var(--border-color);
    padding: 16px;
    overflow-y: auto;
    display: none; /* Oculta por padrão, mostra quando há conteúdo longo */
}

.chat-navigation-sidebar.visible {
    display: block;
}

.nav-section {
    margin-bottom: 20px;
}

.nav-section h4 {
    color: var(--text-primary);
    font-size: 0.9em;
    font-weight: 600;
    margin-bottom: 8px;
    padding-bottom: 4px;
    border-bottom: 1px solid var(--border-subtle);
}

.nav-item {
    padding: 8px 12px;
    margin: 4px 0;
    border-radius: 8px;
    cursor: pointer;
    transition: all var(--transition-base);
    font-size: 0.85em;
    color: var(--text-secondary);
}

.nav-item:hover {
    background: rgba(14, 165, 233, 0.1);
    color: var(--text-primary);
}

.nav-item.active {
    background: var(--bg-gradient);
    color: var(--text-primary);
    font-weight: 600;
}

/* Área de mensagens - Design moderno */
.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 24px;
    background: var(--bg-primary);
    scroll-behavior: smooth;
    position: relative;
    max-width: 100%;
}

/* Quando a sidebar está visível, ajusta o layout */
.chat-navigation-sidebar.visible + .chat-messages {
    padding-right: 40px;
}

.chat-messages::-webkit-scrollbar {
    width: 8px;
}

.chat-messages::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: 4px;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: var(--border-hover);
}

/* Mensagem de boas-vindas - Design moderno */
.welcome-message {
    background: var(--bg-gradient-card);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    padding: 32px;
    margin-bottom: 24px;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.12),
        0 2px 6px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.welcome-message::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--bg-gradient);
}

.welcome-message h3 {
    color: var(--text-primary);
    margin: 0 0 16px 0;
    font-size: 1.5em;
    font-weight: 700;
    background: var(--bg-gradient);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.welcome-message p {
    color: var(--text-secondary);
    margin: 0 0 24px 0;
    line-height: 1.6;
    font-size: 1.05em;
}

/* Lista de recursos - Design moderno */
.features-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px 20px;
    background: rgba(14, 165, 233, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(14, 165, 233, 0.1);
    transition: all var(--transition-base);
}

.feature-item:hover {
    background: rgba(14, 165, 233, 0.1);
    border-color: rgba(14, 165, 233, 0.2);
    transform: translateY(-2px);
}

.feature-icon {
    font-size: 1.4em;
    min-width: 28px;
    color: var(--primary-color);
}

.feature-text {
    font-size: 0.95em;
    color: var(--text-secondary);
    font-weight: 500;
}

/* Exemplos de consultas */
.example-queries h4 {
    color: #1565c0;
    margin: 0 0 12px 0;
    font-size: 1.1em;
    font-weight: 600;
}

.query-examples {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.example-query {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid #1976d2;
    border-radius: 8px;
    padding: 12px 16px;
    color: #1565c0;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9em;
    text-align: left;
    font-style: italic;
}

.example-query:hover {
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    border-color: #1565c0;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(25, 118, 210, 0.2);
}

/* Mensagens do chat - Design moderno */
.message {
    margin-bottom: 24px;
    animation: fadeInUp 0.4s ease;
    max-width: 100%;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding: 0 4px;
}

.message-sender {
    font-weight: 600;
    font-size: 0.9em;
    display: flex;
    align-items: center;
    gap: 8px;
}

.user-message .message-sender {
    color: var(--primary-color);
}

.assistant-message .message-sender {
    color: var(--accent-color);
}

.message-time {
    font-size: 0.8em;
    color: var(--text-muted);
    font-weight: 500;
}

.message-content {
    padding: 20px 24px;
    border-radius: 16px;
    line-height: 1.7;
    word-wrap: break-word;
    position: relative;
    overflow-wrap: break-word;
    max-width: 100%;
}

.user-message .message-content {
    background: var(--bg-gradient-card);
    border: 1px solid var(--border-color);
    margin-left: 0;
    box-shadow:
        0 4px 12px rgba(14, 165, 233, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.assistant-message .message-content {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    margin-right: 0;
    box-shadow:
        0 4px 12px rgba(139, 92, 246, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

/* Citações e metadados - Design moderno */
.message-citations {
    margin-top: 20px;
    padding: 20px;
    background: rgba(14, 165, 233, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(14, 165, 233, 0.1);
    border-left: 4px solid var(--primary-color);
}

.message-citations h5 {
    margin: 0 0 12px 0;
    color: var(--primary-color);
    font-size: 0.95em;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.message-citations ul {
    margin: 0;
    padding-left: 0;
    list-style: none;
}

.message-citations li {
    margin-bottom: 8px;
    font-size: 0.9em;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.similarity {
    color: var(--success-color);
    font-weight: 600;
    font-size: 0.8em;
    padding: 2px 8px;
    background: rgba(34, 197, 94, 0.1);
    border-radius: 12px;
    border: 1px solid rgba(34, 197, 94, 0.2);
}

.message-metadata {
    margin-top: 12px;
    text-align: right;
    padding: 8px 0;
    border-top: 1px solid var(--border-subtle);
}

.message-metadata small {
    color: var(--text-muted);
    font-size: 0.8em;
    font-weight: 500;
}

/* Área de input - Design moderno */
.chat-input-container {
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
    padding: 20px;
    backdrop-filter: blur(10px);
}

.chat-input-wrapper {
    display: flex;
    gap: 12px;
    align-items: flex-end;
}

.chat-input {
    flex: 1;
    min-height: 44px;
    max-height: 120px;
    padding: 12px 16px;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    font-size: 14px;
    font-family: inherit;
    resize: none;
    transition: all 0.2s ease;
    background: #ffffff;
}

.chat-input:focus {
    outline: none;
    border-color: #1976d2;
    box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
}

.chat-input:disabled {
    background: #f5f5f5;
    color: #9e9e9e;
    cursor: not-allowed;
}

.send-button {
    min-width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
    border: none;
    border-radius: 12px;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.send-button:hover:not(:disabled) {
    background: linear-gradient(135deg, #1565c0 0%, #0d47a1 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
}

.send-button:disabled {
    background: #e0e0e0;
    color: #9e9e9e;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.send-icon {
    font-size: 1.2em;
}

/* Status do chat */
.chat-status {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 12px;
    font-size: 0.85em;
}

.status-indicator {
    font-size: 0.8em;
    animation: pulse 2s infinite;
}

.status-indicator.loading {
    animation: spin 1s linear infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.status-text {
    color: #666;
}

/* Responsividade */
@media (max-width: 768px) {
    .chat-container {
        height: 60vh;
        border-radius: 8px;
    }

    /* Oculta sidebar em telas pequenas */
    .chat-navigation-sidebar {
        display: none !important;
    }

    .features-list {
        grid-template-columns: 1fr;
    }

    .query-examples {
        gap: 6px;
    }

    .example-query {
        padding: 10px 12px;
        font-size: 0.85em;
    }

    .message-content {
        padding: 12px;
    }

    .user-message .message-content {
        margin-left: 0;
    }

    .assistant-message .message-content {
        margin-right: 0;
    }
}

/* Responsividade para tablets */
@media (max-width: 1024px) and (min-width: 769px) {
    .chat-navigation-sidebar {
        width: 200px;
    }

    .nav-item {
        padding: 6px 10px;
        font-size: 0.8em;
    }
}
