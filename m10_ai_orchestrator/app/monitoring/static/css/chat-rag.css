/**
 * 🧠 Chat RAG - Estilos para o Farejador de Informações
 * Design moderno e responsivo para interface de chat inteligente
 */

/* Container principal do chat */
.chat-container {
    display: flex;
    flex-direction: column;
    height: 70vh;
    max-height: 600px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    border: 1px solid #dee2e6;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* <PERSON><PERSON> de mensagens */
.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background: #ffffff;
    scroll-behavior: smooth;
}

.chat-messages::-webkit-scrollbar {
    width: 8px;
}

.chat-messages::-webkit-scrollbar-track {
    background: #f1f3f4;
    border-radius: 4px;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: #c1c8cd;
    border-radius: 4px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: #a8b2ba;
}

/* Mensagem de boas-vindas */
.welcome-message {
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    border: 1px solid #bbdefb;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.welcome-message h3 {
    color: #1565c0;
    margin: 0 0 16px 0;
    font-size: 1.4em;
    font-weight: 600;
}

.welcome-message p {
    color: #424242;
    margin: 0 0 20px 0;
    line-height: 1.6;
}

/* Lista de recursos */
.features-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 12px;
    margin-bottom: 20px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 8px;
    border: 1px solid rgba(25, 118, 210, 0.2);
}

.feature-icon {
    font-size: 1.2em;
    min-width: 24px;
}

.feature-text {
    font-size: 0.9em;
    color: #37474f;
}

/* Exemplos de consultas */
.example-queries h4 {
    color: #1565c0;
    margin: 0 0 12px 0;
    font-size: 1.1em;
    font-weight: 600;
}

.query-examples {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.example-query {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid #1976d2;
    border-radius: 8px;
    padding: 12px 16px;
    color: #1565c0;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9em;
    text-align: left;
    font-style: italic;
}

.example-query:hover {
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    border-color: #1565c0;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(25, 118, 210, 0.2);
}

/* Mensagens do chat */
.message {
    margin-bottom: 20px;
    animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.message-sender {
    font-weight: 600;
    font-size: 0.9em;
}

.user-message .message-sender {
    color: #1976d2;
}

.assistant-message .message-sender {
    color: #7b1fa2;
}

.message-time {
    font-size: 0.8em;
    color: #757575;
}

.message-content {
    padding: 16px;
    border-radius: 12px;
    line-height: 1.6;
    word-wrap: break-word;
}

.user-message .message-content {
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    border: 1px solid #bbdefb;
    margin-left: 20px;
}

.assistant-message .message-content {
    background: linear-gradient(135deg, #f3e5f5 0%, #fce4ec 100%);
    border: 1px solid #e1bee7;
    margin-right: 20px;
}

/* Citações e metadados */
.message-citations {
    margin-top: 16px;
    padding: 12px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    border-left: 4px solid #ff9800;
}

.message-citations h5 {
    margin: 0 0 8px 0;
    color: #f57c00;
    font-size: 0.9em;
}

.message-citations ul {
    margin: 0;
    padding-left: 20px;
}

.message-citations li {
    margin-bottom: 4px;
    font-size: 0.85em;
}

.similarity {
    color: #4caf50;
    font-weight: 500;
    margin-left: 8px;
}

.message-metadata {
    margin-top: 8px;
    text-align: right;
}

.message-metadata small {
    color: #757575;
    font-size: 0.75em;
}

/* Área de input */
.chat-input-container {
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
    padding: 16px;
}

.chat-input-wrapper {
    display: flex;
    gap: 12px;
    align-items: flex-end;
}

.chat-input {
    flex: 1;
    min-height: 44px;
    max-height: 120px;
    padding: 12px 16px;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    font-size: 14px;
    font-family: inherit;
    resize: none;
    transition: all 0.2s ease;
    background: #ffffff;
}

.chat-input:focus {
    outline: none;
    border-color: #1976d2;
    box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
}

.chat-input:disabled {
    background: #f5f5f5;
    color: #9e9e9e;
    cursor: not-allowed;
}

.send-button {
    min-width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
    border: none;
    border-radius: 12px;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.send-button:hover:not(:disabled) {
    background: linear-gradient(135deg, #1565c0 0%, #0d47a1 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
}

.send-button:disabled {
    background: #e0e0e0;
    color: #9e9e9e;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.send-icon {
    font-size: 1.2em;
}

/* Status do chat */
.chat-status {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 12px;
    font-size: 0.85em;
}

.status-indicator {
    font-size: 0.8em;
    animation: pulse 2s infinite;
}

.status-indicator.loading {
    animation: spin 1s linear infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.status-text {
    color: #666;
}

/* Responsividade */
@media (max-width: 768px) {
    .chat-container {
        height: 60vh;
        border-radius: 8px;
    }
    
    .features-list {
        grid-template-columns: 1fr;
    }
    
    .query-examples {
        gap: 6px;
    }
    
    .example-query {
        padding: 10px 12px;
        font-size: 0.85em;
    }
    
    .message-content {
        padding: 12px;
    }
    
    .user-message .message-content {
        margin-left: 10px;
    }
    
    .assistant-message .message-content {
        margin-right: 10px;
    }
}
