/* ==========================================================================
   M10 AI Orchestrator Monitor - Elipsys.ai Design System
   ========================================================================== */

/* Reset & Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Modern React/Node.js Inspired Color System */
    --primary-color: #0ea5e9;
    --primary-dark: #0284c7;
    --primary-light: #38bdf8;
    --secondary-color: #64748b;
    --accent-color: #8b5cf6;
    --accent-orange: #f97316;
    --accent-green: #22c55e;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --info-color: #3b82f6;

    /* Modern Dark Theme Backgrounds */
    --bg-primary: #0a0a0f;
    --bg-secondary: #1a1a2e;
    --bg-tertiary: #16213e;
    --bg-dark: #050505;
    --bg-sidebar: #0f1419;
    --bg-card: #1e293b;
    --bg-overlay: rgba(0, 0, 0, 0.9);
    --bg-gradient: linear-gradient(135deg, #0ea5e9 0%, #8b5cf6 100%);
    --bg-gradient-card: linear-gradient(145deg, #1e293b 0%, #334155 100%);
    --bg-gradient-sidebar: linear-gradient(180deg, #0f1419 0%, #1a1a2e 100%);

    /* Modern Text Colors */
    --text-primary: #f8fafc;
    --text-secondary: #e2e8f0;
    --text-muted: #94a3b8;
    --text-inverse: #0f172a;
    --text-light: #cbd5e1;
    --text-accent: #0ea5e9;

    /* Modern Border Colors */
    --border-color: #334155;
    --border-hover: #475569;
    --border-focus: #0ea5e9;
    --border-accent: rgba(14, 165, 233, 0.3);
    --border-subtle: #1e293b;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* Typography */
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    
    /* Layout */
    --header-height: 64px;
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 64px;
    --footer-height: 48px;
    
    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-base: 300ms ease-in-out;
    --transition-slow: 500ms ease-in-out;
    
    /* Z-index layers */
    --z-dropdown: 1000;
    --z-modal: 1050;
    --z-overlay: 1100;
    --z-tooltip: 1200;
}

/* Dark Theme Variables */
[data-theme="dark"] {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --bg-card: #1e293b;
    --bg-sidebar: #0f172a;
    
    --text-primary: #f1f5f9;
    --text-secondary: #cbd5e1;
    --text-muted: #64748b;
    
    --border-color: #334155;
    --border-hover: #475569;
}

/* Base HTML & Body */
html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--bg-primary);
    background-image:
        radial-gradient(circle at 25% 25%, rgba(14, 165, 233, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
        linear-gradient(180deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
    overflow-x: hidden;
    font-weight: 400;
    letter-spacing: -0.01em;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-secondary);
    overflow-x: hidden;
}

/* Skip Link for Accessibility */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-color);
    color: var(--text-inverse);
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: var(--z-tooltip);
    transition: top var(--transition-fast);
}

.skip-link:focus {
    top: 6px;
}

/* ==========================================================================
   Header Styles
   ========================================================================== */

.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: var(--header-height);
    background: rgba(15, 15, 35, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-color);
    box-shadow:
        0 4px 32px rgba(0, 0, 0, 0.1),
        0 1px 0 rgba(255, 255, 255, 0.05);
    z-index: 100;
}

.header-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 var(--spacing-lg);
    max-width: 100%;
}

.header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    flex-shrink: 0;
}

.menu-toggle {
    display: none;
    flex-direction: column;
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: 4px;
    transition: background-color var(--transition-fast);
}

.menu-toggle:hover {
    background-color: var(--bg-tertiary);
}

.hamburger {
    width: 20px;
    height: 2px;
    background-color: var(--text-primary);
    margin: 2px 0;
    transition: var(--transition-fast);
}

.header-title {
    font-size: var(--font-size-xl);
    font-weight: 700;
    background: var(--bg-gradient);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.header-icon {
    font-size: var(--font-size-2xl);
}

.header-center {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
}

.system-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: 20px;
    border: 1px solid var(--border-color);
}

.status-indicator {
    display: flex;
    align-items: center;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--warning-color);
    animation: pulse 2s infinite;
}

.status-indicator[data-status="healthy"] .status-dot {
    background-color: var(--success-color);
    animation: none;
}

.status-indicator[data-status="error"] .status-dot {
    background-color: var(--error-color);
    animation: pulse 1s infinite;
}

.status-text {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-secondary);
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    flex-shrink: 0;
}

.header-nav {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.nav-button {
    position: relative;
    background: none;
    border: none;
    padding: var(--spacing-sm);
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    transition: background-color var(--transition-fast);
}

.nav-button:hover {
    background-color: var(--bg-tertiary);
}

.nav-button .icon {
    font-size: var(--font-size-lg);
}

.notification-badge {
    position: absolute;
    top: -2px;
    right: -2px;
    background: var(--error-color);
    color: var(--text-inverse);
    font-size: var(--font-size-xs);
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
    font-weight: 600;
}

.last-update {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    text-align: right;
}

.update-label {
    display: block;
}

/* ==========================================================================
   Layout Styles
   ========================================================================== */

.layout {
    display: flex;
    min-height: 100vh;
    padding-top: var(--header-height);
}

/* ==========================================================================
   Sidebar Styles
   ========================================================================== */

.sidebar {
    position: fixed;
    left: 0;
    top: var(--header-height);
    bottom: 0;
    width: var(--sidebar-width);
    background: var(--bg-gradient-sidebar);
    border-right: 1px solid var(--border-subtle);
    overflow-y: auto;
    z-index: 50;
    display: flex;
    flex-direction: column;
    transition: all var(--transition-base);
    backdrop-filter: blur(20px);
    box-shadow:
        4px 0 24px rgba(0, 0, 0, 0.15),
        inset -1px 0 0 rgba(255, 255, 255, 0.05);
}

.sidebar-nav {
    flex: 1;
    padding: var(--spacing-lg) 0;
}

.nav-list {
    list-style: none;
}

.nav-item {
    margin: 0;
}

.nav-divider {
    height: 1px;
    background: var(--border-color);
    margin: var(--spacing-lg) var(--spacing-md);
}

.nav-link {
    display: flex;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-lg);
    margin: 0 var(--spacing-sm);
    color: var(--text-secondary);
    text-decoration: none;
    transition: all var(--transition-base);
    position: relative;
    gap: var(--spacing-md);
    border-radius: 12px;
    font-weight: 500;
    font-size: var(--font-size-sm);
}

.nav-link:hover {
    background: rgba(14, 165, 233, 0.1);
    color: var(--text-primary);
    transform: translateX(4px);
    box-shadow: 0 4px 12px rgba(14, 165, 233, 0.15);
}

.nav-link.active {
    background: linear-gradient(135deg, rgba(14, 165, 233, 0.2) 0%, rgba(139, 92, 246, 0.2) 100%);
    color: var(--text-primary);
    border: 1px solid var(--border-accent);
    box-shadow:
        0 4px 12px rgba(14, 165, 233, 0.25),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.nav-link.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 20px;
    background: var(--bg-gradient);
    border-radius: 0 2px 2px 0;
}

.nav-icon {
    font-size: var(--font-size-lg);
    width: 24px;
    text-align: center;
    flex-shrink: 0;
}

.nav-text {
    font-weight: 500;
    flex: 1;
}

.nav-badge {
    background: var(--success-color);
    color: var(--text-inverse);
    font-size: var(--font-size-xs);
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 600;
    min-width: 20px;
    text-align: center;
}

.alert-badge {
    background: var(--error-color);
}

.sidebar-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

.connection-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.connection-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--success-color);
}

.connection-dot[data-status="disconnected"] {
    background-color: var(--error-color);
    animation: pulse 2s infinite;
}

.connection-text {
    font-size: var(--font-size-sm);
    color: var(--text-light);
}

.version-info {
    text-align: center;
}

.version-text {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    font-family: var(--font-mono);
}

/* ==========================================================================
   Main Content Styles
   ========================================================================== */

.main-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    padding: var(--spacing-xl);
    min-height: calc(100vh - var(--header-height) - var(--footer-height));
    background: var(--bg-secondary);
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
    animation: fadeIn 0.3s ease-in-out;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 2px solid var(--border-color);
}

.section-title {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.section-actions {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
}

.action-button {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--bg-gradient);
    color: var(--text-primary);
    border: 1px solid var(--border-accent);
    border-radius: 12px;
    font-size: var(--font-size-sm);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
    box-shadow:
        0 4px 14px 0 rgba(139, 92, 246, 0.25),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.action-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-base);
}

.action-button:hover {
    transform: translateY(-2px);
    box-shadow:
        0 8px 25px 0 rgba(139, 92, 246, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border-color: var(--primary-light);
}

.action-button:hover::before {
    left: 100%;
}

.action-button .icon {
    font-size: var(--font-size-base);
}

.time-range-selector {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    cursor: pointer;
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-xl);
}

/* Widget Styles - Elipsys.ai Inspired */
.widget {
    background: var(--bg-gradient-card);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.12),
        0 2px 6px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all var(--transition-base);
    overflow: hidden;
    position: relative;
}

.widget::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(139, 92, 246, 0.5), transparent);
}

.widget:hover {
    transform: translateY(-2px);
    box-shadow:
        0 16px 48px rgba(0, 0, 0, 0.2),
        0 4px 12px rgba(0, 0, 0, 0.12),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    border-color: var(--border-accent);
}

.widget-full {
    grid-column: 1 / -1;
}

.widget-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    background: rgba(26, 26, 46, 0.8);
    backdrop-filter: blur(10px);
}

.widget-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.widget-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.widget-action {
    background: none;
    border: none;
    padding: var(--spacing-xs);
    border-radius: 4px;
    cursor: pointer;
    color: var(--text-muted);
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
}

.widget-action:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.widget-content {
    padding: var(--spacing-lg);
}

/* System Metrics Widget */
.metrics-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.metric-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    background: var(--bg-gradient-card);
    border-radius: 16px;
    border: 1px solid var(--border-color);
    box-shadow:
        0 4px 6px -1px rgba(0, 0, 0, 0.1),
        0 2px 4px -1px rgba(0, 0, 0, 0.06),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--bg-gradient);
}

.metric-card:hover {
    transform: translateY(-4px);
    box-shadow:
        0 10px 25px -3px rgba(0, 0, 0, 0.2),
        0 4px 6px -2px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    border-color: var(--border-accent);
}

.metric-icon {
    font-size: var(--font-size-2xl);
    flex-shrink: 0;
}

.metric-info {
    flex: 1;
    min-width: 0;
}

.metric-label {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    font-weight: 500;
    margin-bottom: var(--spacing-xs);
}

.metric-value {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.metric-trend {
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.metric-trend::before {
    content: "↗";
    margin-right: var(--spacing-xs);
    color: var(--success-color);
}

.metric-trend.down::before {
    content: "↘";
    color: var(--error-color);
}

.metric-trend.stable::before {
    content: "→";
    color: var(--warning-color);
}

.metric-gauge {
    flex-shrink: 0;
}

.system-chart {
    height: 200px;
    margin-top: var(--spacing-lg);
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Services Status Widget */
.services-overview {
    margin-bottom: var(--spacing-lg);
}

.service-summary {
    display: flex;
    justify-content: space-around;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    background: var(--bg-secondary);
    border-radius: 8px;
    margin-bottom: var(--spacing-lg);
}

.summary-stat {
    text-align: center;
    flex: 1;
}

.stat-value {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    font-weight: 500;
}

.services-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.service-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: 6px;
    border-left: 4px solid var(--success-color);
    transition: background-color var(--transition-fast);
}

.service-item.degraded {
    border-left-color: var(--warning-color);
}

.service-item.critical {
    border-left-color: var(--error-color);
}

.service-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    flex: 1;
}

.service-name {
    font-weight: 600;
    color: var(--text-primary);
}

.service-status {
    font-size: var(--font-size-sm);
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 500;
    color: var(--text-inverse);
    background: var(--success-color);
}

.service-status.degraded {
    background: var(--warning-color);
}

.service-status.critical {
    background: var(--error-color);
}

.service-response-time {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    font-family: var(--font-mono);
}

.service-loading {
    text-align: center;
    padding: var(--spacing-xl);
    color: var(--text-muted);
    font-style: italic;
}

/* AI Metrics Widget */
.ai-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.ai-stat {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: 8px;
    transition: transform var(--transition-fast);
}

.ai-stat:hover {
    transform: translateY(-1px);
}

.ai-stat-icon {
    font-size: var(--font-size-xl);
    flex-shrink: 0;
}

.ai-stat-info {
    flex: 1;
    min-width: 0;
}

.ai-stat-value {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.ai-stat-label {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    font-weight: 500;
}

.ai-performance {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.performance-metric {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: 6px;
}

.performance-label {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-secondary);
    min-width: 100px;
    flex-shrink: 0;
}

.performance-bar {
    flex: 1;
    height: 8px;
    background: var(--bg-tertiary);
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.performance-fill {
    height: 100%;
    background: var(--success-color);
    border-radius: 4px;
    transition: width var(--transition-base);
    position: relative;
}

.performance-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    animation: shimmer 2s infinite;
}

.performance-value {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-primary);
    min-width: 60px;
    text-align: right;
    flex-shrink: 0;
}

/* Activity Timeline Widget */
.activity-timeline {
    max-height: 300px;
    overflow-y: auto;
    padding-right: var(--spacing-sm);
}

.activity-item {
    display: flex;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
    transition: background-color var(--transition-fast);
}

.activity-item:hover {
    background: var(--bg-secondary);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-time {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    font-family: var(--font-mono);
    min-width: 60px;
    flex-shrink: 0;
}

.activity-icon {
    font-size: var(--font-size-base);
    width: 24px;
    text-align: center;
    flex-shrink: 0;
    margin-top: 2px;
}

.activity-content {
    flex: 1;
    min-width: 0;
}

.activity-title {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.activity-description {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    line-height: 1.4;
}

.activity-loading {
    text-align: center;
    padding: var(--spacing-xl);
    color: var(--text-muted);
    font-style: italic;
}

/* Chart animations */
@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.widget-placeholder {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-sm);
    transition: box-shadow var(--transition-fast);
}

.widget-placeholder:hover {
    box-shadow: var(--shadow-md);
}

.widget-placeholder h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.widget-placeholder p {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

/* Placeholder sections for other content */
.services-placeholder,
.metrics-placeholder,
.alerts-placeholder,
.chat-placeholder,
.debug-placeholder,
.settings-placeholder {
    background: var(--bg-card);
    border: 2px dashed var(--border-color);
    border-radius: 12px;
    padding: var(--spacing-2xl);
    text-align: center;
    color: var(--text-muted);
    font-size: var(--font-size-lg);
}

/* ==========================================================================
   Footer Styles
   ========================================================================== */

.footer {
    background: var(--bg-primary);
    border-top: 1px solid var(--border-color);
    padding: var(--spacing-md) 0;
    margin-left: var(--sidebar-width);
}

.footer-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 var(--spacing-xl);
}

.footer-text {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

/* ==========================================================================
   Loading & Modal Styles
   ========================================================================== */

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--bg-overlay);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: var(--z-overlay);
}

.loading-overlay[aria-hidden="false"] {
    display: flex;
}

.loading-spinner {
    text-align: center;
    color: var(--text-inverse);
}

.spinner-ring {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-md);
}

.loading-text {
    font-size: var(--font-size-base);
    font-weight: 500;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--bg-overlay);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modal);
}

.modal-overlay[aria-hidden="false"] {
    display: flex;
}

.modal-content {
    background: var(--bg-primary);
    border-radius: 12px;
    box-shadow: var(--shadow-xl);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--font-size-2xl);
    cursor: pointer;
    padding: var(--spacing-xs);
    color: var(--text-muted);
    transition: color var(--transition-fast);
}

.modal-close:hover {
    color: var(--text-primary);
}

.modal-body {
    padding: var(--spacing-lg);
}

.modal-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
}

.button {
    padding: var(--spacing-md) var(--spacing-lg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    font-size: var(--font-size-sm);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.button-primary {
    background: var(--bg-gradient);
    color: var(--text-primary);
    border-color: var(--border-accent);
    box-shadow:
        0 4px 14px 0 rgba(139, 92, 246, 0.25),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.button-primary:hover {
    transform: translateY(-2px);
    box-shadow:
        0 8px 25px 0 rgba(139, 92, 246, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.button-secondary {
    background: var(--bg-card);
    color: var(--text-primary);
    border-color: var(--border-color);
}

.button-secondary:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-hover);
    transform: translateY(-1px);
}

/* ==========================================================================
   Animations
   ========================================================================== */

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */

/* Tablet Styles */
@media (max-width: 1024px) {
    .header-container {
        padding: 0 var(--spacing-md);
    }
    
    .main-content {
        padding: var(--spacing-lg);
    }
    
    .dashboard-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--spacing-lg);
    }
    
    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
    }
    
    .section-actions {
        align-self: stretch;
        justify-content: flex-end;
    }
}

/* Mobile Styles */
@media (max-width: 768px) {
    :root {
        --sidebar-width: 100%;
    }
    
    .menu-toggle {
        display: flex;
    }
    
    .header-title {
        font-size: var(--font-size-lg);
    }
    
    .header-center {
        display: none;
    }
    
    .header-right {
        gap: var(--spacing-sm);
    }
    
    .last-update {
        display: none;
    }
    
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.open {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
        padding: var(--spacing-md);
    }
    
    .footer {
        margin-left: 0;
    }
    
    .footer-container {
        flex-direction: column;
        gap: var(--spacing-sm);
        text-align: center;
        padding: 0 var(--spacing-md);
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .section-title {
        font-size: var(--font-size-2xl);
    }
    
    .action-button .button-text {
        display: none;
    }
    
    .nav-text {
        font-size: var(--font-size-sm);
    }
    
    .modal-content {
        width: 95%;
        margin: var(--spacing-md);
    }
}

/* Small Mobile Styles */
@media (max-width: 480px) {
    .header-container {
        padding: 0 var(--spacing-sm);
    }
    
    .main-content {
        padding: var(--spacing-sm);
    }
    
    .section-header {
        margin-bottom: var(--spacing-lg);
    }
    
    .widget-placeholder {
        padding: var(--spacing-lg);
    }
    
    .nav-link {
        padding: var(--spacing-sm) var(--spacing-md);
    }
    
    .sidebar-footer {
        padding: var(--spacing-md);
    }
}

/* Print Styles */
@media print {
    .header,
    .sidebar,
    .footer,
    .loading-overlay,
    .modal-overlay {
        display: none !important;
    }
    
    .main-content {
        margin-left: 0;
        padding: 0;
    }
    
    .content-section {
        display: block !important;
        page-break-after: always;
    }
    
    .content-section:last-child {
        page-break-after: auto;
    }
    
    body {
        background: white;
        color: black;
    }
}

/* ==========================================================================
   Utility Classes
   ========================================================================== */

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }

/* ==========================================================================
   Focus Management & Accessibility
   ========================================================================== */

:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

:focus:not(:focus-visible) {
    outline: none;
}

.nav-link:focus,
.action-button:focus,
.nav-button:focus {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000000;
        --text-muted: #000000;
    }
    
    [data-theme="dark"] {
        --border-color: #ffffff;
        --text-muted: #ffffff;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .skip-link,
    .sidebar,
    .loading-overlay,
    .modal-overlay {
        transition: none !important;
    }
}
