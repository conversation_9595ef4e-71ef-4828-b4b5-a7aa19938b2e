/**
 * M10 AI Orchestrator Monitor - Service Worker
 * Provides offline functionality and resource caching
 */

const CACHE_NAME = 'm10-monitor-v1.0.0';
const STATIC_CACHE_NAME = 'm10-monitor-static-v1.0.0';

// Resources to cache for offline functionality
const STATIC_RESOURCES = [
    '/monitor/', // Main interface HTML
    '/monitor/index.html', // Explicitly cache index.html as well
    '/monitor/static/css/monitor.css',
    '/monitor/static/js/monitor.js',
    '/monitor/static/js/sw.js' // Cache the service worker itself
];

// Resources that should be cached at runtime
const RUNTIME_CACHE_URLS = [
    '/api/health',
    '/api/system/status',
    '/api/metrics',
    '/api/services'
];

/**
 * Service Worker Installation
 * Cache static resources for offline use
 */
self.addEventListener('install', event => {
    console.log('Service Worker installing...');
    
    event.waitUntil(
        caches.open(STATIC_CACHE_NAME)
            .then(cache => {
                console.log('Caching static resources...');
                return cache.addAll(STATIC_RESOURCES);
            })
            .then(() => {
                console.log('Static resources cached successfully');
                // Skip waiting to activate immediately
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('Error caching static resources:', error);
            })
    );
});

/**
 * Service Worker Activation
 * Clean up old caches
 */
self.addEventListener('activate', event => {
    console.log('Service Worker activating...');
    
    event.waitUntil(
        caches.keys()
            .then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        // Delete old caches
                        if (cacheName !== CACHE_NAME && cacheName !== STATIC_CACHE_NAME) {
                            console.log('Deleting old cache:', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('Service Worker activated successfully');
                // Take control of all pages immediately
                return self.clients.claim();
            })
            .catch(error => {
                console.error('Error during service worker activation:', error);
            })
    );
});

/**
 * Fetch Event Handler
 * Implement cache-first strategy for static resources and network-first for API calls
 */
self.addEventListener('fetch', event => {
    const { request } = event;
    const url = new URL(request.url);
    
    // Skip non-GET requests
    if (request.method !== 'GET') {
        return;
    }
    
    // Skip external requests
    if (url.origin !== location.origin) {
        return;
    }
    
    // Handle different types of requests
    if (isStaticResource(request)) {
        event.respondWith(handleStaticResource(request));
    } else if (isAPIRequest(request)) {
        event.respondWith(handleAPIRequest(request));
    } else {
        event.respondWith(handleOtherRequest(request));
    }
});

/**
 * Check if request is for static resource
 * @param {Request} request - Fetch request
 * @returns {boolean} - True if static resource
 */
function isStaticResource(request) {
    const url = new URL(request.url);
    return (
        url.pathname.startsWith('/static/') ||
        url.pathname.endsWith('.css') ||
        url.pathname.endsWith('.js') ||
        url.pathname.endsWith('.html') ||
        url.pathname.endsWith('.ico') ||
        url.pathname.endsWith('.png') ||
        url.pathname.endsWith('.jpg') ||
        url.pathname.endsWith('.svg')
    );
}

/**
 * Check if request is for API endpoint
 * @param {Request} request - Fetch request
 * @returns {boolean} - True if API request
 */
function isAPIRequest(request) {
    const url = new URL(request.url);
    return url.pathname.startsWith('/api/') || url.pathname.startsWith('/ws/');
}

/**
 * Handle static resource requests with cache-first strategy
 * @param {Request} request - Fetch request
 * @returns {Promise<Response>} - Response promise
 */
async function handleStaticResource(request) {
    try {
        // Try cache first
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // If not in cache, fetch from network
        const networkResponse = await fetch(request);
        
        // Cache the response for future use
        if (networkResponse.ok) {
            const cache = await caches.open(STATIC_CACHE_NAME);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
        
    } catch (error) {
        console.error('Error handling static resource:', error);
        
        // Return offline fallback if available
        const fallback = await getOfflineFallback(request);
        if (fallback) {
            return fallback;
        }
        
        // Return error response
        return new Response('Resource not available offline', {
            status: 503,
            statusText: 'Service Unavailable'
        });
    }
}

/**
 * Handle API requests with network-first strategy
 * @param {Request} request - Fetch request
 * @returns {Promise<Response>} - Response promise
 */
async function handleAPIRequest(request) {
    try {
        // Try network first for fresh data
        const networkResponse = await fetch(request);
        
        // Cache successful responses
        if (networkResponse.ok && shouldCacheAPIResponse(request)) {
            const cache = await caches.open(CACHE_NAME);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
        
    } catch (error) {
        console.error('Network request failed, trying cache:', error);
        
        // Fallback to cache
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            // Add offline indicator header
            const response = cachedResponse.clone();
            response.headers.set('X-Served-From', 'cache');
            return response;
        }
        
        // Return offline fallback for API requests
        return getAPIOfflineFallback(request);
    }
}

/**
 * Handle other requests with network-first strategy
 * @param {Request} request - Fetch request
 * @returns {Promise<Response>} - Response promise
 */
async function handleOtherRequest(request) {
    try {
        return await fetch(request);
    } catch (error) {
        console.error('Request failed:', error);
        
        // Try cache as fallback
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // Return basic offline page
        return new Response(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>Offline - M10 Monitor</title>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <style>
                    body {
                        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                        text-align: center;
                        padding: 2rem;
                        background: #f8fafc;
                        color: #334155;
                    }
                    .offline-message {
                        max-width: 400px;
                        margin: 0 auto;
                        padding: 2rem;
                        background: white;
                        border-radius: 8px;
                        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                    }
                    .icon {
                        font-size: 3rem;
                        margin-bottom: 1rem;
                    }
                    .retry-button {
                        margin-top: 1rem;
                        padding: 0.5rem 1rem;
                        background: #2563eb;
                        color: white;
                        border: none;
                        border-radius: 6px;
                        cursor: pointer;
                    }
                </style>
            </head>
            <body>
                <div class="offline-message">
                    <div class="icon">📡</div>
                    <h1>Você está offline</h1>
                    <p>Não foi possível conectar ao M10 Monitor. Verifique sua conexão de internet.</p>
                    <button class="retry-button" onclick="window.location.reload()">
                        Tentar Novamente
                    </button>
                </div>
            </body>
            </html>
        `, {
            headers: { 'Content-Type': 'text/html' }
        });
    }
}

/**
 * Check if API response should be cached
 * @param {Request} request - Fetch request
 * @returns {boolean} - True if should cache
 */
function shouldCacheAPIResponse(request) {
    const url = new URL(request.url);
    return RUNTIME_CACHE_URLS.some(cachePath => url.pathname.startsWith(cachePath));
}

/**
 * Get offline fallback for static resources
 * @param {Request} request - Fetch request
 * @returns {Promise<Response|null>} - Fallback response or null
 */
async function getOfflineFallback(request) {
    const url = new URL(request.url);
    
    // Return cached index.html for navigation requests
    if (request.mode === 'navigate') {
        return await caches.match('/index.html');
    }
    
    return null;
}

/**
 * Get offline fallback for API requests
 * @param {Request} request - Fetch request
 * @returns {Response} - Fallback response
 */
function getAPIOfflineFallback(request) {
    const url = new URL(request.url);
    
    // Return appropriate offline responses for different API endpoints
    if (url.pathname.includes('/health')) {
        return new Response(JSON.stringify({
            status: 'offline',
            message: 'Service offline - cached data may be available',
            timestamp: new Date().toISOString()
        }), {
            headers: { 'Content-Type': 'application/json' }
        });
    }
    
    if (url.pathname.includes('/status')) {
        return new Response(JSON.stringify({
            health: 'offline',
            message: 'Sistema offline',
            services: {},
            lastUpdate: new Date().toISOString()
        }), {
            headers: { 'Content-Type': 'application/json' }
        });
    }
    
    // Generic offline API response
    return new Response(JSON.stringify({
        error: 'Service unavailable',
        message: 'This service is currently offline',
        offline: true
    }), {
        status: 503,
        headers: { 'Content-Type': 'application/json' }
    });
}

/**
 * Handle background sync for offline actions
 */
self.addEventListener('sync', event => {
    console.log('Background sync triggered:', event.tag);
    
    if (event.tag === 'background-sync') {
        event.waitUntil(handleBackgroundSync());
    }
});

/**
 * Handle background sync operations
 */
async function handleBackgroundSync() {
    try {
        console.log('Processing background sync...');
        
        // Here you could implement:
        // - Retry failed API requests
        // - Sync offline data
        // - Update cached resources
        
        console.log('Background sync completed');
    } catch (error) {
        console.error('Background sync failed:', error);
    }
}

/**
 * Handle push notifications
 */
self.addEventListener('push', event => {
    console.log('Push message received:', event);
    
    // Handle push notifications here if needed
    // For now, we'll just log the event
});

/**
 * Message handler for communication with main thread
 */
self.addEventListener('message', event => {
    console.log('Service Worker received message:', event.data);
    
    const { type, payload } = event.data;
    
    switch (type) {
        case 'SKIP_WAITING':
            self.skipWaiting();
            break;
            
        case 'CLEAR_CACHE':
            clearAllCaches();
            break;
            
        case 'UPDATE_CACHE':
            updateCache(payload);
            break;
            
        default:
            console.log('Unknown message type:', type);
    }
});

/**
 * Clear all caches
 */
async function clearAllCaches() {
    try {
        const cacheNames = await caches.keys();
        await Promise.all(
            cacheNames.map(cacheName => caches.delete(cacheName))
        );
        console.log('All caches cleared');
    } catch (error) {
        console.error('Error clearing caches:', error);
    }
}

/**
 * Update cache with new resources
 * @param {Array} resources - Resources to cache
 */
async function updateCache(resources) {
    try {
        const cache = await caches.open(CACHE_NAME);
        await cache.addAll(resources);
        console.log('Cache updated with new resources');
    } catch (error) {
        console.error('Error updating cache:', error);
    }
}

console.log('M10 Monitor Service Worker loaded');
