/**
 * 🧠 Chat RAG - Farejador de Informações Inteligente
 * Sistema de chat com busca semântica e geração contextualizada
 */

class ChatRAG {
    constructor() {
        this.apiUrl = '/api/v1/chat/rag';
        this.sessionId = this.generateSessionId();
        this.isLoading = false;
        this.messageHistory = [];
        console.log('🧠 ChatRAG: Inicializando farejador de informações...');
    }

    /**
     * Inicializa o sistema de chat RAG
     */
    init() {
        console.log('🧠 ChatRAG: Configurando interface...');
        try {
            this.setupEventListeners();
            this.setupExampleQueries();
            console.log('✅ ChatRAG: Sistema pronto para farejar informações!');
        } catch (error) {
            console.error('❌ ChatRAG: Erro na inicialização:', error);
        }
    }

    /**
     * Configura os event listeners
     */
    setupEventListeners() {
        // Input de chat
        const chatInput = document.getElementById('chat-input');
        const sendBtn = document.getElementById('send-btn');
        const clearBtn = document.getElementById('clear-chat-btn');
        const newChatBtn = document.getElementById('new-chat-btn');

        if (chatInput) {
            // Enviar com Enter (Shift+Enter para nova linha)
            chatInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.sendMessage();
                }
            });

            // Habilitar/desabilitar botão de envio
            chatInput.addEventListener('input', () => {
                const hasText = chatInput.value.trim().length > 0;
                sendBtn.disabled = !hasText || this.isLoading;
            });

            // Auto-resize do textarea
            chatInput.addEventListener('input', () => {
                chatInput.style.height = 'auto';
                chatInput.style.height = Math.min(chatInput.scrollHeight, 120) + 'px';
            });
        }

        if (sendBtn) {
            sendBtn.addEventListener('click', () => this.sendMessage());
        }

        if (clearBtn) {
            clearBtn.addEventListener('click', () => this.clearChat());
        }

        if (newChatBtn) {
            newChatBtn.addEventListener('click', () => this.newChat());
        }
    }

    /**
     * Configura as consultas de exemplo
     */
    setupExampleQueries() {
        const exampleButtons = document.querySelectorAll('.example-query');
        exampleButtons.forEach(button => {
            button.addEventListener('click', () => {
                const query = button.getAttribute('data-query');
                const chatInput = document.getElementById('chat-input');
                if (chatInput && query) {
                    chatInput.value = query;
                    chatInput.focus();
                    // Trigger input event para habilitar botão
                    chatInput.dispatchEvent(new Event('input'));
                }
            });
        });
    }
                
                <div class="chat-messages" id="chat-messages">
                    <div class="chat-message system">
                        <div class="message-content">
                            <strong>Sistema:</strong> Chat inicializado com sucesso! Digite sua mensagem abaixo.
                        </div>
                        <div class="message-time">${this.getCurrentTime()}</div>
                    </div>
                </div>
                
                <div class="chat-input-area">
                    <div class="input-group">
                        <textarea 
                            id="chat-input" 
                            placeholder="Digite sua mensagem aqui..." 
                            rows="2"
                            maxlength="1000"
                        ></textarea>
                        <button id="chat-send-btn" class="btn-primary">
                            📤 Enviar
                        </button>
                    </div>
                    <div class="chat-status" id="chat-status">Pronto para conversar</div>
                </div>
            </div>
        `;

        contentArea.innerHTML = chatHTML;
        console.log('ChatStandalone: Interface criada com sucesso!');
    }

    /**
     * Configura os event listeners
     */
    setupEventListeners() {
        console.log('ChatStandalone: Configurando event listeners...');

        const sendBtn = document.getElementById('chat-send-btn');
        const clearBtn = document.getElementById('chat-clear-btn');
        const chatInput = document.getElementById('chat-input');

        if (!sendBtn || !clearBtn || !chatInput) {
            throw new Error('Elementos da interface não encontrados');
        }

        // Botão enviar
        sendBtn.addEventListener('click', () => this.sendMessage());

        // Botão limpar
        clearBtn.addEventListener('click', () => this.clearChat());

        // Enter para enviar (Shift+Enter para nova linha)
        chatInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        console.log('ChatStandalone: Event listeners configurados!');
    }

    /**
     * Envia uma mensagem
     */
    async sendMessage() {
        console.log('ChatStandalone: Enviando mensagem...');

        const chatInput = document.getElementById('chat-input');
        const sendBtn = document.getElementById('chat-send-btn');
        const statusDiv = document.getElementById('chat-status');

        const message = chatInput.value.trim();
        if (!message) {
            console.log('ChatStandalone: Mensagem vazia, ignorando');
            return;
        }

        try {
            // Desabilitar interface durante envio
            chatInput.disabled = true;
            sendBtn.disabled = true;
            statusDiv.textContent = 'Enviando mensagem...';

            // Adicionar mensagem do usuário
            this.addMessage('user', message);
            chatInput.value = '';

            // Enviar para API
            const response = await this.callChatAPI(message);
            
            // Adicionar resposta da IA
            this.addMessage('assistant', response);
            
            statusDiv.textContent = 'Mensagem enviada com sucesso!';
            
        } catch (error) {
            console.error('ChatStandalone: Erro ao enviar mensagem:', error);
            this.addMessage('system', `Erro: ${error.message}`);
            statusDiv.textContent = 'Erro ao enviar mensagem';
        } finally {
            // Reabilitar interface
            chatInput.disabled = false;
            sendBtn.disabled = false;
            chatInput.focus();
            
            // Limpar status após 3 segundos
            setTimeout(() => {
                statusDiv.textContent = 'Pronto para conversar';
            }, 3000);
        }
    }

    /**
     * Chama a API de chat
     */
    async callChatAPI(message) {
        console.log('ChatStandalone: Chamando API de chat...');

        const response = await fetch(this.apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                message: message,
                timestamp: new Date().toISOString()
            })
        });

        if (!response.ok) {
            throw new Error(`Erro na API: ${response.status} - ${response.statusText}`);
        }

        const data = await response.json();
        return data.response || 'Resposta vazia da IA';
    }

    /**
     * Adiciona uma mensagem ao chat
     */
    addMessage(type, content) {
        console.log(`ChatStandalone: Adicionando mensagem ${type}:`, content);

        const messagesContainer = document.getElementById('chat-messages');
        if (!messagesContainer) {
            console.error('ChatStandalone: Container de mensagens não encontrado');
            return;
        }

        const messageDiv = document.createElement('div');
        messageDiv.className = `chat-message ${type}`;

        const icon = type === 'user' ? '👤' : type === 'assistant' ? '🤖' : '⚙️';
        const label = type === 'user' ? 'Você' : type === 'assistant' ? 'IA' : 'Sistema';

        messageDiv.innerHTML = `
            <div class="message-content">
                <strong>${icon} ${label}:</strong> ${this.escapeHtml(content)}
            </div>
            <div class="message-time">${this.getCurrentTime()}</div>
        `;

        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    /**
     * Limpa o chat
     */
    clearChat() {
        console.log('ChatStandalone: Limpando chat...');

        const messagesContainer = document.getElementById('chat-messages');
        if (messagesContainer) {
            messagesContainer.innerHTML = `
                <div class="chat-message system">
                    <div class="message-content">
                        <strong>Sistema:</strong> Chat limpo. Pronto para nova conversa!
                    </div>
                    <div class="message-time">${this.getCurrentTime()}</div>
                </div>
            `;
        }
    }

    /**
     * Obtém o horário atual formatado
     */
    getCurrentTime() {
        return new Date().toLocaleTimeString('pt-BR', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }

    /**
     * Escapa HTML para segurança
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Instância global do chat
window.chatStandalone = new ChatStandalone();

console.log('ChatStandalone: Script carregado com sucesso!');
