/**
 * M10 AI Orchestrator Monitor - JavaScript Framework
 * Handles navigation, WebSocket connections, theme management, and UI interactions
 */

class M10Monitor {
    constructor() {
        this.currentSection = 'dashboard';
        this.websocket = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
        this.theme = localStorage.getItem('m10-theme') || 'light';
        this.lastUpdateTime = null;
        this.updateInterval = null;
        
        this.init();
    }

    /**
     * Initialize the application
     */
    init() {
        this.setupEventListeners();
        this.setupTheme();
        this.setupNavigation();
        this.setupWebSocket();
        this.setupAutoUpdate();
        this.showLoading(false);

        // Initialize system status
        this.initializeSystemStatus();

        console.log('M10 AI Orchestrator Monitor initialized');
    }

    /**
     * Initialize system status on startup
     */
    async initializeSystemStatus() {
        try {
            // Get initial system status from services API
            const response = await fetch('/monitor/api/services');
            const data = await response.json();

            // Calculate overall system health
            const services = data.services || {};
            const serviceStatuses = Object.values(services);

            let healthyCount = 0;
            let unhealthyCount = 0;
            let degradedCount = 0;

            serviceStatuses.forEach(service => {
                switch (service.status) {
                    case 'healthy':
                        healthyCount++;
                        break;
                    case 'unhealthy':
                        unhealthyCount++;
                        break;
                    case 'degraded':
                        degradedCount++;
                        break;
                }
            });

            // Determine overall system status
            let systemHealth, systemMessage;
            if (unhealthyCount > serviceStatuses.length / 2) {
                systemHealth = 'critical';
                systemMessage = `Sistema Crítico (${unhealthyCount} serviços indisponíveis)`;
            } else if (unhealthyCount > 0 || degradedCount > 0) {
                systemHealth = 'warning';
                systemMessage = `Sistema Operacional com Alertas (${unhealthyCount} falhas, ${degradedCount} degradados)`;
            } else {
                systemHealth = 'healthy';
                systemMessage = `Sistema Operacional (${healthyCount} serviços ativos)`;
            }

            // Update the status indicator
            this.updateSystemStatus({
                health: systemHealth,
                message: systemMessage
            });

        } catch (error) {
            console.error('Error initializing system status:', error);
            this.updateSystemStatus({
                health: 'error',
                message: 'Erro ao verificar status do sistema'
            });
        }
    }

    /**
     * Set up all event listeners
     */
    setupEventListeners() {
        // Navigation links
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const section = link.dataset.section;
                if (section) {
                    this.navigateToSection(section);
                }
            });
        });

        // Mobile menu toggle
        const menuToggle = document.querySelector('.menu-toggle');
        if (menuToggle) {
            menuToggle.addEventListener('click', () => {
                this.toggleMobileMenu();
            });
        }

        // Theme toggle
        const themeToggle = document.querySelector('.theme-toggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => {
                this.toggleTheme();
            });
        }

        // Action buttons
        document.querySelectorAll('.action-button').forEach(button => {
            button.addEventListener('click', (e) => {
                this.handleActionButton(e.target.closest('.action-button'));
            });
        });

        // Modal handling
        this.setupModalHandlers();

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });

        // Window resize handling
        window.addEventListener('resize', () => {
            this.handleWindowResize();
        });

        // Page visibility change
        document.addEventListener('visibilitychange', () => {
            this.handleVisibilityChange();
        });
    }

    /**
     * Set up theme management
     */
    setupTheme() {
        document.documentElement.setAttribute('data-theme', this.theme);
        this.updateThemeIcon();
    }

    /**
     * Toggle between light and dark themes
     */
    toggleTheme() {
        this.theme = this.theme === 'light' ? 'dark' : 'light';
        document.documentElement.setAttribute('data-theme', this.theme);
        localStorage.setItem('m10-theme', this.theme);
        this.updateThemeIcon();
        
        this.showNotification('Tema alterado para ' + (this.theme === 'light' ? 'claro' : 'escuro'), 'info');
    }

    /**
     * Update theme toggle icon
     */
    updateThemeIcon() {
        const themeToggle = document.querySelector('.theme-toggle .icon');
        if (themeToggle) {
            themeToggle.textContent = this.theme === 'light' ? '🌙' : '☀️';
        }
    }

    /**
     * Set up navigation system
     */
    setupNavigation() {
        // Get current section from URL hash or default to dashboard
        const hash = window.location.hash.slice(1);
        const initialSection = hash || 'dashboard';
        this.navigateToSection(initialSection);

        // Handle browser back/forward buttons
        window.addEventListener('popstate', (e) => {
            const section = window.location.hash.slice(1) || 'dashboard';
            this.navigateToSection(section, false);
        });
    }

    /**
     * Navigate to a specific section
     * @param {string} section - Section to navigate to
     * @param {boolean} updateHistory - Whether to update browser history
     */
    navigateToSection(section, updateHistory = true) {
        // Validate section exists
        const sectionElement = document.getElementById(`${section}-section`);
        if (!sectionElement) {
            console.warn(`Section '${section}' not found`);
            return;
        }

        // Update current section
        this.currentSection = section;

        // Hide all sections
        document.querySelectorAll('.content-section').forEach(el => {
            el.classList.remove('active');
            el.setAttribute('hidden', '');
        });

        // Show target section
        sectionElement.classList.add('active');
        sectionElement.removeAttribute('hidden');

        // Update navigation links
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
            link.removeAttribute('aria-current');
        });

        const activeLink = document.querySelector(`[data-section="${section}"]`);
        if (activeLink) {
            activeLink.classList.add('active');
            activeLink.setAttribute('aria-current', 'page');
        }

        // Update browser history and URL
        if (updateHistory) {
            window.history.pushState({ section }, '', `#${section}`);
        }

        // Update page title
        this.updatePageTitle(section);

        // Load section-specific content
        this.loadSectionContent(section);

        // Close mobile menu if open
        this.closeMobileMenu();
    }

    /**
     * Update page title based on current section
     * @param {string} section - Current section
     */
    updatePageTitle(section) {
        const sectionTitles = {
            dashboard: 'Dashboard',
            services: 'Serviços',
            metrics: 'Métricas',
            alerts: 'Alertas',
            chat: 'Chat de Teste',
            debug: 'Debug Tools',
            settings: 'Configurações'
        };

        const title = sectionTitles[section] || 'Monitor';
        document.title = `${title} - M10 AI Orchestrator Monitor`;
    }

    /**
     * Load content for specific section
     * @param {string} section - Section to load content for
     */
    async loadSectionContent(section) {
        switch (section) {
            case 'dashboard':
                await this.loadDashboardContent();
                break;
            case 'services':
                await this.loadServicesContent();
                break;
            case 'metrics':
                await this.loadMetricsContent();
                break;
            case 'alerts':
                await this.loadAlertsContent();
                break;
            case 'chat':
                console.log('=== CALLING NEW CHAT SYSTEM ===');
                this.loadNewChatContent();
                break;
            case 'debug':
                await this.loadDebugContent();
                break;
            case 'settings':
                await this.loadSettingsContent();
                break;
        }
    }

    /**
     * Toggle mobile menu
     */
    toggleMobileMenu() {
        const sidebar = document.querySelector('.sidebar');
        const menuToggle = document.querySelector('.menu-toggle');
        
        if (sidebar && menuToggle) {
            const isOpen = sidebar.classList.contains('open');
            
            if (isOpen) {
                this.closeMobileMenu();
            } else {
                this.openMobileMenu();
            }
        }
    }

    /**
     * Open mobile menu
     */
    openMobileMenu() {
        const sidebar = document.querySelector('.sidebar');
        const menuToggle = document.querySelector('.menu-toggle');
        
        if (sidebar && menuToggle) {
            sidebar.classList.add('open');
            menuToggle.setAttribute('aria-expanded', 'true');
            
            // Add overlay click handler
            this.addMobileMenuOverlay();
        }
    }

    /**
     * Close mobile menu
     */
    closeMobileMenu() {
        const sidebar = document.querySelector('.sidebar');
        const menuToggle = document.querySelector('.menu-toggle');
        
        if (sidebar && menuToggle) {
            sidebar.classList.remove('open');
            menuToggle.setAttribute('aria-expanded', 'false');
            
            // Remove overlay
            this.removeMobileMenuOverlay();
        }
    }

    /**
     * Add mobile menu overlay
     */
    addMobileMenuOverlay() {
        let overlay = document.querySelector('.mobile-menu-overlay');
        if (!overlay) {
            overlay = document.createElement('div');
            overlay.className = 'mobile-menu-overlay';
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.5);
                z-index: 40;
            `;
            overlay.addEventListener('click', () => this.closeMobileMenu());
            document.body.appendChild(overlay);
        }
    }

    /**
     * Remove mobile menu overlay
     */
    removeMobileMenuOverlay() {
        const overlay = document.querySelector('.mobile-menu-overlay');
        if (overlay) {
            overlay.remove();
        }
    }

    /**
     * Set up WebSocket connection
     */
    setupWebSocket() {
        this.connectWebSocket();
    }

    /**
     * Connect to WebSocket
     */
    connectWebSocket() {
        try {
            // Determine WebSocket URL
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const host = window.location.host;
            const wsUrl = `${protocol}//${host}/monitor/ws`; // Corrigido o caminho do WebSocket

            this.websocket = new WebSocket(wsUrl);

            this.websocket.onopen = () => {
                console.log('WebSocket connected');
                this.reconnectAttempts = 0;
                this.updateConnectionStatus(true);
            };

            this.websocket.onmessage = (event) => {
                this.handleWebSocketMessage(event);
            };

            this.websocket.onclose = () => {
                console.log('WebSocket disconnected');
                this.updateConnectionStatus(false);
                this.scheduleReconnect();
            };

            this.websocket.onerror = (error) => {
                console.error('WebSocket error:', error);
                this.updateConnectionStatus(false);
            };

        } catch (error) {
            console.error('Failed to create WebSocket connection:', error);
            this.updateConnectionStatus(false);
            this.scheduleReconnect();
        }
    }

    /**
     * Handle WebSocket message
     * @param {MessageEvent} event - WebSocket message event
     */
    handleWebSocketMessage(event) {
        try {
            const data = JSON.parse(event.data);
            
            switch (data.type) {
                case 'system_status':
                    this.updateSystemStatus(data.payload);
                    break;
                case 'metrics_update':
                    this.updateMetrics(data.payload);
                    break;
                case 'alert':
                    this.handleAlert(data.payload);
                    break;
                case 'notification':
                    this.showNotification(data.payload.message, data.payload.type);
                    break;
                default:
                    console.log('Unknown WebSocket message type:', data.type);
            }
        } catch (error) {
            console.error('Error parsing WebSocket message:', error);
        }
    }

    /**
     * Schedule WebSocket reconnection
     */
    scheduleReconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
            
            console.log(`Scheduling WebSocket reconnect attempt ${this.reconnectAttempts} in ${delay}ms`);
            
            setTimeout(() => {
                this.connectWebSocket();
            }, delay);
        } else {
            console.error('Max WebSocket reconnect attempts reached');
        }
    }

    /**
     * Update connection status indicator
     * @param {boolean} connected - Whether connected
     */
    updateConnectionStatus(connected) {
        const connectionDot = document.querySelector('.connection-dot');
        const connectionText = document.querySelector('.connection-text');
        
        if (connectionDot) {
            connectionDot.setAttribute('data-status', connected ? 'connected' : 'disconnected');
        }
        
        if (connectionText) {
            connectionText.textContent = connected ? 'WebSocket Conectado' : 'WebSocket Desconectado';
        }
    }

    /**
     * Update system status
     * @param {Object} status - System status data
     */
    updateSystemStatus(status) {
        const statusIndicator = document.getElementById('system-status');
        const statusText = document.getElementById('status-text');
        
        if (statusIndicator) {
            statusIndicator.setAttribute('data-status', status.health);
        }
        
        if (statusText) {
            statusText.textContent = status.message;
        }

        // Adicionado para processar system_health se presente na mensagem inicial
        if (status.system_health) {
            this.updateSystemMetrics(status.system_health);
        }

        // Adicionado para processar services se presente na mensagem inicial
        // Isso pode popular a lista de serviços mais rapidamente.
        // A função loadServicesStatus() que faz fetch ainda pode ser mantida para atualizações periódicas ou como fallback.
        if (status.services) {
            this.updateServicesDisplay({ services: status.services });
        }
    }

    /**
     * Set up auto-update functionality
     */
    setupAutoUpdate() {
        this.updateLastUpdateTime();
        
        // Update every 30 seconds
        this.updateInterval = setInterval(() => {
            this.updateLastUpdateTime();
        }, 30000);
    }

    /**
     * Update last update time display
     */
    updateLastUpdateTime() {
        const lastUpdateElement = document.getElementById('last-update-time');
        if (lastUpdateElement) {
            const now = new Date();
            const timeString = now.toLocaleTimeString('pt-BR', {
                hour: '2-digit',
                minute: '2-digit'
            });
            lastUpdateElement.textContent = timeString;
            lastUpdateElement.setAttribute('datetime', now.toISOString());
        }
        this.lastUpdateTime = Date.now();
    }

    /**
     * Handle action button clicks
     * @param {HTMLElement} button - Button that was clicked
     */
    handleActionButton(button) {
        if (!button) return;

        const buttonClasses = button.classList;
        
        if (buttonClasses.contains('refresh-button')) {
            this.refreshCurrentSection();
        } else if (buttonClasses.contains('export-button')) {
            this.exportCurrentSection();
        } else {
            // Handle other action buttons based on their context
            const section = button.closest('.content-section');
            if (section && section.id === 'chat-section') {
                // This is the "Nova Conversa" button in chat section
                this.handleNewConversation();
            } else {
                console.log('Action button clicked:', button);
            }
        }
    }

    /**
     * Handle new conversation button in chat section
     */
    handleNewConversation() {
        console.log('Nova conversa iniciada');

        // Clear existing chat messages
        const chatMessages = document.getElementById('chat-messages');
        if (chatMessages) {
            chatMessages.innerHTML = `
                <div class="chat-message system">
                    <div class="message-content">
                        <strong>Sistema:</strong> Nova conversa iniciada. Digite sua mensagem abaixo.
                    </div>
                    <div class="message-time">${new Date().toLocaleTimeString('pt-BR')}</div>
                </div>
            `;
        }

        // Clear input field
        const chatInput = document.getElementById('chat-input');
        if (chatInput) {
            chatInput.value = '';
            chatInput.focus();
        }

        // Clear status
        const chatStatus = document.getElementById('chat-status');
        if (chatStatus) {
            chatStatus.textContent = '';
        }

        this.showNotification('Nova conversa iniciada', 'success');
    }

    /**
     * Refresh current section content
     */
    async refreshCurrentSection() {
        this.showLoading(true);
        try {
            await this.loadSectionContent(this.currentSection);
            this.updateLastUpdateTime();
            this.showNotification('Conteúdo atualizado com sucesso', 'success');
        } catch (error) {
            console.error('Error refreshing section:', error);
            this.showNotification('Erro ao atualizar conteúdo', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * Export current section data
     */
    exportCurrentSection() {
        // Placeholder for export functionality
        this.showNotification('Funcionalidade de exportação será implementada', 'info');
    }

    /**
     * Set up modal handlers
     */
    setupModalHandlers() {
        // Modal close buttons
        document.querySelectorAll('.modal-close').forEach(button => {
            button.addEventListener('click', () => {
                this.closeModal(button.closest('.modal-overlay'));
            });
        });

        // Modal overlay clicks
        document.querySelectorAll('.modal-overlay').forEach(overlay => {
            overlay.addEventListener('click', (e) => {
                if (e.target === overlay) {
                    this.closeModal(overlay);
                }
            });
        });

        // Modal action buttons
        document.querySelectorAll('[data-action]').forEach(button => {
            button.addEventListener('click', (e) => {
                this.handleModalAction(e.target.dataset.action, e.target.closest('.modal-overlay'));
            });
        });
    }

    /**
     * Show modal
     * @param {string} modalId - Modal ID
     * @param {Object} data - Optional data for modal
     */
    showModal(modalId, data = {}) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.setAttribute('aria-hidden', 'false');
            
            // Focus trap
            const focusableElements = modal.querySelectorAll(
                'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
            );
            if (focusableElements.length > 0) {
                focusableElements[0].focus();
            }
        }
    }

    /**
     * Close modal
     * @param {HTMLElement} modal - Modal element
     */
    closeModal(modal) {
        if (modal) {
            modal.setAttribute('aria-hidden', 'true');
        }
    }

    /**
     * Handle modal actions
     * @param {string} action - Action to handle
     * @param {HTMLElement} modal - Modal element
     */
    handleModalAction(action, modal) {
        switch (action) {
            case 'close':
                this.closeModal(modal);
                break;
            case 'retry':
                this.closeModal(modal);
                this.refreshCurrentSection();
                break;
            default:
                console.log('Unknown modal action:', action);
        }
    }

    /**
     * Show loading overlay
     * @param {boolean} show - Whether to show loading
     */
    showLoading(show) {
        const loadingOverlay = document.getElementById('loading-overlay');
        if (loadingOverlay) {
            loadingOverlay.setAttribute('aria-hidden', show ? 'false' : 'true');
        }
    }

    /**
     * Show notification
     * @param {string} message - Notification message
     * @param {string} type - Notification type (success, error, warning, info)
     */
    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.setAttribute('role', 'alert');
        notification.setAttribute('aria-live', 'polite');
        
        notification.innerHTML = `
            <span class="notification-message">${message}</span>
            <button class="notification-close" aria-label="Fechar notificação">&times;</button>
        `;

        // Add styles
        notification.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 16px;
            box-shadow: var(--shadow-lg);
            z-index: 1200;
            max-width: 400px;
            animation: slideInRight 0.3s ease-out;
        `;

        // Add to document
        document.body.appendChild(notification);

        // Close button handler
        notification.querySelector('.notification-close').addEventListener('click', () => {
            notification.remove();
        });

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.style.animation = 'slideOutRight 0.3s ease-in';
                setTimeout(() => notification.remove(), 300);
            }
        }, 5000);
    }

    /**
     * Handle keyboard shortcuts
     * @param {KeyboardEvent} e - Keyboard event
     */
    handleKeyboardShortcuts(e) {
        // Escape key closes modals and mobile menu
        if (e.key === 'Escape') {
            // Close mobile menu
            this.closeMobileMenu();
            
            // Close any open modals
            document.querySelectorAll('.modal-overlay[aria-hidden="false"]').forEach(modal => {
                this.closeModal(modal);
            });
        }

        // Ctrl/Cmd + R to refresh
        if ((e.ctrlKey || e.metaKey) && e.key === 'r' && !e.shiftKey) {
            e.preventDefault();
            this.refreshCurrentSection();
        }

        // Alt + number keys for navigation
        if (e.altKey && e.key >= '1' && e.key <= '8') {
            e.preventDefault();
            const sections = ['dashboard', 'services', 'metrics', 'alerts', 'chat', 'debug', 'settings'];
            const sectionIndex = parseInt(e.key) - 1;
            if (sections[sectionIndex]) {
                this.navigateToSection(sections[sectionIndex]);
            }
        }
    }

    /**
     * Handle window resize
     */
    handleWindowResize() {
        // Close mobile menu on desktop resize
        if (window.innerWidth > 768) {
            this.closeMobileMenu();
        }
    }

    /**
     * Handle page visibility change
     */
    handleVisibilityChange() {
        if (document.hidden) {
            // Page is hidden, reduce activity
            console.log('Page hidden, reducing activity');
        } else {
            // Page is visible, resume normal activity
            console.log('Page visible, resuming normal activity');
            this.refreshCurrentSection();
        }
    }

    // Section-specific content loaders

    async loadDashboardContent() {
        console.log('Loading dashboard content...');
        
        try {
            // Initialize charts and widgets
            this.initializeSystemMetricsChart();
            this.initializeGauges();
            this.loadServicesStatus();
            this.loadAIMetrics();
            this.loadRecentActivity();
            
            // Request initial data if WebSocket is connected
            if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
                this.websocket.send(JSON.stringify({
                    type: 'request_dashboard_data'
                }));
            }
        } catch (error) {
            console.error('Error loading dashboard content:', error);
        }
    }

    async loadServicesContent() {
        console.log('Loading services content...');
        const servicesSection = document.getElementById('services-section');
        const contentArea = servicesSection.querySelector('.section-content');
        if (!contentArea) return;

        contentArea.innerHTML = '<div class="service-loading">Carregando status dos serviços...</div>';

        try {
            const response = await fetch('/monitor/api/services');
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const data = await response.json();

            if (data.services && Object.keys(data.services).length > 0) {
                let servicesHTML = '<div class="services-grid">'; // Usar um grid para melhor layout
                Object.entries(data.services).forEach(([serviceName, details]) => {
                    servicesHTML += `
                        <div class="service-card ${details.status || 'unknown'}">
                            <div class="service-card-header">
                                <span class="service-card-icon">🔧</span>
                                <h3 class="service-card-title">${this.formatServiceName(serviceName)}</h3>
                                <span class="service-card-status ${details.status || 'unknown'}">${this.formatServiceStatus(details.status || 'unknown')}</span>
                            </div>
                            <div class="service-card-body">
                                <p><strong>Endpoint:</strong> ${details.endpoint || 'N/A'}</p>
                                <p><strong>Tempo de Resposta:</strong> ${details.response_time_ms !== null && details.response_time_ms !== undefined ? details.response_time_ms + 'ms' : 'N/A'}</p>
                                <p><strong>Última Verificação:</strong> ${details.last_check ? new Date(details.last_check).toLocaleString('pt-BR') : 'N/A'}</p>
                                <p><strong>Versão:</strong> ${details.version || 'N/A'}</p>
                                <p><strong>Uptime:</strong> ${details.uptime || 'N/A'}</p>
                                ${details.details ? `<p><strong>Detalhes:</strong> ${details.details}</p>` : ''}
                                ${details.error ? `<p class="service-error"><strong>Erro:</strong> ${details.error}</p>` : ''}
                            </div>
                        </div>
                    `;
                });
                servicesHTML += '</div>';
                contentArea.innerHTML = servicesHTML;
            } else {
                contentArea.innerHTML = '<div class="services-placeholder"><p>Nenhum serviço encontrado ou dados indisponíveis.</p></div>';
            }
        } catch (error) {
            console.error('Error loading services content:', error);
            contentArea.innerHTML = '<div class="services-placeholder"><p>Erro ao carregar o status dos serviços. Tente novamente mais tarde.</p></div>';
            this.showNotification('Erro ao carregar status dos serviços', 'error');
        }
    }

    async loadMetricsContent() {
        console.log('Loading metrics content...');
        const metricsSection = document.getElementById('metrics-section');
        const contentArea = metricsSection.querySelector('.section-content');
        if (!contentArea) return;

        contentArea.innerHTML = '<div class="metrics-loading">Carregando métricas detalhadas...</div>';

        try {
            const response = await fetch('/monitor/api/metrics');
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const data = await response.json();

            let metricsHTML = '<div class="metrics-grid">';

            // System Health Metrics
            if (data.system_health && !data.system_health.error) {
                metricsHTML += `
                    <div class="metric-group">
                        <h3 class="metric-group-title">Métricas do Sistema</h3>
                        <div class="metric-item"><strong>CPU Usage:</strong> ${data.system_health.cpu_usage?.toFixed(1) || 'N/A'}%</div>
                        <div class="metric-item"><strong>Memory Usage:</strong> ${data.system_health.memory_usage?.toFixed(1) || 'N/A'}%</div>
                        <div class="metric-item"><strong>Disk Usage:</strong> ${data.system_health.disk_usage?.toFixed(1) || 'N/A'}%</div>
                        <div class="metric-item"><strong>Timestamp:</strong> ${new Date(data.system_health.timestamp).toLocaleString('pt-BR')}</div>
                    </div>
                `;
            } else if (data.system_health && data.system_health.error) {
                 metricsHTML += `<div class="metric-group"><h3 class="metric-group-title">Métricas do Sistema</h3><p class="error">Erro ao carregar: ${data.system_health.error}</p></div>`;
            }


            // AI Metrics
            if (data.ai_metrics && !data.ai_metrics.error) {
                metricsHTML += `
                    <div class="metric-group">
                        <h3 class="metric-group-title">Métricas de IA</h3>
                        <div class="metric-item"><strong>Total de Conversas:</strong> ${this.formatNumber(data.ai_metrics.total_conversations)}</div>
                        <div class="metric-item"><strong>Conversas Ativas:</strong> ${this.formatNumber(data.ai_metrics.active_conversations)}</div>
                        <div class="metric-item"><strong>Tempo Médio de Resposta:</strong> ${data.ai_metrics.avg_response_time_s || '0.0'}s</div>
                        <div class="metric-item"><strong>Total de Tokens Usados Hoje:</strong> ${this.formatNumber(data.ai_metrics.total_tokens_used_today || 0)}</div>
                        <div class="metric-item"><strong>Chamadas de API Hoje:</strong> ${this.formatNumber(data.ai_metrics.api_calls_today)}</div>
                        <div class="metric-item"><strong>Taxa de Erro Hoje:</strong> ${((data.ai_metrics.error_rate_today || 0) * 100).toFixed(2)}%</div>
                        <div class="metric-item"><strong>Timestamp:</strong> ${new Date(data.ai_metrics.timestamp).toLocaleString('pt-BR')}</div>
                    </div>
                `;
            } else if (data.ai_metrics && data.ai_metrics.error) {
                metricsHTML += `<div class="metric-group"><h3 class="metric-group-title">Métricas de IA</h3><p class="error">Erro ao carregar: ${data.ai_metrics.error}</p></div>`;
            }

            // Service Status (resumo)
            if (data.service_status && Object.keys(data.service_status).length > 0) {
                metricsHTML += '<div class="metric-group"><h3 class="metric-group-title">Status Resumido dos Serviços</h3>';
                Object.entries(data.service_status).forEach(([serviceName, details]) => {
                    metricsHTML += `
                        <div class="metric-item">
                            <strong>${this.formatServiceName(serviceName)}:</strong> 
                            <span class="status-badge ${details.status || 'unknown'}">${this.formatServiceStatus(details.status || 'unknown')}</span>
                            (RT: ${details.response_time_ms !== null && details.response_time_ms !== undefined ? details.response_time_ms + 'ms' : 'N/A'})
                        </div>`;
                });
                metricsHTML += '</div>';
            }

            metricsHTML += '</div>'; // Fim do metrics-grid
            contentArea.innerHTML = metricsHTML;

        } catch (error) {
            console.error('Error loading metrics content:', error);
            contentArea.innerHTML = '<div class="metrics-placeholder"><p>Erro ao carregar as métricas detalhadas. Tente novamente mais tarde.</p></div>';
            this.showNotification('Erro ao carregar métricas detalhadas', 'error');
        }
    }

    async loadAlertsContent() {
        console.log('Loading alerts content...');
        const alertsSection = document.getElementById('alerts-section');
        const contentArea = alertsSection.querySelector('.section-content');
        if (!contentArea) return;

        try {
            // Get current service status to generate alerts
            const response = await fetch('/monitor/api/services');
            const data = await response.json();

            let alertsHTML = `
                <div class="alerts-container">
                    <div class="alerts-header">
                        <h2>🚨 Alertas do Sistema</h2>
                        <p>Status dos serviços e alertas ativos</p>
                    </div>
                    <div class="alerts-content">
            `;

            // Generate alerts based on service status
            const alerts = this.generateAlerts(data.services);

            if (alerts.length === 0) {
                alertsHTML += `
                    <div class="alert-item success">
                        <div class="alert-icon">✅</div>
                        <div class="alert-content">
                            <h3>Sistema Operacional</h3>
                            <p>Nenhum alerta crítico detectado no momento.</p>
                            <small>Última verificação: ${new Date().toLocaleString('pt-BR')}</small>
                        </div>
                    </div>
                `;
            } else {
                alerts.forEach(alert => {
                    alertsHTML += `
                        <div class="alert-item ${alert.severity}">
                            <div class="alert-icon">${alert.icon}</div>
                            <div class="alert-content">
                                <h3>${alert.title}</h3>
                                <p>${alert.message}</p>
                                <small>Serviço: ${alert.service} | ${alert.timestamp}</small>
                            </div>
                        </div>
                    `;
                });
            }

            alertsHTML += `
                    </div>
                </div>
            `;

            contentArea.innerHTML = alertsHTML;

        } catch (error) {
            console.error('Error loading alerts:', error);
            contentArea.innerHTML = `
                <div class="alerts-container">
                    <div class="alerts-header">
                        <h2>🚨 Alertas do Sistema</h2>
                        <p>Erro ao carregar alertas</p>
                    </div>
                    <div class="alert-item error">
                        <div class="alert-icon">❌</div>
                        <div class="alert-content">
                            <h3>Erro de Conexão</h3>
                            <p>Não foi possível carregar os alertas: ${error.message}</p>
                        </div>
                    </div>
                </div>
            `;
        }
    }

    generateAlerts(services) {
        const alerts = [];
        const now = new Date().toLocaleString('pt-BR');

        if (!services) return alerts;

        Object.entries(services).forEach(([serviceName, details]) => {
            if (details.status === 'unhealthy') {
                alerts.push({
                    severity: 'error',
                    icon: '🔴',
                    title: `Serviço Indisponível: ${this.formatServiceName(serviceName)}`,
                    message: details.error || 'Serviço não está respondendo',
                    service: serviceName,
                    timestamp: now
                });
            } else if (details.status === 'degraded') {
                alerts.push({
                    severity: 'warning',
                    icon: '🟡',
                    title: `Serviço Degradado: ${this.formatServiceName(serviceName)}`,
                    message: details.details || 'Serviço operando com limitações',
                    service: serviceName,
                    timestamp: now
                });
            } else if (details.response_time_ms && details.response_time_ms > 1000) {
                alerts.push({
                    severity: 'warning',
                    icon: '⚠️',
                    title: `Latência Alta: ${this.formatServiceName(serviceName)}`,
                    message: `Tempo de resposta: ${details.response_time_ms}ms (>1s)`,
                    service: serviceName,
                    timestamp: now
                });
            }
        });

        return alerts;
    }

    /**
     * Carrega o novo sistema de chat standalone
     */
    loadNewChatContent() {
        console.log('Monitor: Carregando novo sistema de chat... [VERSÃO ATUALIZADA 2025-06-26 20:21]');

        // Função para tentar inicializar o chat com retry
        const tryInitializeChat = (attempts = 0) => {
            const maxAttempts = 10;

            if (attempts >= maxAttempts) {
                console.error('Monitor: ChatRAG não encontrado após múltiplas tentativas!');
                this.showChatError('Sistema de chat não carregado. Recarregue a página.');
                return;
            }

            if (typeof window.chatRAG === 'undefined') {
                console.log(`Monitor: Aguardando ChatRAG... tentativa ${attempts + 1}/${maxAttempts}`);
                setTimeout(() => tryInitializeChat(attempts + 1), 100);
                return;
            }

            try {
                // ChatRAG encontrado, inicializar
                console.log('Monitor: ChatRAG encontrado, inicializando...');
                this.clearChatError(); // Limpar qualquer erro anterior
                window.chatRAG.init();
                console.log('Monitor: Novo sistema de chat carregado com sucesso!');
            } catch (error) {
                console.error('Monitor: Erro ao carregar chat:', error);
                this.showChatError(`Erro ao inicializar chat: ${error.message}`);
            }
        };

        // Iniciar tentativas
        tryInitializeChat();
    }

    /**
     * Limpa mensagem de erro do chat
     */
    clearChatError() {
        const chatSection = document.getElementById('chat-section');
        if (!chatSection) return;

        // Verificar se há uma mensagem de erro
        const errorDiv = chatSection.querySelector('div[style*="color: #dc3545"]');
        if (errorDiv) {
            console.log('Monitor: Limpando mensagem de erro do chat...');
            errorDiv.remove();
        }
    }

    /**
     * Mostra mensagem de erro no chat
     */
    showChatError(message) {
        const chatSection = document.getElementById('chat-section');
        if (!chatSection) return;

        const contentArea = chatSection.querySelector('.section-content');
        if (!contentArea) return;

        contentArea.innerHTML = `
            <div style="padding: 2rem; text-align: center; color: #dc3545;">
                <h3>❌ Erro no Chat</h3>
                <p>${message}</p>
                <button onclick="location.reload()" style="padding: 0.5rem 1rem; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    🔄 Recarregar Página
                </button>
            </div>
        `;
    }

    // Função antiga removida - usando novo sistema de chat standalone

    // Funções antigas do chat removidas - usando novo sistema standalone

    async loadDebugContent() {
        console.log('Loading debug content...');
        // Implementation will be added in future subtasks
    }

    async loadSettingsContent() {
        console.log('Loading settings content...');
        // Implementation will be added in future subtasks
    }

    /**
     * Initialize system metrics chart
     */
    initializeSystemMetricsChart() {
        const canvas = document.getElementById('system-metrics-chart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        
        // Create chart with Chart.js
        this.systemChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [
                    {
                        label: 'CPU (%)',
                        data: [],
                        borderColor: '#ef4444',
                        backgroundColor: 'rgba(239, 68, 68, 0.1)',
                        tension: 0.4,
                        fill: true
                    },
                    {
                        label: 'Memória (%)',
                        data: [],
                        borderColor: '#3b82f6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4,
                        fill: true
                    },
                    {
                        label: 'Disco (%)',
                        data: [],
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.4,
                        fill: true
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                    }
                },
                scales: {
                    x: {
                        type: 'time',
                        time: {
                            unit: 'minute',
                            displayFormats: {
                                minute: 'HH:mm'
                            }
                        },
                        title: {
                            display: true,
                            text: 'Tempo'
                        }
                    },
                    y: {
                        beginAtZero: true,
                        max: 100,
                        title: {
                            display: true,
                            text: 'Porcentagem (%)'
                        }
                    }
                },
                interaction: {
                    mode: 'nearest',
                    axis: 'x',
                    intersect: false
                }
            }
        });
    }

    /**
     * Initialize gauge charts for CPU, Memory, and Disk
     */
    initializeGauges() {
        this.initializeGauge('cpu-gauge', 'CPU', '#ef4444');
        this.initializeGauge('memory-gauge', 'Memória', '#3b82f6');
        this.initializeGauge('disk-gauge', 'Disco', '#10b981');
    }

    /**
     * Initialize individual gauge chart
     * @param {string} canvasId - Canvas element ID
     * @param {string} label - Gauge label
     * @param {string} color - Gauge color
     */
    initializeGauge(canvasId, label, color) {
        const canvas = document.getElementById(canvasId);
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        
        const gauge = new Chart(ctx, {
            type: 'doughnut',
            data: {
                datasets: [{
                    data: [0, 100],
                    backgroundColor: [color, '#e5e7eb'],
                    borderWidth: 0,
                    cutout: '70%'
                }]
            },
            options: {
                responsive: false,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        enabled: false
                    }
                },
                elements: {
                    arc: {
                        borderWidth: 0
                    }
                }
            }
        });

        // Store gauge reference
        if (!this.gauges) this.gauges = {};
        this.gauges[canvasId] = gauge;
    }

    /**
     * Load services status
     */
    async loadServicesStatus() {
        try {
            const response = await fetch('/monitor/api/services');
            const data = await response.json();
            
            this.updateServicesDisplay(data);
        } catch (error) {
            console.error('Error loading services status:', error);
            this.showNotification('Erro ao carregar status dos serviços', 'error');
        }
    }

    /**
     * Update services display
     * @param {Object} data - Services data
     */
    updateServicesDisplay(data) { // data pode ser { services: {...} } ou a resposta da API /api/services
        const servicesData = data.services || {}; // Garante que temos o objeto de serviços

        // Update summary stats
        const healthyCountEl = document.getElementById('services-healthy');
        const degradedCountEl = document.getElementById('services-degraded');
        const criticalCountEl = document.getElementById('services-critical');
        
        let calculatedHealthy = 0;
        let calculatedDegraded = 0;
        let calculatedCritical = 0;

        if (typeof servicesData === 'object' && servicesData !== null) {
            Object.values(servicesData).forEach(service => {
                if (service && typeof service.status === 'string') {
                    switch (service.status.toLowerCase()) {
                        case 'healthy':
                            calculatedHealthy++;
                            break;
                        case 'degraded':
                            calculatedDegraded++;
                            break;
                        case 'critical':
                        case 'unhealthy': // Tratar 'unhealthy' como crítico
                            calculatedCritical++;
                            break;
                    }
                }
            });
        }
        
        if (healthyCountEl) healthyCountEl.textContent = calculatedHealthy;
        if (degradedCountEl) degradedCountEl.textContent = calculatedDegraded;
        if (criticalCountEl) criticalCountEl.textContent = calculatedCritical;
        
        // Update services list
        const servicesList = document.getElementById('services-list');
        if (!servicesList) return;
        
        servicesList.innerHTML = ''; // Limpa a lista antes de adicionar novos itens
        
        if (typeof servicesData === 'object' && servicesData !== null && Object.keys(servicesData).length > 0) {
            Object.entries(servicesData).forEach(([serviceName, serviceDetails]) => {
                if (serviceDetails && typeof serviceDetails.status === 'string') { // Checagem adicional
                    const serviceItem = this.createServiceItem(serviceName, serviceDetails);
                    servicesList.appendChild(serviceItem);
                }
            });
        } else {
            servicesList.innerHTML = '<div class="service-loading">Nenhum serviço para exibir</div>';
        }
    }

    /**
     * Create service item element
     * @param {string} name - Service name
     * @param {Object} data - Service data
     * @returns {HTMLElement} Service item element
     */
    createServiceItem(name, data) {
        const item = document.createElement('div');
        item.className = `service-item ${data.status === 'healthy' ? '' : data.status}`;
        
        item.innerHTML = `
            <div class="service-info">
                <div class="service-name">${this.formatServiceName(name)}</div>
                <div class="service-status ${data.status}">${this.formatServiceStatus(data.status)}</div>
            </div>
            <div class="service-response-time">${data.response_time_ms !== null && data.response_time_ms !== undefined ? data.response_time_ms : 'N/A'}ms</div>
        `;
        
        return item;
    }

    /**
     * Format service name for display
     * @param {string} name - Service name
     * @returns {string} Formatted name
     */
    formatServiceName(name) {
        const nameMap = {
            'postgresql': 'PostgreSQL',
            'openai_api': 'OpenAI API',
            'm6_vector_indexer': 'M6 Vector Indexer',
            'm4_translator': 'M4 Translator',
            'm2_orchestrator': 'M2 Orchestrator'
        };
        
        return nameMap[name] || name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }

    /**
     * Format service status for display
     * @param {string} status - Service status
     * @returns {string} Formatted status
     */
    formatServiceStatus(status) {
        const statusMap = {
            'healthy': 'Saudável',
            'degraded': 'Degradado',
            'critical': 'Crítico'
        };
        
        return statusMap[status] || status;
    }

    /**
     * Load AI metrics
     */
    async loadAIMetrics() {
        try {
            const response = await fetch('/monitor/api/metrics');
            const data = await response.json();
            
            this.updateAIMetricsDisplay(data.ai_metrics);
        } catch (error) {
            console.error('Error loading AI metrics:', error);
            this.showNotification('Erro ao carregar métricas de IA', 'error');
        }
    }

    /**
     * Update AI metrics display
     * @param {Object} metrics - AI metrics data
     */
    updateAIMetricsDisplay(metrics) {
        if (!metrics) return;
        
        // Update AI stats
        const totalConversations = document.getElementById('total-conversations');
        const activeConversations = document.getElementById('active-conversations');
        const avgResponseTime = document.getElementById('avg-response-time');
        const apiCallsToday = document.getElementById('api-calls-today');
        
        if (totalConversations) totalConversations.textContent = this.formatNumber(metrics.total_conversations);
        if (activeConversations) activeConversations.textContent = metrics.active_conversations || 0;
        if (avgResponseTime) avgResponseTime.textContent = metrics.avg_response_time_s || '0.0';
        if (apiCallsToday) apiCallsToday.textContent = this.formatNumber(metrics.api_calls_today);
        
        // Update performance bars
        const errorRateBar = document.getElementById('error-rate-bar');
        const errorRateValue = document.getElementById('error-rate');
        const tokensUsageBar = document.getElementById('tokens-usage-bar');
        const tokensUsedValue = document.getElementById('tokens-used');
        
        if (errorRateBar && errorRateValue) {
            const errorRate = (metrics.error_rate_today || 0) * 100;
            errorRateBar.style.width = `${Math.min(errorRate, 100)}%`;
            errorRateValue.textContent = `${errorRate.toFixed(1)}%`;
            
            // Change color based on error rate
            if (errorRate > 5) {
                errorRateBar.style.background = '#ef4444'; // Red
            } else if (errorRate > 2) {
                errorRateBar.style.background = '#f59e0b'; // Yellow
            } else {
                errorRateBar.style.background = '#10b981'; // Green
            }
        }
        
        if (tokensUsageBar && tokensUsedValue) {
            tokensUsedValue.textContent = this.formatNumber(metrics.total_tokens_used_today || 0);
            // For tokens, we'll show a percentage based on some daily limit
            const dailyLimit = 1000000; // 1M tokens per day example
            const tokensPercentage = Math.min(((metrics.total_tokens_used_today || 0) / dailyLimit) * 100, 100);
            tokensUsageBar.style.width = `${tokensPercentage}%`;
        }
    }

    /**
     * Load recent activity
     */
    async loadRecentActivity() {
        const activityTimeline = document.getElementById('activity-timeline');
        if (!activityTimeline) return;

        activityTimeline.innerHTML = '<div class="activity-loading">Carregando atividades recentes...</div>';

        try {
            const response = await fetch('/monitor/api/audit/queries'); // Usar o endpoint real
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const data = await response.json();

            if (data.logs && data.logs.length > 0) {
                activityTimeline.innerHTML = ''; // Limpar o placeholder de carregamento
                data.logs.forEach(log => {
                    // Mapear os dados do log para o formato esperado por createActivityItem
                    const activity = {
                        time: new Date(log.timestamp),
                        icon: this.getIconForQueryType(log.query_type),
                        title: `Consulta ${log.query_type || 'Desconhecida'} (${log.response_status || 'N/A'})`,
                        description: `${log.query_text.substring(0, 100)}${log.query_text.length > 100 ? '...' : ''}`,
                        details: log // Manter todos os detalhes do log para possível expansão futura
                    };
                    const activityItem = this.createActivityItem(activity);
                    activityTimeline.appendChild(activityItem);
                });
            } else {
                activityTimeline.innerHTML = '<div class="activity-placeholder"><p>Nenhuma atividade recente encontrada.</p></div>';
            }
        } catch (error) {
            console.error('Error loading recent activity:', error);
            activityTimeline.innerHTML = '<div class="activity-placeholder"><p>Erro ao carregar atividades recentes. Tente novamente mais tarde.</p></div>';
            this.showNotification('Erro ao carregar atividades recentes', 'error');
        }
    }

    /**
     * Get icon based on query type
     * @param {string} queryType - Type of query
     * @returns {string} Icon emoji
     */
    getIconForQueryType(queryType) {
        switch (queryType) {
            case 'chat': return '💬';
            case 'search': return '🔍';
            case 'diff': return '🔄';
            case 'lookup': return 'ℹ️';
            case 'context_retrieval': return '📚';
            case 'intent_classification': return '🎯';
            default: return '⚙️';
        }
    }

    /**
     * Create activity item element
     * @param {Object} activity - Activity data
     * @returns {HTMLElement} Activity item element
     */
    createActivityItem(activity) {
        const item = document.createElement('div');
        item.className = 'activity-item';
        
        const timeString = activity.time.toLocaleTimeString('pt-BR', {
            hour: '2-digit',
            minute: '2-digit'
        });
        
        item.innerHTML = `
            <div class="activity-time">${timeString}</div>
            <div class="activity-icon">${activity.icon}</div>
            <div class="activity-content">
                <div class="activity-title">${activity.title}</div>
                <div class="activity-description">${activity.description}</div>
            </div>
        `;
        // Adicionar um event listener para mostrar mais detalhes se necessário
        item.addEventListener('click', () => {
            console.log('Activity details:', activity.details);
            // Poderia abrir um modal com mais informações aqui
            this.showNotification(`Detalhes da consulta: ${activity.details.query_id}`, 'info');
        });
        
        return item;
    }

    /**
     * Update metrics display
     * @param {Object} metrics - Metrics data
     */
    updateMetrics(metrics) {
        console.log('Updating metrics:', metrics);
        
        if (metrics.system_health) {
            this.updateSystemMetrics(metrics.system_health);
        }
        
        if (metrics.ai_metrics) {
            this.updateAIMetricsDisplay(metrics.ai_metrics);
        }
        
        if (metrics.service_status) {
            this.updateServicesDisplay({ services: metrics.service_status });
        }
    }

    /**
     * Update system metrics (CPU, Memory, Disk)
     * @param {Object} systemHealth - System health data
     */
    updateSystemMetrics(systemHealth) {
        if (!systemHealth) return;
        
        // Update metric values
        const cpuUsage = document.getElementById('cpu-usage');
        const memoryUsage = document.getElementById('memory-usage');
        const diskUsage = document.getElementById('disk-usage');
        
        if (cpuUsage) cpuUsage.textContent = `${systemHealth.cpu_usage?.toFixed(1) || 0}%`;
        if (memoryUsage) memoryUsage.textContent = `${systemHealth.memory_usage?.toFixed(1) || 0}%`;
        if (diskUsage) diskUsage.textContent = `${systemHealth.disk_usage?.toFixed(1) || 0}%`;
        
        // Update gauges
        if (this.gauges) {
            if (this.gauges['cpu-gauge']) {
                this.updateGauge('cpu-gauge', systemHealth.cpu_usage || 0);
            }
            if (this.gauges['memory-gauge']) {
                this.updateGauge('memory-gauge', systemHealth.memory_usage || 0);
            }
            if (this.gauges['disk-gauge']) {
                this.updateGauge('disk-gauge', systemHealth.disk_usage || 0);
            }
        }
        
        // Update system chart
        if (this.systemChart) {
            const now = new Date();
            const timeLabel = now.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' });
            
            // Add new data point
            this.systemChart.data.labels.push(now);
            this.systemChart.data.datasets[0].data.push(systemHealth.cpu_usage || 0);
            this.systemChart.data.datasets[1].data.push(systemHealth.memory_usage || 0);
            this.systemChart.data.datasets[2].data.push(systemHealth.disk_usage || 0);
            
            // Keep only last 20 data points
            if (this.systemChart.data.labels.length > 20) {
                this.systemChart.data.labels.shift();
                this.systemChart.data.datasets.forEach(dataset => {
                    dataset.data.shift();
                });
            }
            
            this.systemChart.update('none');
        }
        
        // Update trends
        this.updateMetricTrends(systemHealth);
    }

    /**
     * Update gauge chart value
     * @param {string} canvasId - Canvas element ID
     * @param {number} value - New value (0-100)
     */
    updateGauge(canvasId, value) {
        const gauge = this.gauges[canvasId];
        if (!gauge) return;
        
        const clampedValue = Math.max(0, Math.min(100, value));
        gauge.data.datasets[0].data = [clampedValue, 100 - clampedValue];
        gauge.update('none');
    }

    /**
     * Update metric trends
     * @param {Object} systemHealth - System health data
     */
    updateMetricTrends(systemHealth) {
        // This is a simplified trend calculation
        // In a real implementation, you'd compare with previous values
        const cpuTrend = document.getElementById('cpu-trend');
        const memoryTrend = document.getElementById('memory-trend');
        const diskTrend = document.getElementById('disk-trend');
        
        if (cpuTrend) {
            cpuTrend.className = 'metric-trend';
            if (systemHealth.cpu_usage > 80) {
                cpuTrend.classList.add('down');
                cpuTrend.textContent = 'Alto';
            } else if (systemHealth.cpu_usage > 50) {
                cpuTrend.classList.add('stable');
                cpuTrend.textContent = 'Estável';
            } else {
                cpuTrend.textContent = 'Normal';
            }
        }
        
        if (memoryTrend) {
            memoryTrend.className = 'metric-trend';
            if (systemHealth.memory_usage > 85) {
                memoryTrend.classList.add('down');
                memoryTrend.textContent = 'Alto';
            } else if (systemHealth.memory_usage > 60) {
                memoryTrend.classList.add('stable');
                memoryTrend.textContent = 'Estável';
            } else {
                memoryTrend.textContent = 'Normal';
            }
        }
        
        if (diskTrend) {
            diskTrend.className = 'metric-trend';
            if (systemHealth.disk_usage > 90) {
                diskTrend.classList.add('down');
                diskTrend.textContent = 'Alto';
            } else if (systemHealth.disk_usage > 70) {
                diskTrend.classList.add('stable');
                diskTrend.textContent = 'Estável';
            } else {
                diskTrend.textContent = 'Normal';
            }
        }
    }

    /**
     * Format number for display
     * @param {number} num - Number to format
     * @returns {string} Formatted number
     */
    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num?.toString() || '0';
    }

    /**
     * Handle alert
     * @param {Object} alert - Alert data
     */
    handleAlert(alert) {
        console.log('Handling alert:', alert);
        this.showNotification(alert.message, alert.severity || 'warning');
        
        // Update alert badge
        const alertsBadge = document.getElementById('alerts-badge');
        if (alertsBadge) {
            const currentCount = parseInt(alertsBadge.textContent) || 0;
            alertsBadge.textContent = currentCount + 1;
        }
    }

    /**
     * Cleanup resources
     */
    cleanup() {
        if (this.websocket) {
            this.websocket.close();
        }
        
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }
        
        this.removeMobileMenuOverlay();
        
        console.log('M10 Monitor cleaned up');
    }
}

// Initialize the application when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.m10Monitor = new M10Monitor();
});

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    if (window.m10Monitor) {
        window.m10Monitor.cleanup();
    }
});

// Add animation keyframes to document
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }

    .notification {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 12px;
    }

    .notification-success {
        border-left: 4px solid var(--success-color);
    }

    .notification-error {
        border-left: 4px solid var(--error-color);
    }

    .notification-warning {
        border-left: 4px solid var(--warning-color);
    }

    .notification-info {
        border-left: 4px solid var(--info-color);
    }

    .notification-close {
        background: none;
        border: none;
        font-size: 18px;
        cursor: pointer;
        color: var(--text-muted);
        padding: 4px;
        border-radius: 4px;
        transition: color var(--transition-fast);
    }

    .notification-close:hover {
        color: var(--text-primary);
        background: var(--bg-tertiary);
    }
`;
document.head.appendChild(style);
