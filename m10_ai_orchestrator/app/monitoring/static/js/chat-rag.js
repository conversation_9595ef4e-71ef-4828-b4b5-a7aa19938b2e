/**
 * 🧠 Chat RAG - Farejador de Informações Inteligente
 * Sistema de chat com busca semântica e geração contextualizada
 */

class ChatRAG {
    constructor() {
        this.apiUrl = '/api/v1/chat/rag';
        this.sessionId = this.generateSessionId();
        this.isLoading = false;
        this.messageHistory = [];
        console.log('🧠 ChatRAG: Inicializando farejador de informações...');
    }

    /**
     * Inicializa o sistema de chat RAG
     */
    init() {
        console.log('🧠 ChatRAG: Configurando interface...');
        try {
            // Limpar qualquer mensagem de erro anterior
            this.clearErrorMessage();

            this.setupEventListeners();
            this.setupExampleQueries();
            console.log('✅ ChatRAG: Sistema pronto para farejar informações!');
        } catch (error) {
            console.error('❌ ChatRAG: Erro na inicialização:', error);
        }
    }

    /**
     * Limpa mensagens de erro do chat
     */
    clearErrorMessage() {
        const chatSection = document.getElementById('chat-section');
        if (!chatSection) return;

        // Procurar por divs de erro e removê-las
        const errorDivs = chatSection.querySelectorAll('div[style*="color: #dc3545"], div[style*="color:#dc3545"]');
        errorDivs.forEach(div => {
            console.log('🧠 ChatRAG: Removendo mensagem de erro...');
            div.remove();
        });

        // Garantir que o conteúdo original do chat esteja visível
        const chatContainer = chatSection.querySelector('.chat-container');
        if (chatContainer) {
            chatContainer.style.display = 'block';
        }
    }

    /**
     * Configura os event listeners
     */
    setupEventListeners() {
        // Input de chat
        const chatInput = document.getElementById('chat-input');
        const sendBtn = document.getElementById('send-btn');
        const clearBtn = document.getElementById('clear-chat-btn');
        const newChatBtn = document.getElementById('new-chat-btn');

        if (chatInput) {
            // Enviar com Enter (Shift+Enter para nova linha)
            chatInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.sendMessage();
                }
            });

            // Habilitar/desabilitar botão de envio
            chatInput.addEventListener('input', () => {
                const hasText = chatInput.value.trim().length > 0;
                sendBtn.disabled = !hasText || this.isLoading;
            });

            // Auto-resize do textarea
            chatInput.addEventListener('input', () => {
                chatInput.style.height = 'auto';
                chatInput.style.height = Math.min(chatInput.scrollHeight, 120) + 'px';
            });
        }

        if (sendBtn) {
            sendBtn.addEventListener('click', () => this.sendMessage());
        }

        if (clearBtn) {
            clearBtn.addEventListener('click', () => this.clearChat());
        }

        if (newChatBtn) {
            newChatBtn.addEventListener('click', () => this.newChat());
        }
    }

    /**
     * Configura as consultas de exemplo
     */
    setupExampleQueries() {
        const exampleButtons = document.querySelectorAll('.example-query');
        exampleButtons.forEach(button => {
            button.addEventListener('click', () => {
                const query = button.getAttribute('data-query');
                const chatInput = document.getElementById('chat-input');
                if (chatInput && query) {
                    chatInput.value = query;
                    chatInput.focus();
                    // Trigger input event para habilitar botão
                    chatInput.dispatchEvent(new Event('input'));
                }
            });
        });
    }

    /**
     * Envia uma mensagem para o chat RAG
     */
    async sendMessage() {
        const chatInput = document.getElementById('chat-input');
        const message = chatInput.value.trim();
        
        if (!message || this.isLoading) return;

        // Limpar input e desabilitar
        chatInput.value = '';
        chatInput.style.height = 'auto';
        this.setLoading(true);

        // Adicionar mensagem do usuário
        this.addMessage('user', message);

        try {
            console.log('🔍 Enviando consulta para RAG:', message);
            
            // Fazer requisição para API RAG
            const response = await fetch(this.apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message: message,
                    session_id: this.sessionId,
                    use_context: true
                })
            });

            if (!response.ok) {
                throw new Error(`Erro HTTP: ${response.status}`);
            }

            const data = await response.json();
            console.log('✅ Resposta RAG recebida:', data);

            // Adicionar resposta do assistente
            this.addMessage('assistant', data.response, data.citations, data.metadata);
            
            // Salvar no histórico
            this.messageHistory.push({
                user: message,
                assistant: data.response,
                timestamp: new Date().toISOString(),
                metadata: data.metadata
            });

        } catch (error) {
            console.error('❌ Erro no chat RAG:', error);
            this.addMessage('assistant', 
                `❌ Erro ao processar sua consulta: ${error.message}\n\n` +
                `💡 Tente novamente ou reformule sua pergunta.`, 
                [], 
                { error: true }
            );
        } finally {
            this.setLoading(false);
        }
    }

    /**
     * Adiciona uma mensagem ao chat
     */
    addMessage(sender, content, citations = [], metadata = {}) {
        const messagesContainer = document.getElementById('chat-messages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;

        const timestamp = new Date().toLocaleTimeString('pt-BR', {
            hour: '2-digit',
            minute: '2-digit'
        });

        let citationsHtml = '';
        if (citations && citations.length > 0) {
            citationsHtml = `
                <div class="message-citations">
                    <h5>📚 Fontes encontradas:</h5>
                    <ul>
                        ${citations.map(citation => `
                            <li>
                                <strong>${citation.document}</strong>
                                ${citation.similarity ? `<span class="similarity">(${(citation.similarity * 100).toFixed(1)}% similar)</span>` : ''}
                            </li>
                        `).join('')}
                    </ul>
                </div>
            `;
        }

        let metadataHtml = '';
        if (metadata && metadata.results_found !== undefined) {
            metadataHtml = `
                <div class="message-metadata">
                    <small>🔍 ${metadata.results_found} resultados encontrados | ${metadata.search_type || 'busca'}</small>
                </div>
            `;
        }

        messageDiv.innerHTML = `
            <div class="message-header">
                <span class="message-sender">${sender === 'user' ? '👤 Você' : '🧠 RAG Assistant'}</span>
                <span class="message-time">${timestamp}</span>
            </div>
            <div class="message-content">
                ${this.formatMessage(content)}
            </div>
            ${citationsHtml}
            ${metadataHtml}
        `;

        messagesContainer.appendChild(messageDiv);

        // Gerenciar navegação para mensagens longas (apenas para assistente)
        if (sender === 'assistant') {
            this.manageChatNavigation(messageDiv, { citations, metadata });
        }

        // Scroll para a última mensagem
        messageDiv.scrollIntoView({ behavior: 'smooth' });
    }

    /**
     * Define o estado de loading
     */
    setLoading(loading) {
        this.isLoading = loading;
        const sendBtn = document.getElementById('send-btn');
        const chatInput = document.getElementById('chat-input');
        const statusIndicator = document.querySelector('.status-indicator');
        const statusText = document.querySelector('.status-text');

        if (sendBtn) {
            sendBtn.disabled = loading || !chatInput?.value.trim();
            sendBtn.innerHTML = loading ? 
                '<span class="send-icon">⏳</span>' : 
                '<span class="send-icon">📤</span>';
        }

        if (chatInput) {
            chatInput.disabled = loading;
        }

        if (statusIndicator && statusText) {
            if (loading) {
                statusIndicator.className = 'status-indicator loading';
                statusIndicator.textContent = '🔄';
                statusText.textContent = 'Farejando informações...';
            } else {
                statusIndicator.className = 'status-indicator ready';
                statusIndicator.textContent = '🟢';
                statusText.textContent = 'Pronto para buscar informações';
            }
        }
    }

    /**
     * Limpa o chat
     */
    clearChat() {
        const messagesContainer = document.getElementById('chat-messages');
        if (messagesContainer) {
            // Manter apenas a mensagem de boas-vindas
            const welcomeMessage = messagesContainer.querySelector('.welcome-message');
            messagesContainer.innerHTML = '';
            if (welcomeMessage) {
                messagesContainer.appendChild(welcomeMessage);
            }
        }
        this.messageHistory = [];
    }

    /**
     * Inicia nova conversa
     */
    newChat() {
        this.clearChat();
        this.sessionId = this.generateSessionId();
        console.log('🆕 Nova sessão de chat iniciada:', this.sessionId);
    }

    /**
     * Gera um ID de sessão único
     */
    generateSessionId() {
        return 'chat_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * Formata mensagens com markdown básico
     */
    formatMessage(text) {
        return text
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/\n/g, '<br>');
    }

    /**
     * Gerencia a sidebar de navegação para respostas longas
     */
    manageChatNavigation(messageElement, messageData) {
        console.log('🚀 manageChatNavigation: MÉTODO CHAMADO!', messageData);

        const sidebar = document.getElementById('chat-navigation');
        const responseNav = document.getElementById('response-navigation');
        const sourcesNav = document.getElementById('sources-navigation');

        console.log('🔍 manageChatNavigation: Verificando elementos...', {
            sidebar: !!sidebar,
            responseNav: !!responseNav,
            sourcesNav: !!sourcesNav
        });

        if (!sidebar || !responseNav || !sourcesNav) {
            console.log('❌ manageChatNavigation: Elementos não encontrados');
            return;
        }

        // Verifica se a mensagem é longa o suficiente para precisar de navegação
        const messageContent = messageElement.querySelector('.message-content');
        if (!messageContent) {
            console.log('❌ manageChatNavigation: messageContent não encontrado');
            return;
        }

        const contentHeight = messageContent.scrollHeight;
        const contentText = messageContent.textContent;
        const shouldShowNavigation = contentHeight > 200 || contentText.length > 500;

        console.log('🔍 manageChatNavigation: Análise de conteúdo', {
            contentHeight,
            textLength: contentText.length,
            shouldShowNavigation
        });

        if (shouldShowNavigation) {
            console.log('✅ manageChatNavigation: Criando navegação...');

            // Primeiro, cria navegação para seções da resposta
            console.log('🔍 DEBUG: PONTO 1 - Antes de try createResponseNavigation');
            try {
                console.log('🔍 DEBUG: PONTO 2 - Dentro do try, antes de createResponseNavigation');
                this.createResponseNavigation(messageContent, responseNav);
                console.log('🔍 DEBUG: PONTO 3 - Depois de createResponseNavigation - SUCESSO!');
            } catch (error) {
                console.error('❌ ERRO em createResponseNavigation:', error);
                console.error('❌ STACK TRACE:', error.stack);
                console.log('🔍 DEBUG: PONTO 3 - Depois de createResponseNavigation - ERRO CAPTURADO!');
            }

            console.log('🔍 DEBUG: PONTO 4 - Após try/catch de createResponseNavigation - CONTINUANDO...');

            // Segundo, cria navegação para fontes citadas se existirem
            console.log('🔍 DEBUG: PONTO 5 - Verificando messageData:', messageData);
            console.log('🔍 DEBUG: messageData.citations:', messageData?.citations);
            console.log('🔍 DEBUG: citations.length:', messageData?.citations?.length);

            if (messageData && messageData.citations && messageData.citations.length > 0) {
                console.log('🔍 DEBUG: PONTO 6 - ENTRANDO no if das citações');
                try {
                    console.log('🔍 DEBUG: PONTO 7 - Antes de createSourcesNavigation');
                    this.createSourcesNavigation(messageData.citations, sourcesNav);
                    console.log('🔍 DEBUG: PONTO 8 - Depois de createSourcesNavigation - SUCESSO!');
                } catch (error) {
                    console.error('❌ ERRO em createSourcesNavigation:', error);
                    console.error('❌ STACK TRACE:', error.stack);
                    console.log('🔍 DEBUG: PONTO 8 - Depois de createSourcesNavigation - ERRO CAPTURADO!');
                }
            } else {
                console.log('❌ manageChatNavigation: Nenhuma citação encontrada');
                console.log('❌ DEBUG: messageData existe?', !!messageData);
                console.log('❌ DEBUG: citations existe?', !!messageData?.citations);
                console.log('❌ DEBUG: citations.length > 0?', messageData?.citations?.length > 0);
                sourcesNav.innerHTML = '<div class="nav-item">Nenhuma fonte citada</div>';
            }

            console.log('🔍 DEBUG: PONTO 9 - FINAL do manageChatNavigation - ANTES de mostrar sidebar');

            console.log('🔍 DEBUG: PONTO 8 - Antes de sidebar.classList.add');
            sidebar.classList.add('visible');
            console.log('✅ manageChatNavigation: Sidebar visível');

            console.log('✅ manageChatNavigation chamado com sucesso');
        } else {
            sidebar.classList.remove('visible');
            console.log('❌ manageChatNavigation: Conteúdo muito pequeno, sidebar oculta');
        }
    }

    /**
     * Cria navegação para seções da resposta
     */
    createResponseNavigation(messageContent, container) {
        console.log('🔍 createResponseNavigation: Iniciando...');
        container.innerHTML = '';

        // Procura por cabeçalhos ou seções numeradas
        const sections = this.extractSections(messageContent.textContent);
        console.log('🔍 createResponseNavigation: Seções encontradas', {
            count: sections.length,
            sections: sections
        });

        if (sections.length === 0) {
            console.log('❌ createResponseNavigation: Nenhuma seção encontrada');
            container.innerHTML = '<div class="nav-item disabled">Nenhuma seção encontrada</div>';
            console.log('✅ createResponseNavigation: Finalizado (sem seções)');
            // REMOVIDO O RETURN PARA PERMITIR QUE manageChatNavigation CONTINUE EXECUTANDO
        }

        sections.forEach((section, index) => {
            const navItem = document.createElement('div');
            navItem.className = 'nav-item';
            navItem.innerHTML = `
                <span class="nav-number">${index + 1}</span>
                <span class="nav-title">${section.title || 'Seção ' + (index + 1)}</span>
            `;

            navItem.onclick = () => {
                this.scrollToSection(messageContent, section.position);
                // Remove active de outros itens
                container.querySelectorAll('.nav-item').forEach(item =>
                    item.classList.remove('active'));
                navItem.classList.add('active');
            };
            container.appendChild(navItem);
        });

        console.log('✅ createResponseNavigation: Navegação criada com sucesso');
    }

    /**
     * Cria navegação para fontes citadas
     */
    createSourcesNavigation(citations, container) {
        try {
            console.log('🚀 createSourcesNavigation: MÉTODO CHAMADO!', citations);
            container.innerHTML = '';

            // Debug: verificar estrutura das citações
            console.log('🔍 DEBUG: Estrutura das citações:', citations);

            citations.forEach((citation, index) => {
                console.log(`🔍 DEBUG: Citação ${index}:`, citation);

                const navItem = document.createElement('div');
                navItem.className = 'nav-item';

                // Tentar diferentes propriedades possíveis para o nome do arquivo
                const filename = citation.filename || citation.file || citation.source || citation.title || citation.name || `Documento ${index + 1}`;
                const similarity = citation.similarity || citation.score || citation.relevance || 'N/A';

                console.log(`🔍 DEBUG: Filename extraído: "${filename}", Similarity: "${similarity}"`);

                navItem.innerHTML = `
                    <div style="font-weight: 600; margin-bottom: 4px;">${filename}</div>
                    <div style="font-size: 0.8em; color: var(--text-muted);">${similarity}</div>
                `;
                navItem.onclick = () => {
                    try {
                        this.scrollToCitation(index);
                        container.querySelectorAll('.nav-item').forEach(item =>
                            item.classList.remove('active'));
                        navItem.classList.add('active');
                    } catch (scrollError) {
                        console.error('❌ Erro no scroll da citação:', scrollError);
                    }
                };
                container.appendChild(navItem);
                console.log(`✅ DEBUG: Item ${index} adicionado ao container`);
            });

            console.log('✅ createSourcesNavigation: Método concluído com sucesso!');
        } catch (error) {
            console.error('❌ ERRO em createSourcesNavigation:', error);
        }
    }

    /**
     * Extrai seções do texto para navegação
     */
    extractSections(text) {
        const sections = [];
        const lines = text.split('\n');

        lines.forEach((line, index) => {
            const trimmedLine = line.trim();
            if (!trimmedLine) return;

            // Procura por padrões de seção mais específicos
            if (
                trimmedLine.match(/^#{1,6}\s/) || // Markdown headers
                trimmedLine.match(/^\d+\.\s/) || // Numbered lists
                trimmedLine.match(/^[•\-\*]\s/) || // Bullet points
                trimmedLine.match(/^[A-Z][^.]*:$/) || // Titles ending with :
                trimmedLine.match(/^[A-Z][A-Z\s]{2,}$/) || // ALL CAPS titles
                trimmedLine.match(/^\*\*.*\*\*/) // Bold text
            ) {
                // Remove markdown formatting for display
                let cleanTitle = trimmedLine
                    .replace(/^#{1,6}\s/, '') // Remove markdown headers
                    .replace(/^\d+\.\s/, '') // Remove numbers
                    .replace(/^[•\-\*]\s/, '') // Remove bullets
                    .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold markdown
                    .trim();

                if (cleanTitle.length > 0) {
                    sections.push({
                        title: cleanTitle.substring(0, 60) + (cleanTitle.length > 60 ? '...' : ''),
                        position: index,
                        originalLine: trimmedLine
                    });
                }
            }
        });

        // Se não encontrou seções, cria seções baseadas em parágrafos
        if (sections.length === 0) {
            const paragraphs = text.split('\n\n').filter(p => p.trim().length > 0);
            paragraphs.forEach((paragraph, index) => {
                const firstLine = paragraph.split('\n')[0].trim();
                if (firstLine.length > 10) {
                    sections.push({
                        title: firstLine.substring(0, 60) + (firstLine.length > 60 ? '...' : ''),
                        position: index * 2, // Aproximação da posição
                        originalLine: firstLine
                    });
                }
            });
        }

        return sections;
    }

    /**
     * Adiciona funcionalidade para fechar sidebar em mobile
     */
    addMobileCloseFunctionality(sidebar) {
        // Remove listener anterior se existir
        if (sidebar._closeHandler) {
            sidebar.removeEventListener('click', sidebar._closeHandler);
        }

        // Adiciona novo listener
        sidebar._closeHandler = (e) => {
            // Se clicou no pseudo-elemento de fechar (aproximação)
            if (window.innerWidth <= 768 && e.offsetX > sidebar.offsetWidth - 40 && e.offsetY < 40) {
                sidebar.classList.remove('visible');
            }
        };

        sidebar.addEventListener('click', sidebar._closeHandler);
    }

    /**
     * Rola para uma seção específica
     */
    scrollToSection(messageContent, position) {
        const lines = messageContent.textContent.split('\n');
        const targetText = lines[position];

        // Simula scroll para a posição (implementação básica)
        messageContent.scrollTop = (position / lines.length) * messageContent.scrollHeight;
    }

    /**
     * Rola para uma citação específica
     */
    scrollToCitation(citationIndex) {
        const citations = document.querySelectorAll('.message-citations');
        if (citations[citationIndex]) {
            citations[citationIndex].scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });
        }
    }
}

// Inicializar quando a página carregar
document.addEventListener('DOMContentLoaded', () => {
    console.log('🧠 ChatRAG: DOM carregado, inicializando...');
    window.chatRAG = new ChatRAG();
    
    // Aguardar um pouco para garantir que todos os elementos estejam prontos
    setTimeout(() => {
        if (window.chatRAG) {
            window.chatRAG.init();
        }
    }, 100);
});
