/**
 * 🧠 Chat RAG - Farejador de Informações Inteligente
 * Sistema de chat com busca semântica e geração contextualizada
 */

class ChatRAG {
    constructor() {
        this.apiUrl = '/api/v1/chat/rag';
        this.sessionId = this.generateSessionId();
        this.isLoading = false;
        this.messageHistory = [];
        console.log('🧠 ChatRAG: Inicializando farejador de informações...');
    }

    /**
     * Inicializa o sistema de chat RAG
     */
    init() {
        console.log('🧠 ChatRAG: Configurando interface...');
        try {
            // Limpar qualquer mensagem de erro anterior
            this.clearErrorMessage();

            this.setupEventListeners();
            this.setupExampleQueries();
            console.log('✅ ChatRAG: Sistema pronto para farejar informações!');
        } catch (error) {
            console.error('❌ ChatRAG: Erro na inicialização:', error);
        }
    }

    /**
     * Limpa mensagens de erro do chat
     */
    clearErrorMessage() {
        const chatSection = document.getElementById('chat-section');
        if (!chatSection) return;

        // Procurar por divs de erro e removê-las
        const errorDivs = chatSection.querySelectorAll('div[style*="color: #dc3545"], div[style*="color:#dc3545"]');
        errorDivs.forEach(div => {
            console.log('🧠 ChatRAG: Removendo mensagem de erro...');
            div.remove();
        });

        // Garantir que o conteúdo original do chat esteja visível
        const chatContainer = chatSection.querySelector('.chat-container');
        if (chatContainer) {
            chatContainer.style.display = 'block';
        }
    }

    /**
     * Configura os event listeners
     */
    setupEventListeners() {
        // Input de chat
        const chatInput = document.getElementById('chat-input');
        const sendBtn = document.getElementById('send-btn');
        const clearBtn = document.getElementById('clear-chat-btn');
        const newChatBtn = document.getElementById('new-chat-btn');

        if (chatInput) {
            // Enviar com Enter (Shift+Enter para nova linha)
            chatInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.sendMessage();
                }
            });

            // Habilitar/desabilitar botão de envio
            chatInput.addEventListener('input', () => {
                const hasText = chatInput.value.trim().length > 0;
                sendBtn.disabled = !hasText || this.isLoading;
            });

            // Auto-resize do textarea
            chatInput.addEventListener('input', () => {
                chatInput.style.height = 'auto';
                chatInput.style.height = Math.min(chatInput.scrollHeight, 120) + 'px';
            });
        }

        if (sendBtn) {
            sendBtn.addEventListener('click', () => this.sendMessage());
        }

        if (clearBtn) {
            clearBtn.addEventListener('click', () => this.clearChat());
        }

        if (newChatBtn) {
            newChatBtn.addEventListener('click', () => this.newChat());
        }
    }

    /**
     * Configura as consultas de exemplo
     */
    setupExampleQueries() {
        const exampleButtons = document.querySelectorAll('.example-query');
        exampleButtons.forEach(button => {
            button.addEventListener('click', () => {
                const query = button.getAttribute('data-query');
                const chatInput = document.getElementById('chat-input');
                if (chatInput && query) {
                    chatInput.value = query;
                    chatInput.focus();
                    // Trigger input event para habilitar botão
                    chatInput.dispatchEvent(new Event('input'));
                }
            });
        });
    }

    /**
     * Envia uma mensagem para o chat RAG
     */
    async sendMessage() {
        const chatInput = document.getElementById('chat-input');
        const message = chatInput.value.trim();
        
        if (!message || this.isLoading) return;

        // Limpar input e desabilitar
        chatInput.value = '';
        chatInput.style.height = 'auto';
        this.setLoading(true);

        // Adicionar mensagem do usuário
        this.addMessage('user', message);

        try {
            console.log('🔍 Enviando consulta para RAG:', message);
            
            // Fazer requisição para API RAG
            const response = await fetch(this.apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message: message,
                    session_id: this.sessionId,
                    use_context: true
                })
            });

            if (!response.ok) {
                throw new Error(`Erro HTTP: ${response.status}`);
            }

            const data = await response.json();
            console.log('✅ Resposta RAG recebida:', data);

            // Adicionar resposta do assistente
            this.addMessage('assistant', data.response, data.citations, data.metadata);
            
            // Salvar no histórico
            this.messageHistory.push({
                user: message,
                assistant: data.response,
                timestamp: new Date().toISOString(),
                metadata: data.metadata
            });

        } catch (error) {
            console.error('❌ Erro no chat RAG:', error);
            this.addMessage('assistant', 
                `❌ Erro ao processar sua consulta: ${error.message}\n\n` +
                `💡 Tente novamente ou reformule sua pergunta.`, 
                [], 
                { error: true }
            );
        } finally {
            this.setLoading(false);
        }
    }

    /**
     * Adiciona uma mensagem ao chat
     */
    addMessage(sender, content, citations = [], metadata = {}) {
        const messagesContainer = document.getElementById('chat-messages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;

        const timestamp = new Date().toLocaleTimeString('pt-BR', {
            hour: '2-digit',
            minute: '2-digit'
        });

        let citationsHtml = '';
        if (citations && citations.length > 0) {
            citationsHtml = `
                <div class="message-citations">
                    <h5>📚 Fontes encontradas:</h5>
                    <ul>
                        ${citations.map(citation => `
                            <li>
                                <strong>${citation.document}</strong>
                                ${citation.similarity ? `<span class="similarity">(${(citation.similarity * 100).toFixed(1)}% similar)</span>` : ''}
                            </li>
                        `).join('')}
                    </ul>
                </div>
            `;
        }

        let metadataHtml = '';
        if (metadata && metadata.results_found !== undefined) {
            metadataHtml = `
                <div class="message-metadata">
                    <small>🔍 ${metadata.results_found} resultados encontrados | ${metadata.search_type || 'busca'}</small>
                </div>
            `;
        }

        messageDiv.innerHTML = `
            <div class="message-header">
                <span class="message-sender">${sender === 'user' ? '👤 Você' : '🧠 RAG Assistant'}</span>
                <span class="message-time">${timestamp}</span>
            </div>
            <div class="message-content">
                ${this.formatMessage(content)}
            </div>
            ${citationsHtml}
            ${metadataHtml}
        `;

        messagesContainer.appendChild(messageDiv);
        
        // Scroll para a última mensagem
        messageDiv.scrollIntoView({ behavior: 'smooth' });
    }

    /**
     * Define o estado de loading
     */
    setLoading(loading) {
        this.isLoading = loading;
        const sendBtn = document.getElementById('send-btn');
        const chatInput = document.getElementById('chat-input');
        const statusIndicator = document.querySelector('.status-indicator');
        const statusText = document.querySelector('.status-text');

        if (sendBtn) {
            sendBtn.disabled = loading || !chatInput?.value.trim();
            sendBtn.innerHTML = loading ? 
                '<span class="send-icon">⏳</span>' : 
                '<span class="send-icon">📤</span>';
        }

        if (chatInput) {
            chatInput.disabled = loading;
        }

        if (statusIndicator && statusText) {
            if (loading) {
                statusIndicator.className = 'status-indicator loading';
                statusIndicator.textContent = '🔄';
                statusText.textContent = 'Farejando informações...';
            } else {
                statusIndicator.className = 'status-indicator ready';
                statusIndicator.textContent = '🟢';
                statusText.textContent = 'Pronto para buscar informações';
            }
        }
    }

    /**
     * Limpa o chat
     */
    clearChat() {
        const messagesContainer = document.getElementById('chat-messages');
        if (messagesContainer) {
            // Manter apenas a mensagem de boas-vindas
            const welcomeMessage = messagesContainer.querySelector('.welcome-message');
            messagesContainer.innerHTML = '';
            if (welcomeMessage) {
                messagesContainer.appendChild(welcomeMessage);
            }
        }
        this.messageHistory = [];
    }

    /**
     * Inicia nova conversa
     */
    newChat() {
        this.clearChat();
        this.sessionId = this.generateSessionId();
        console.log('🆕 Nova sessão de chat iniciada:', this.sessionId);
    }

    /**
     * Gera um ID de sessão único
     */
    generateSessionId() {
        return 'chat_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * Formata mensagens com markdown básico
     */
    formatMessage(text) {
        return text
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/\n/g, '<br>');
    }
}

// Inicializar quando a página carregar
document.addEventListener('DOMContentLoaded', () => {
    console.log('🧠 ChatRAG: DOM carregado, inicializando...');
    window.chatRAG = new ChatRAG();
    
    // Aguardar um pouco para garantir que todos os elementos estejam prontos
    setTimeout(() => {
        if (window.chatRAG) {
            window.chatRAG.init();
        }
    }, 100);
});
