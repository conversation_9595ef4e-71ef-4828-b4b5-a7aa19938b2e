<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="M10 AI Orchestrator Monitor - Interface de monitoramento em tempo real">
    <meta name="author" content="M10 AI Orchestrator Team">
    <title>M10 AI Orchestrator Monitor</title>
    
    <!-- CSS Framework -->
    <link rel="stylesheet" href="static/css/monitor.css">
    <link rel="stylesheet" href="static/css/chat-standalone.css">
    
    <!-- Chart.js CDN -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns@3.0.0/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    
    <!-- Favicon (set to empty to prevent 404 on /favicon.ico) -->
    <link rel="icon" href="data:,">
    
    <!-- Preload critical resources -->
    <link rel="preload" href="static/css/monitor.css" as="style">
    <link rel="preload" href="static/js/monitor.js" as="script">
</head>
<body>
    <!-- Skip to content link for accessibility -->
    <a href="#main-content" class="skip-link" aria-label="Pular para conteúdo principal">
        Pular para conteúdo principal
    </a>

    <!-- Header -->
    <header class="header" role="banner">
        <div class="header-container">
            <div class="header-left">
                <button class="menu-toggle" aria-label="Alternar menu lateral" aria-expanded="false" aria-controls="sidebar">
                    <span class="hamburger"></span>
                    <span class="hamburger"></span>
                    <span class="hamburger"></span>
                </button>
                <h1 class="header-title">
                    <span class="header-icon" aria-hidden="true">🤖</span>
                    M10 AI Orchestrator Monitor
                </h1>
            </div>
            
            <div class="header-center">
                <div class="system-status" aria-live="polite">
                    <span class="status-indicator" id="system-status" 
                          data-status="loading" 
                          aria-label="Status do sistema: carregando">
                        <span class="status-dot"></span>
                    </span>
                    <span class="status-text" id="status-text">Carregando...</span>
                </div>
            </div>
            
            <div class="header-right">
                <nav class="header-nav" role="navigation" aria-label="Navegação do cabeçalho">
                    <button class="nav-button" aria-label="Notificações" data-notifications="0">
                        <span class="icon">🔔</span>
                        <span class="notification-badge" id="notification-count" aria-hidden="true">0</span>
                    </button>
                    <button class="nav-button" aria-label="Configurações">
                        <span class="icon">⚙️</span>
                    </button>
                    <button class="nav-button theme-toggle" aria-label="Alternar tema claro/escuro">
                        <span class="icon">🌙</span>
                    </button>
                </nav>
                
                <div class="last-update" aria-live="polite">
                    <span class="update-label">Última atualização:</span>
                    <time id="last-update-time" datetime="">--:--</time>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Layout -->
    <div class="layout">
        <!-- Sidebar Navigation -->
        <aside class="sidebar" id="sidebar" role="navigation" aria-label="Navegação principal">
            <nav class="sidebar-nav">
                <ul class="nav-list" role="menubar">
                    <li class="nav-item" role="none">
                        <a href="#dashboard" 
                           class="nav-link active" 
                           role="menuitem"
                           aria-current="page"
                           data-section="dashboard">
                            <span class="nav-icon" aria-hidden="true">📊</span>
                            <span class="nav-text">Dashboard</span>
                        </a>
                    </li>
                    
                    <li class="nav-item" role="none">
                        <a href="#services" 
                           class="nav-link" 
                           role="menuitem"
                           data-section="services">
                            <span class="nav-icon" aria-hidden="true">🔧</span>
                            <span class="nav-text">Serviços</span>
                            <span class="nav-badge" id="services-badge" aria-label="Status dos serviços">3/3</span>
                        </a>
                    </li>
                    
                    <li class="nav-item" role="none">
                        <a href="#metrics" 
                           class="nav-link" 
                           role="menuitem"
                           data-section="metrics">
                            <span class="nav-icon" aria-hidden="true">📈</span>
                            <span class="nav-text">Métricas</span>
                        </a>
                    </li>
                    
                    <li class="nav-item" role="none">
                        <a href="#alerts" 
                           class="nav-link" 
                           role="menuitem"
                           data-section="alerts">
                            <span class="nav-icon" aria-hidden="true">🚨</span>
                            <span class="nav-text">Alertas</span>
                            <span class="nav-badge alert-badge" id="alerts-badge" aria-label="Alertas ativos">0</span>
                        </a>
                    </li>
                    
                    <li class="nav-item" role="none">
                        <a href="#chat" 
                           class="nav-link" 
                           role="menuitem"
                           data-section="chat">
                            <span class="nav-icon" aria-hidden="true">💬</span>
                            <span class="nav-text">Chat de Teste</span>
                        </a>
                    </li>
                    
                    <li class="nav-item nav-divider" role="none" aria-hidden="true"></li>
                    
                    <li class="nav-item" role="none">
                        <a href="#debug" 
                           class="nav-link" 
                           role="menuitem"
                           data-section="debug">
                            <span class="nav-icon" aria-hidden="true">🐛</span>
                            <span class="nav-text">Debug Tools</span>
                        </a>
                    </li>
                    
                    <li class="nav-item" role="none">
                        <a href="#settings" 
                           class="nav-link" 
                           role="menuitem"
                           data-section="settings">
                            <span class="nav-icon" aria-hidden="true">⚙️</span>
                            <span class="nav-text">Configurações</span>
                        </a>
                    </li>
                </ul>
            </nav>
            
            <!-- Sidebar Footer -->
            <div class="sidebar-footer">
                <div class="connection-status" aria-live="polite">
                    <span class="connection-dot" data-status="connected" aria-hidden="true"></span>
                    <span class="connection-text">WebSocket Conectado</span>
                </div>
                <div class="version-info">
                    <span class="version-text">M10 v1.0.0</span>
                </div>
            </div>
        </aside>

        <!-- Main Content Area -->
        <main class="main-content" id="main-content" role="main">
            <!-- Dashboard Section -->
            <section id="dashboard-section" class="content-section active" aria-labelledby="dashboard-title">
                <header class="section-header">
                    <h2 id="dashboard-title" class="section-title">Dashboard do Sistema</h2>
                    <div class="section-actions">
                        <button class="action-button refresh-button" aria-label="Atualizar dashboard">
                            <span class="icon">🔄</span>
                            <span class="button-text">Atualizar</span>
                        </button>
                        <button class="action-button export-button" aria-label="Exportar dados">
                            <span class="icon">📤</span>
                            <span class="button-text">Exportar</span>
                        </button>
                    </div>
                </header>
                
                <div class="section-content">
                    <div class="dashboard-grid">
                        <!-- System Metrics Widget -->
                        <div class="widget" id="system-metrics-widget" aria-label="Widget de métricas do sistema">
                            <div class="widget-header">
                                <h3 class="widget-title">Métricas do Sistema</h3>
                                <div class="widget-actions">
                                    <button class="widget-action" aria-label="Atualizar métricas" data-action="refresh-metrics">
                                        <span class="icon">🔄</span>
                                    </button>
                                </div>
                            </div>
                            <div class="widget-content">
                                <div class="metrics-overview">
                                    <div class="metric-card" data-metric="cpu">
                                        <div class="metric-icon">🖥️</div>
                                        <div class="metric-info">
                                            <div class="metric-label">CPU</div>
                                            <div class="metric-value" id="cpu-usage">--</div>
                                            <div class="metric-trend" id="cpu-trend"></div>
                                        </div>
                                        <div class="metric-gauge">
                                            <canvas id="cpu-gauge" width="60" height="60"></canvas>
                                        </div>
                                    </div>
                                    
                                    <div class="metric-card" data-metric="memory">
                                        <div class="metric-icon">💾</div>
                                        <div class="metric-info">
                                            <div class="metric-label">Memória</div>
                                            <div class="metric-value" id="memory-usage">--</div>
                                            <div class="metric-trend" id="memory-trend"></div>
                                        </div>
                                        <div class="metric-gauge">
                                            <canvas id="memory-gauge" width="60" height="60"></canvas>
                                        </div>
                                    </div>
                                    
                                    <div class="metric-card" data-metric="disk">
                                        <div class="metric-icon">💿</div>
                                        <div class="metric-info">
                                            <div class="metric-label">Disco</div>
                                            <div class="metric-value" id="disk-usage">--</div>
                                            <div class="metric-trend" id="disk-trend"></div>
                                        </div>
                                        <div class="metric-gauge">
                                            <canvas id="disk-gauge" width="60" height="60"></canvas>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="system-chart">
                                    <canvas id="system-metrics-chart" width="400" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Services Status Widget -->
                        <div class="widget" id="services-status-widget" aria-label="Widget de status dos serviços">
                            <div class="widget-header">
                                <h3 class="widget-title">Status dos Serviços</h3>
                                <div class="widget-actions">
                                    <button class="widget-action" aria-label="Verificar serviços" data-action="check-services">
                                        <span class="icon">🔍</span>
                                    </button>
                                </div>
                            </div>
                            <div class="widget-content">
                                <div class="services-overview">
                                    <div class="service-summary">
                                        <div class="summary-stat">
                                            <div class="stat-value" id="services-healthy">--</div>
                                            <div class="stat-label">Saudáveis</div>
                                        </div>
                                        <div class="summary-stat">
                                            <div class="stat-value" id="services-degraded">--</div>
                                            <div class="stat-label">Degradados</div>
                                        </div>
                                        <div class="summary-stat">
                                            <div class="stat-value" id="services-critical">--</div>
                                            <div class="stat-label">Críticos</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="services-list" id="services-list">
                                    <!-- Services will be populated by JavaScript -->
                                    <div class="service-loading">Carregando serviços...</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- AI Metrics Widget -->
                        <div class="widget" id="ai-metrics-widget" aria-label="Widget de métricas de IA">
                            <div class="widget-header">
                                <h3 class="widget-title">Métricas de IA</h3>
                                <div class="widget-actions">
                                    <button class="widget-action" aria-label="Atualizar métricas de IA" data-action="refresh-ai-metrics">
                                        <span class="icon">🤖</span>
                                    </button>
                                </div>
                            </div>
                            <div class="widget-content">
                                <div class="ai-overview">
                                    <div class="ai-stat">
                                        <div class="ai-stat-icon">💬</div>
                                        <div class="ai-stat-info">
                                            <div class="ai-stat-value" id="total-conversations">--</div>
                                            <div class="ai-stat-label">Conversas Totais</div>
                                        </div>
                                    </div>
                                    
                                    <div class="ai-stat">
                                        <div class="ai-stat-icon">⚡</div>
                                        <div class="ai-stat-info">
                                            <div class="ai-stat-value" id="active-conversations">--</div>
                                            <div class="ai-stat-label">Conversas Ativas</div>
                                        </div>
                                    </div>
                                    
                                    <div class="ai-stat">
                                        <div class="ai-stat-icon">⏱️</div>
                                        <div class="ai-stat-info">
                                            <div class="ai-stat-value" id="avg-response-time">--</div>
                                            <div class="ai-stat-label">Tempo Médio (s)</div>
                                        </div>
                                    </div>
                                    
                                    <div class="ai-stat">
                                        <div class="ai-stat-icon">🎯</div>
                                        <div class="ai-stat-info">
                                            <div class="ai-stat-value" id="api-calls-today">--</div>
                                            <div class="ai-stat-label">Chamadas Hoje</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="ai-performance">
                                    <div class="performance-metric">
                                        <div class="performance-label">Taxa de Erro</div>
                                        <div class="performance-bar">
                                            <div class="performance-fill" id="error-rate-bar" style="width: 0%;"></div>
                                        </div>
                                        <div class="performance-value" id="error-rate">0%</div>
                                    </div>
                                    
                                    <div class="performance-metric">
                                        <div class="performance-label">Tokens Usados</div>
                                        <div class="performance-bar">
                                            <div class="performance-fill" id="tokens-usage-bar" style="width: 0%;"></div>
                                        </div>
                                        <div class="performance-value" id="tokens-used">0</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Recent Activity Widget -->
                        <div class="widget widget-full" id="activity-widget" aria-label="Widget de atividade recente">
                            <div class="widget-header">
                                <h3 class="widget-title">Atividade Recente</h3>
                                <div class="widget-actions">
                                    <button class="widget-action" aria-label="Limpar atividades" data-action="clear-activity">
                                        <span class="icon">🧹</span>
                                    </button>
                                    <button class="widget-action" aria-label="Exportar log" data-action="export-activity">
                                        <span class="icon">📤</span>
                                    </button>
                                </div>
                            </div>
                            <div class="widget-content">
                                <div class="activity-timeline" id="activity-timeline">
                                    <!-- Activity items will be populated by JavaScript -->
                                    <div class="activity-loading">Carregando atividades...</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Services Section -->
            <section id="services-section" class="content-section" aria-labelledby="services-title" hidden>
                <header class="section-header">
                    <h2 id="services-title" class="section-title">Status dos Serviços</h2>
                    <div class="section-actions">
                        <button class="action-button" aria-label="Verificar todos os serviços">
                            <span class="icon">🔍</span>
                            <span class="button-text">Verificar Todos</span>
                        </button>
                    </div>
                </header>
                
                <div class="section-content">
                    <div class="services-placeholder">
                        <p>Monitoramento de serviços será implementado aqui</p>
                    </div>
                </div>
            </section>

            <!-- Metrics Section -->
            <section id="metrics-section" class="content-section" aria-labelledby="metrics-title" hidden>
                <header class="section-header">
                    <h2 id="metrics-title" class="section-title">Métricas de Performance</h2>
                    <div class="section-actions">
                        <select class="time-range-selector" aria-label="Selecionar período">
                            <option value="1h">Última hora</option>
                            <option value="24h" selected>Últimas 24 horas</option>
                            <option value="7d">Últimos 7 dias</option>
                            <option value="30d">Últimos 30 dias</option>
                        </select>
                    </div>
                </header>
                
                <div class="section-content">
                    <div class="metrics-placeholder">
                        <p>Gráficos e métricas serão implementados aqui</p>
                    </div>
                </div>
            </section>

            <!-- Alerts Section -->
            <section id="alerts-section" class="content-section" aria-labelledby="alerts-title" hidden>
                <header class="section-header">
                    <h2 id="alerts-title" class="section-title">Centro de Alertas</h2>
                    <div class="section-actions">
                        <button class="action-button" aria-label="Marcar todos como lidos">
                            <span class="icon">✓</span>
                            <span class="button-text">Marcar Todos</span>
                        </button>
                    </div>
                </header>
                
                <div class="section-content">
                    <div class="alerts-placeholder">
                        <p>Sistema de alertas será implementado aqui</p>
                    </div>
                </div>
            </section>

            <!-- Chat Section -->
            <section id="chat-section" class="content-section" aria-labelledby="chat-title" hidden>
                <header class="section-header">
                    <h2 id="chat-title" class="section-title">Chat de Teste IA</h2>
                    <div class="section-actions">
                        <button class="action-button" aria-label="Nova conversa">
                            <span class="icon">💬</span>
                            <span class="button-text">Nova Conversa</span>
                        </button>
                    </div>
                </header>
                
                <div class="section-content">
                    <div class="chat-placeholder">
                        <p>Interface de chat será implementada aqui</p>
                    </div>
                </div>
            </section>

            <!-- Debug Section -->
            <section id="debug-section" class="content-section" aria-labelledby="debug-title" hidden>
                <header class="section-header">
                    <h2 id="debug-title" class="section-title">Ferramentas de Debug</h2>
                    <div class="section-actions">
                        <button class="action-button" aria-label="Limpar logs">
                            <span class="icon">🧹</span>
                            <span class="button-text">Limpar Logs</span>
                        </button>
                    </div>
                </header>
                
                <div class="section-content">
                    <div class="debug-placeholder">
                        <p>Ferramentas de debug serão implementadas aqui</p>
                    </div>
                </div>
            </section>

            <!-- Settings Section -->
            <section id="settings-section" class="content-section" aria-labelledby="settings-title" hidden>
                <header class="section-header">
                    <h2 id="settings-title" class="section-title">Configurações</h2>
                    <div class="section-actions">
                        <button class="action-button" aria-label="Salvar configurações">
                            <span class="icon">💾</span>
                            <span class="button-text">Salvar</span>
                        </button>
                    </div>
                </header>
                
                <div class="section-content">
                    <div class="settings-placeholder">
                        <p>Painel de configurações será implementado aqui</p>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Footer -->
    <footer class="footer" role="contentinfo">
        <div class="footer-container">
            <div class="footer-left">
                <span class="footer-text">© 2025 M10 AI Orchestrator Monitor</span>
            </div>
            <div class="footer-right">
                <span class="footer-text">Desenvolvido com ❤️ para otimizar IA</span>
            </div>
        </div>
    </footer>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loading-overlay" aria-hidden="true">
        <div class="loading-spinner" aria-label="Carregando...">
            <div class="spinner-ring"></div>
            <span class="loading-text">Carregando...</span>
        </div>
    </div>

    <!-- Error Modal -->
    <div class="modal-overlay" id="error-modal" aria-hidden="true" role="dialog" aria-labelledby="error-title">
        <div class="modal-content">
            <header class="modal-header">
                <h3 id="error-title" class="modal-title">Erro</h3>
                <button class="modal-close" aria-label="Fechar modal">&times;</button>
            </header>
            <div class="modal-body">
                <p id="error-message">Ocorreu um erro inesperado.</p>
            </div>
            <footer class="modal-footer">
                <button class="button button-primary" data-action="retry">Tentar Novamente</button>
                <button class="button button-secondary" data-action="close">Fechar</button>
            </footer>
        </div>
    </div>

    <!-- Scripts -->
    <script src="static/js/monitor.js" defer></script>
    <script src="static/js/chat-standalone.js" defer></script>
    
    <!-- Service Worker Registration -->
    <!--
    <script>
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('static/js/sw.js')
                .then(registration => console.log('SW registered'))
                .catch(error => console.log('SW registration failed'));
        }
    </script>
    -->
</body>
</html>
