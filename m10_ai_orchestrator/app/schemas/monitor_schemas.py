"""
Schemas for monitor endpoints
"""
from typing import Dict, List, Optional, Any
from datetime import datetime
from pydantic import BaseModel, Field


class ServiceStatus(BaseModel):
    """Service status information"""
    name: str = Field(..., description="Service name")
    status: str = Field(..., description="Service status (ok, warning, error)")
    message: Optional[str] = Field(None, description="Optional status message")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Status check timestamp")
    details: Optional[Any] = Field(None, description="Additional status details (can be str or dict)")


class QueryLogResponse(BaseModel):
    """Query log response"""
    id: str = Field(..., description="Query ID")
    query: str = Field(..., description="Query text")
    response: str = Field(..., description="Response text")
    timestamp: datetime = Field(..., description="Query timestamp")
    duration_ms: float = Field(..., description="Query duration in milliseconds")
    status: str = Field(..., description="Query status")
    error: Optional[str] = Field(None, description="Error message if any")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class MessageResponse(BaseModel):
    """Generic message response"""
    message: str = Field(..., description="Response message")
    status: str = Field("success", description="Response status")
    data: Optional[Dict[str, Any]] = Field(None, description="Additional data")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Response timestamp")


class HealthCheckResponse(BaseModel):
    """Health check response"""
    status: str = Field(..., description="Overall health status")
    version: str = Field(..., description="Service version")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Check timestamp")
    dependencies: Optional[Dict[str, str]] = Field(None, description="Status of dependencies")
    metrics: Optional[Dict[str, Any]] = Field(None, description="Service metrics")


class ChatSessionResponse(BaseModel):
    """Chat session response"""
    session_id: str = Field(..., description="Session ID")
    user_id: str = Field(..., description="User ID")
    created_at: datetime = Field(..., description="Session creation time")
    updated_at: datetime = Field(..., description="Last update time")
    message_count: int = Field(..., description="Number of messages in session")
    status: str = Field(..., description="Session status")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Session metadata")
