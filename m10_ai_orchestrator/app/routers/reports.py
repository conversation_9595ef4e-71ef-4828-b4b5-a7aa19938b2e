"""
M10 AI Orchestrator - Reports Router

Rotas para geração, visualização e exportação de relatórios.
"""

from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Response
from fastapi.responses import HTMLResponse, FileResponse
from sqlalchemy.orm import Session
from pydantic import BaseModel
import logging
import os

from ..database import get_database_session
from ..services.data_analysis_service import DataAnalysisService
from ..services.report_generator import ReportGenerator
from ..services.pdf_exporter import PDFExporter

logger = logging.getLogger(__name__)

# Configurar router
router = APIRouter(prefix="/reports", tags=["reports"])

# Instâncias globais dos serviços
report_generator = ReportGenerator()
pdf_exporter = PDFExporter()


# Schemas
class ReportRequest(BaseModel):
    report_type: str  # 'product_analysis', 'version_comparison', 'database_stats'
    parameters: Dict[str, Any]


class ReportInfo(BaseModel):
    id: str
    type: str
    title: str
    created_at: str
    url: str


class PDFExportRequest(BaseModel):
    report_id: str
    filename: Optional[str] = None


@router.post("/generate", response_model=ReportInfo)
async def generate_report(
    request: ReportRequest,
    db: Session = Depends(get_database_session)
):
    """
    Gera um novo relatório baseado nos parâmetros fornecidos.
    
    Tipos de relatório suportados:
    - product_analysis: Análise de produto
    - version_comparison: Comparação entre versões
    - database_stats: Estatísticas da base de dados
    """
    try:
        analysis_service = DataAnalysisService(db)
        
        if request.report_type == "product_analysis":
            # Parâmetros esperados: product_name, include_details
            product_name = request.parameters.get("product_name")
            if not product_name:
                raise HTTPException(status_code=400, detail="product_name é obrigatório")
            
            include_details = request.parameters.get("include_details", True)
            
            # Realizar análise
            analysis = analysis_service.analyze_product_versions(product_name)
            
            # Gerar relatório
            report_info = report_generator.generate_product_analysis_report(
                analysis, include_details
            )
            
        elif request.report_type == "version_comparison":
            # Parâmetros esperados: document_name, version_a, version_b
            document_name = request.parameters.get("document_name")
            version_a = request.parameters.get("version_a")
            version_b = request.parameters.get("version_b")
            
            if not all([document_name, version_a, version_b]):
                raise HTTPException(
                    status_code=400, 
                    detail="document_name, version_a e version_b são obrigatórios"
                )
            
            # Realizar comparação
            comparison = analysis_service.compare_document_versions(
                document_name, version_a, version_b
            )
            
            # Gerar relatório
            report_info = report_generator.generate_comparison_report(comparison)
            
        elif request.report_type == "database_stats":
            # Gerar relatório de estatísticas (implementar se necessário)
            stats = analysis_service.get_database_statistics()
            
            # Por enquanto, retornar erro - implementar template específico
            raise HTTPException(
                status_code=501, 
                detail="Relatório de estatísticas ainda não implementado"
            )
            
        else:
            raise HTTPException(
                status_code=400, 
                detail=f"Tipo de relatório não suportado: {request.report_type}"
            )
        
        return ReportInfo(
            id=report_info['id'],
            type=report_info['type'],
            title=report_info['title'],
            created_at=report_info['created_at'].isoformat(),
            url=report_info['url']
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro ao gerar relatório: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{report_id}", response_class=HTMLResponse)
async def view_report(report_id: str):
    """Visualiza um relatório em formato HTML."""
    try:
        report_info = report_generator.get_report(report_id)
        
        if not report_info:
            raise HTTPException(status_code=404, detail="Relatório não encontrado")
        
        return HTMLResponse(content=report_info['html'])
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro ao visualizar relatório: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{report_id}/export-pdf")
async def export_report_to_pdf(
    report_id: str,
    request: PDFExportRequest
):
    """Exporta um relatório para PDF."""
    try:
        report_info = report_generator.get_report(report_id)
        
        if not report_info:
            raise HTTPException(status_code=404, detail="Relatório não encontrado")
        
        # Gerar nome do arquivo se não fornecido
        filename = request.filename
        if not filename:
            filename = f"relatorio_{report_id[:8]}.pdf"
        
        # Exportar para PDF
        export_result = pdf_exporter.export_html_to_pdf(
            html_content=report_info['html'],
            filename=filename
        )
        
        if not export_result['success']:
            raise HTTPException(
                status_code=500, 
                detail=f"Erro na exportação: {export_result['error']}"
            )
        
        return {
            'success': True,
            'filename': export_result['filename'],
            'download_url': export_result['download_url'],
            'file_size': export_result['file_size']
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro ao exportar PDF: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/download/{filename}")
async def download_pdf(filename: str):
    """Faz download de um arquivo PDF gerado."""
    try:
        file_info = pdf_exporter.get_export_info(filename)
        
        if not file_info:
            raise HTTPException(status_code=404, detail="Arquivo não encontrado")
        
        file_path = file_info['filepath']
        
        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="Arquivo não existe")
        
        return FileResponse(
            path=file_path,
            filename=filename,
            media_type='application/pdf'
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro no download: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/", response_model=List[ReportInfo])
async def list_reports(limit: int = 20):
    """Lista relatórios recentes."""
    try:
        reports = report_generator.list_reports(limit)
        
        return [
            ReportInfo(
                id=report['id'],
                type=report['type'],
                title=report['title'],
                created_at=report['created_at'].isoformat(),
                url=report['url']
            )
            for report in reports
        ]
        
    except Exception as e:
        logger.error(f"Erro ao listar relatórios: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{report_id}")
async def delete_report(report_id: str):
    """Remove um relatório."""
    try:
        report_info = report_generator.get_report(report_id)
        
        if not report_info:
            raise HTTPException(status_code=404, detail="Relatório não encontrado")
        
        # Remover do storage (em produção, remover do banco)
        if report_id in report_generator.report_storage:
            del report_generator.report_storage[report_id]
        
        return {"success": True, "message": "Relatório removido"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro ao remover relatório: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/pdf/list")
async def list_pdf_files():
    """Lista arquivos PDF exportados."""
    try:
        files = pdf_exporter.list_exported_files()
        return {"files": files}
        
    except Exception as e:
        logger.error(f"Erro ao listar PDFs: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/pdf/cleanup")
async def cleanup_old_pdfs(days_old: int = 7):
    """Remove arquivos PDF antigos."""
    try:
        removed_count = pdf_exporter.cleanup_old_files(days_old)
        return {
            "success": True,
            "removed_count": removed_count,
            "message": f"Removidos {removed_count} arquivos com mais de {days_old} dias"
        }
        
    except Exception as e:
        logger.error(f"Erro na limpeza: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/system/pdf-engines")
async def get_pdf_engines():
    """Retorna informações sobre engines de PDF disponíveis."""
    try:
        return {
            "available_engines": pdf_exporter.available_engines,
            "install_commands": pdf_exporter.install_dependencies()
        }
        
    except Exception as e:
        logger.error(f"Erro ao verificar engines: {e}")
        raise HTTPException(status_code=500, detail=str(e))
