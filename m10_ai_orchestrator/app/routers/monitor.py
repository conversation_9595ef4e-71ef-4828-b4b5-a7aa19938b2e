"""
M10 AI Orchestrator Monitor Router
Provides monitoring endpoints and WebSocket connections for real-time dashboard
"""

from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Request, HTTPException, Query, Depends
from fastapi.responses import HTMLResponse, FileResponse
from fastapi.staticfiles import StaticFiles # Importar StaticFiles
import json
import asyncio 
import logging # Usar logging padrão
from datetime import datetime, timedelta, timezone # Adicionado timezone
from typing import Dict, List, Optional, Any # Adicionado Any
import psutil
import uuid
from pathlib import Path
from sqlalchemy.orm import Session, sessionmaker # Adicionado sessionmaker
from sqlalchemy import select, func # Removido extract, não usado diretamente aqui

# Corrigir o caminho de importação para settings e db_config
from app.config import get_settings
from app.database import db_config, get_database_session # get_database_session para Depends

from app.models.query_log import QueryLog, QueryStatus, QueryType # QueryStatus e QueryType são enums
from app.models.chat_session import ChatSession, SessionStatus # SessionStatus é enum
from app.integrations.health_service import (
    check_postgresql_health, 
    check_redis_health, 
    check_openai_health, 
    check_rabbitmq_health,
    check_m1_health,
    check_m2_health, 
    check_m3_health,
    check_m4_health,
    check_m5_health,
    check_m6_health,
    check_m7_health,
    check_m8_health,
    check_m14_health
)
from app.schemas.monitor_schemas import ServiceStatus, QueryLogResponse, MessageResponse # Adicionar schemas

logger = logging.getLogger(__name__) # Usar logging padrão
settings = get_settings()

router = APIRouter(prefix="/monitor", tags=["monitoring"])

# WebSocket connection manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        client_id = str(uuid.uuid4())
        self.active_connections[client_id] = websocket
        logger.info(f"WebSocket client connected: {client_id}")
        return client_id

    def disconnect(self, client_id: str):
        if client_id in self.active_connections:
            del self.active_connections[client_id]
            logger.info(f"WebSocket client disconnected: {client_id}")

    async def send_personal_message(self, message: str, client_id: str):
        if client_id in self.active_connections:
            try:
                await self.active_connections[client_id].send_text(message)
            except Exception as e:
                logger.error(f"Error sending personal message to {client_id}: {e}")
                self.disconnect(client_id)

    async def broadcast(self, message: dict):
        if not self.active_connections:
            return
        
        # Tratar caso onde 'ai_metrics' pode ser uma corrotina não resolvida
        if "payload" in message and "ai_metrics" in message["payload"]:
            if asyncio.iscoroutine(message["payload"]["ai_metrics"]):
                logger.warning("ai_metrics era uma corrotina no broadcast, aguardando...")
                try:
                    message["payload"]["ai_metrics"] = await message["payload"]["ai_metrics"]
                except Exception as e_coro:
                    logger.error(f"Erro ao resolver corrotina ai_metrics para broadcast: {e_coro}", exc_info=True)
                    message["payload"]["ai_metrics"] = {"error": f"Erro ao resolver ai_metrics: {str(e_coro)}"}

        message_str = json.dumps(message)
        for client_id in list(self.active_connections.keys()):
            websocket = self.active_connections.get(client_id)
            if websocket:
                try:
                    await websocket.send_text(message_str)
                except Exception as e:
                    logger.error(f"Error broadcasting to client {client_id}: {e}")
                    self.disconnect(client_id)

manager = ConnectionManager()

# System metrics collector
class SystemMetrics:
    @staticmethod
    async def get_system_health():
        try:
            cpu_percent = await asyncio.to_thread(psutil.cpu_percent, interval=0.1)
            memory = await asyncio.to_thread(psutil.virtual_memory)
            disk = await asyncio.to_thread(psutil.disk_usage, '/')
            return {
                "cpu_usage": cpu_percent, "memory_usage": memory.percent,
                "memory_total": memory.total, "memory_available": memory.available,
                "disk_usage": disk.percent, "disk_total": disk.total, "disk_free": disk.free,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        except Exception as e:
            logger.error(f"Error collecting system metrics: {e}", exc_info=True)
            return {"error": str(e), "timestamp": datetime.now(timezone.utc).isoformat()}

    @staticmethod
    async def get_service_status():
        # Executar todos os health checks em paralelo
        results = await asyncio.gather(
            check_postgresql_health(), 
            check_redis_health(), 
            check_openai_health(),
            check_rabbitmq_health(),
            check_m1_health(),
            check_m2_health(), 
            check_m3_health(),
            check_m4_health(), 
            check_m5_health(),
            check_m6_health(), 
            check_m7_health(),
            check_m8_health(),
            check_m14_health(),
            return_exceptions=True  # Não falhar se um serviço der erro
        )
        
        # Desempacotar os resultados
        (db_h, redis_h, openai_h, rabbitmq_h, m1_h, m2_h, m3_h, 
         m4_h, m5_h, m6_h, m7_h, m8_h, m14_h) = results
        
        ts = datetime.now(timezone.utc).isoformat()
        
        # Função auxiliar para tratar exceções
        def safe_result(result, service_name):
            if isinstance(result, Exception):
                logger.error(f"Health check failed for {service_name}: {result}", exc_info=True)
                return {
                    "status": "unhealthy",
                    "response_time_ms": 0,
                    "error": str(result),
                    "details": f"Health check exception for {service_name}",
                    "last_check": ts
                }
            return {**result, "last_check": ts}
        
        return {
            "postgresql": safe_result(db_h, "PostgreSQL"),
            "redis": safe_result(redis_h, "Redis"),
            "openai_api": safe_result(openai_h, "OpenAI API"),
            "rabbitmq": safe_result(rabbitmq_h, "RabbitMQ"),
            "m1_drive_connector": safe_result(m1_h, "M1 Drive Connector"),
            "m2_orchestrator": safe_result(m2_h, "M2 Task Orchestrator"),
            "m3_extractor": safe_result(m3_h, "M3 Extractor"),
            "m4_translator": safe_result(m4_h, "M4 Translator"),
            "m5_db_browser": safe_result(m5_h, "M5 DB Browser"),
            "m6_vector_indexer": safe_result(m6_h, "M6 Vector Indexer"),
            "m7_diff_engine": safe_result(m7_h, "M7 Diff Engine"),
            "m8_api_gateway": safe_result(m8_h, "M8 API Gateway"),
            "m14_importek_bff": safe_result(m14_h, "M14 Importek BFF")
        }

    @staticmethod
    def get_ai_metrics_sync(db: Session):
        logger.info(f"Type of db in get_ai_metrics_sync: {type(db)}")
        try:
            total_conv = db.query(func.count(ChatSession.id)).scalar() or 0
            # Usar o enum SessionStatus.ACTIVE para garantir compatibilidade
            active_conv = db.query(func.count(ChatSession.id)).filter(ChatSession.status == SessionStatus.ACTIVE).scalar() or 0
            
            today_start = datetime.combine(datetime.now(timezone.utc).date(), datetime.min.time(), tzinfo=timezone.utc)
            
            api_calls = db.query(func.count(QueryLog.id)).filter(QueryLog.created_at >= today_start).scalar() or 0
            tokens_query = db.query(func.sum(QueryLog.tokens_used)).filter(QueryLog.created_at >= today_start, QueryLog.tokens_used.isnot(None))
            tokens = tokens_query.scalar() or 0
            
            avg_resp_time_ms_query = db.query(func.avg(QueryLog.execution_time_ms)).filter(
                QueryLog.created_at >= today_start,
                QueryLog.status == 'completed',
                QueryLog.execution_time_ms.isnot(None)
            )
            avg_resp_time_ms = avg_resp_time_ms_query.scalar() or 0.0
            avg_resp_time_s = round(avg_resp_time_ms / 1000.0, 2) if avg_resp_time_ms else 0.0

            failed_queries = db.query(func.count(QueryLog.id)).filter(
                QueryLog.created_at >= today_start,
                (QueryLog.status == 'failed') | (QueryLog.status == 'timeout')
            ).scalar() or 0
            error_rate = (failed_queries / api_calls) if api_calls > 0 else 0.0

            return {
                "total_conversations": total_conv, "active_conversations": active_conv,
                "avg_response_time_s": avg_resp_time_s, "total_tokens_used_today": tokens,
                "api_calls_today": api_calls, "error_rate_today": round(error_rate, 4),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        except Exception as e:
            logger.error(f"Error collecting AI metrics: {e}", exc_info=True)
            return {"error": str(e), "timestamp": datetime.now(timezone.utc).isoformat()}

@router.get("/", response_class=HTMLResponse, include_in_schema=False)
async def get_monitor_interface_route():
    try:
        monitor_html_path = Path("/app/app/monitoring/index.html") 
        if not monitor_html_path.exists():
            logger.error(f"Monitor interface HTML not found at {monitor_html_path}")
            raise HTTPException(status_code=404, detail="Monitor interface HTML not found.")
        return FileResponse(monitor_html_path)
    except Exception as e:
        logger.error(f"Error serving monitor interface: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error loading monitor interface: {str(e)}")

STATIC_FILES_DIR = Path("/app/app/monitoring/static")
if not STATIC_FILES_DIR.exists():
    logger.error(f"Diretório de arquivos estáticos NÃO ENCONTRADO em {STATIC_FILES_DIR}. Verifique o Dockerfile e a estrutura de arquivos.")

def collect_and_send_metrics_for_broadcast():
    """Helper to run synchronous DB operations in a thread for broadcasting."""
    db_generator = get_database_session()
    db_session: Optional[Session] = None
    try:
        db_session = next(db_generator)
        metrics = SystemMetrics.get_ai_metrics_sync(db_session)
        return metrics
    except Exception as e:
        logger.error(f"Erro ao coletar métricas de IA em thread: {e}", exc_info=True)
        return {"error": str(e), "timestamp": datetime.now(timezone.utc).isoformat()}
    finally:
        if db_session is not None: 
            try:
                db_session.close()
            except Exception as close_e:
                logger.error(f"Erro ao fechar sessão: {close_e}", exc_info=True)

async def broadcast_metrics_updates():
    while True:
        await asyncio.sleep(settings.monitor_update_interval_seconds) 
        if manager.active_connections:
            try:
                ai_metrics_payload = await asyncio.to_thread(collect_and_send_metrics_for_broadcast)
                system_health_payload = await SystemMetrics.get_system_health()
                service_status_payload = await SystemMetrics.get_service_status()
                
                update_data = {
                    "type": "metrics_update",
                    "payload": {
                        "system_health": system_health_payload,
                        "ai_metrics": ai_metrics_payload,
                        "service_status": service_status_payload,
                        "timestamp": datetime.now(timezone.utc).isoformat()
                    }
                }
                await manager.broadcast(update_data)
            except Exception as e:
                logger.error(f"Error in broadcast_metrics_updates task: {e}", exc_info=True)

@router.on_event("startup")
async def startup_event():
    asyncio.create_task(broadcast_metrics_updates())
    logger.info("Monitor background task started.")

@router.websocket("/ws")
async def websocket_endpoint_route(websocket: WebSocket, db: Session = Depends(get_database_session)):
    client_id = await manager.connect(websocket) 
    logger.info(f"Type of db in /ws for client {client_id}: {type(db)}")
    try:
        initial_system_health = await SystemMetrics.get_system_health()
        initial_service_status = await SystemMetrics.get_service_status()
        initial_ai_metrics = SystemMetrics.get_ai_metrics_sync(db) 

        initial_data = {
            "type": "initial_state",
            "payload": {
                "system_health": initial_system_health,
                "ai_metrics": initial_ai_metrics,
                "service_status": initial_service_status,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        }
        await manager.send_personal_message(json.dumps(initial_data), client_id)
        
        while True:
            await asyncio.sleep(settings.websocket_keep_alive_interval_seconds) 
            await websocket.send_text(json.dumps({"type": "keepalive"}))

    except WebSocketDisconnect:
        logger.info(f"WebSocket client {client_id} disconnected.")
    except Exception as e:
        logger.error(f"WebSocket error for client {client_id}: {e}", exc_info=True)
    finally:
        manager.disconnect(client_id)

@router.get("/api/services")
async def get_services_status_endpoint():
    logger.info("Acessando endpoint /api/services")
    statuses_dict = await SystemMetrics.get_service_status()
    # Retornar no formato que o JavaScript espera
    return {"services": statuses_dict}

@router.get("/api/metrics")
async def get_metrics_endpoint(db: Session = Depends(get_database_session)):
    logger.info(f"Type of db in /api/metrics endpoint: {type(db)}")
    try:
        system_health = await SystemMetrics.get_system_health()
        ai_metrics = SystemMetrics.get_ai_metrics_sync(db) 
        service_status = await SystemMetrics.get_service_status()
        
        return {
            "system_health": system_health,
            "ai_metrics": ai_metrics,
            "service_status": service_status,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
    except Exception as e:
        logger.error(f"Error in /api/metrics endpoint: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error collecting metrics")

@router.get("/api/audit/queries", response_model=List[QueryLogResponse])
async def get_audit_queries_endpoint(
    db: Session = Depends(get_database_session),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000)
):
    logger.info(f"Acessando endpoint /api/audit/queries. Skip: {skip}, Limit: {limit}")
    logger.info(f"Type of db in /api/audit/queries endpoint: {type(db)}")
    try:
        query_logs = db.query(QueryLog).order_by(QueryLog.created_at.desc()).offset(skip).limit(limit).all()
        return [QueryLogResponse.model_validate(log) for log in query_logs]
    except Exception as e:
        logger.error(f"Error retrieving audit logs: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error retrieving audit logs")

@router.get("/api/audit/messages", response_model=List[MessageResponse])
async def get_audit_messages_endpoint(
    db: Session = Depends(get_database_session),
    chat_session_id: Optional[uuid.UUID] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000)
):
    logger.info(f"Acessando endpoint /api/audit/messages. Session: {chat_session_id}, Skip: {skip}, Limit: {limit}")
    logger.info(f"Type of db in /api/audit/messages endpoint: {type(db)}")
    try:
        query = db.query(ChatSession)
        if chat_session_id:
            query = query.filter(ChatSession.id == chat_session_id)
        
        sessions = query.order_by(ChatSession.created_at.desc()).offset(skip).limit(limit).all()
        return [MessageResponse.model_validate(session) for session in sessions]
    except Exception as e:
        logger.error(f"Error retrieving audit messages: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error retrieving audit messages")
