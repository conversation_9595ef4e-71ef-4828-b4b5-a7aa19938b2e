"""
M10 AI Orchestrator - Chat Router

Router para endpoints de chat e conversação com IA.
Implementa funcionalidades de RAG (Retrieval-Augmented Generation).
"""

import uuid
import time
from typing import Optional, List, Dict, Any
from uuid import uuid4

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from sqlalchemy.orm import Session

from app.database import get_database_session
from app.services.llm_service import LLMService
from app.repositories.chat_session_repository import ChatSessionRepository
from app.repositories.message_repository import MessageRepository
from app.repositories.query_log_repository import QueryLogRepository
from app.models.query_log import QueryType, QueryStatus

# Configurar router
router = APIRouter()

# Schemas
class ChatRequest(BaseModel):
    message: str
    session_id: Optional[str] = None
    use_context: bool = True
    temperature: Optional[float] = 0.3
    max_tokens: Optional[int] = 1000

class ChatResponse(BaseModel):
    session_id: str
    response: str
    citations: List[Dict[str, Any]] = []
    metadata: Dict[str, Any] = {}

class ChatSessionInfo(BaseModel):
    session_id: str
    name: str
    status: str
    created_at: str
    message_count: int

# LLM Service será instanciado quando necessário

@router.post("/chat", response_model=ChatResponse)
async def process_chat_message(
    request: ChatRequest,
    db: Session = Depends(get_database_session)
):
    """
    Processar mensagem de chat com IA.
    
    Implementa RAG (Retrieval-Augmented Generation):
    1. Busca contexto relevante no M6 (se habilitado)
    2. Gera resposta usando LLM
    3. Salva conversa no banco
    4. Retorna resposta com metadados
    """
    start_time = time.time()
    session_id = request.session_id or str(uuid4())
    
    # Repositórios
    chat_repo = ChatSessionRepository(db)
    message_repo = MessageRepository(db)
    query_repo = QueryLogRepository(db)
    
    try:
        # Criar ou buscar sessão de chat
        chat_session = chat_repo.get_by_id(uuid.UUID(session_id))
        if not chat_session:
            chat_session = chat_repo.create_session(
                title=f"Chat {session_id[:8]}",
                model_name="gpt-4o",
                temperature=request.temperature or 0.3,
                max_tokens=request.max_tokens or 1000
            )
            session_id = str(chat_session.id)
        
        # Log da query
        query_log = query_repo.create_query_log(
            session_id=uuid.UUID(session_id),
            query_text=request.message,
            query_type=QueryType.CHAT,
            intent_classification="chat_message",
            intent_confidence=0.9
        )
        
        # Buscar contexto no M6 se habilitado
        context = []
        context_sources = []
        if request.use_context:
            # TODO: Implementar busca no M6 Vector Indexer
            # context = await search_vector_context(request.message)
            pass
        
        # Preparar mensagens para o LLM
        messages = [
            {"role": "user", "content": request.message}
        ]
        
        # Gerar resposta com LLM
        llm_service = LLMService()
        llm_response = llm_service.generate_response(
            messages=messages,
            temperature=request.temperature or 0.3,
            max_tokens=request.max_tokens or 1000,
            system_prompt="Você é um assistente especializado em documentos regulatórios e técnicos. Responda de forma precisa e útil."
        )
        
        # Salvar mensagem do usuário
        user_message = message_repo.create_message(
            session_id=uuid.UUID(session_id),
            role="user",
            content=request.message,
            sequence_number=1,  # TODO: Calcular sequência correta
            intent_classification="chat_message",
            intent_confidence=0.9,
            context_retrieved=request.use_context,
            context_sources=context_sources
        )
        
        # Salvar resposta do assistente
        assistant_message = message_repo.create_message(
            session_id=uuid.UUID(session_id),
            role="assistant",
            content=llm_response.content,
            sequence_number=2,  # TODO: Calcular sequência correta
            model_name=llm_response.model,
            temperature=request.temperature or 0.3,
            max_tokens=request.max_tokens or 1000,
            metadata={
                "tokens_used": llm_response.total_tokens,
                "cost_usd": llm_response.cost_usd,
                "response_time_ms": llm_response.response_time_ms
            }
        )
        
        # Atualizar query log com sucesso
        query_repo.update_query_log(
            query_log.id,
            status=QueryStatus.COMPLETED,
            response_text=llm_response.content,
            tokens_used=llm_response.total_tokens,
            cost_usd=llm_response.cost_usd,
            response_time_ms=llm_response.response_time_ms,
            context_chunks_count=len(context)
        )
        
        # Commit das mudanças
        db.commit()
        
        response_time_ms = (time.time() - start_time) * 1000
        
        return ChatResponse(
            session_id=session_id,
            response=llm_response.content,
            citations=[],  # TODO: Implementar citações
            metadata={
                "tokens_used": llm_response.total_tokens,
                "cost_usd": llm_response.cost_usd,
                "response_time_ms": response_time_ms,
                "context_chunks": len(context),
                "model": llm_response.model,
                "temperature": request.temperature or 0.3
            }
        )
        
    except Exception as e:
        # Atualizar query log com erro
        if 'query_log' in locals():
            query_repo.update_query_log(
                query_log.id,
                status=QueryStatus.FAILED,
                error_message=str(e)
            )
        
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Erro no chat: {str(e)}")

@router.get("/sessions", response_model=List[ChatSessionInfo])
async def list_chat_sessions(
    limit: int = 10,
    offset: int = 0,
    db: Session = Depends(get_database_session)
):
    """Listar sessões de chat recentes."""
    chat_repo = ChatSessionRepository(db)
    
    sessions = chat_repo.get_recent_sessions(limit=limit, offset=offset)
    
    return [
        ChatSessionInfo(
            session_id=str(session.id),
            name=session.name or f"Chat {str(session.id)[:8]}",
            status=session.status.value,
            created_at=session.created_at.isoformat(),
            message_count=len(session.messages)
        )
        for session in sessions
    ]

@router.get("/sessions/{session_id}")
async def get_chat_session(
    session_id: str,
    db: Session = Depends(get_database_session)
):
    """Obter detalhes de uma sessão de chat específica."""
    chat_repo = ChatSessionRepository(db)
    
    try:
        session = chat_repo.get_by_id(uuid.UUID(session_id))
        if not session:
            raise HTTPException(status_code=404, detail="Sessão não encontrada")
        
        return {
            "session_id": str(session.id),
            "name": session.name,
            "status": session.status.value,
            "created_at": session.created_at.isoformat(),
            "updated_at": session.updated_at.isoformat(),
            "messages": [
                {
                    "id": str(msg.id),
                    "role": msg.role,
                    "content": msg.content,
                    "created_at": msg.created_at.isoformat(),
                    "metadata": msg.extra_metadata or {}
                }
                for msg in session.messages
            ]
        }
        
    except ValueError:
        raise HTTPException(status_code=400, detail="ID de sessão inválido")
