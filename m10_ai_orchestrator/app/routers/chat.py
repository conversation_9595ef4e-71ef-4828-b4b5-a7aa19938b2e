"""
M10 AI Orchestrator - Chat Router

Router para endpoints de chat e conversação com IA.
Implementa funcionalidades de RAG (Retrieval-Augmented Generation).
"""

import uuid
import time
from typing import Optional, List, Dict, Any
from uuid import uuid4

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from sqlalchemy.orm import Session

from app.database import get_database_session
from app.services.llm_service import LLMService
from app.services.nl_query_processor import NLQueryProcessor, QueryType as NLQueryType
from app.services.data_analysis_service import DataAnalysisService
from app.services.report_generator import ReportGenerator
from app.repositories.chat_session_repository import ChatSessionRepository
from app.repositories.message_repository import MessageRepository
from app.repositories.query_log_repository import QueryLogRepository
from app.models.query_log import QueryType, QueryStatus

# Configurar router
router = APIRouter()

# Schemas
class ChatRequest(BaseModel):
    message: str
    session_id: Optional[str] = None
    use_context: bool = True
    temperature: Optional[float] = 0.3
    max_tokens: Optional[int] = 1000

class ChatResponse(BaseModel):
    session_id: str
    response: str
    citations: List[Dict[str, Any]] = []
    metadata: Dict[str, Any] = {}

class ChatSessionInfo(BaseModel):
    session_id: str
    name: str
    status: str
    created_at: str
    message_count: int

# LLM Service será instanciado quando necessário

@router.post("/chat", response_model=ChatResponse)
async def process_chat_message(
    request: ChatRequest,
    db: Session = Depends(get_database_session)
):
    """
    Processar mensagem de chat com IA.
    
    Implementa RAG (Retrieval-Augmented Generation):
    1. Busca contexto relevante no M6 (se habilitado)
    2. Gera resposta usando LLM
    3. Salva conversa no banco
    4. Retorna resposta com metadados
    """
    start_time = time.time()
    session_id = request.session_id or str(uuid4())
    
    # Repositórios
    chat_repo = ChatSessionRepository(db)
    message_repo = MessageRepository(db)
    query_repo = QueryLogRepository(db)
    
    try:
        # Criar ou buscar sessão de chat
        chat_session = chat_repo.get_by_id(uuid.UUID(session_id))
        if not chat_session:
            chat_session = chat_repo.create_session(
                title=f"Chat {session_id[:8]}"
            )
            session_id = str(chat_session.id)
        
        # Log da query
        query_log = query_repo.create_query_log(
            query_id=str(uuid4()),
            query_type="chat",
            original_query=request.message,
            session_id=uuid.UUID(session_id),
            detected_intent="chat_message",
            intent_confidence=0.9
        )
        
        # Processar consulta com NL Query Processor
        nl_processor = NLQueryProcessor()
        query_intent = nl_processor.process_query(request.message)

        # Verificar se é uma solicitação de análise/relatório
        if query_intent.query_type in [NLQueryType.PRODUCT_ANALYSIS, NLQueryType.VERSION_COMPARISON,
                                       NLQueryType.DATABASE_STATS, NLQueryType.DOCUMENT_SEARCH]:
            # Processar solicitação de análise
            analysis_result = await process_analysis_request(query_intent, db)

            # Retornar resposta com link para relatório
            return ChatResponse(
                session_id=session_id,
                response=analysis_result['response'],
                citations=[],
                metadata=analysis_result['metadata']
            )

        # Buscar contexto no M6 se habilitado
        context = []
        context_sources = []
        if request.use_context:
            # TODO: Implementar busca no M6 Vector Indexer
            # context = await search_vector_context(request.message)
            pass

        # Preparar mensagens para o LLM
        messages = [
            {"role": "user", "content": request.message}
        ]

        # Gerar resposta com LLM
        llm_service = LLMService()
        llm_response = llm_service.generate_response(
            messages=messages,
            temperature=request.temperature or 0.3,
            max_tokens=request.max_tokens or 1000,
            system_prompt="Você é um assistente especializado em documentos regulatórios e técnicos. Responda de forma precisa e útil."
        )
        
        # Salvar mensagem do usuário
        user_message = message_repo.create_message(
            session_id=uuid.UUID(session_id),
            role="user",
            content=request.message,
            sequence_number=1,  # TODO: Calcular sequência correta
            intent_classification="chat_message",
            intent_confidence=0.9,
            context_retrieved=request.use_context,
            context_sources=context_sources
        )
        
        # Salvar resposta do assistente
        assistant_message = message_repo.create_message(
            session_id=uuid.UUID(session_id),
            role="assistant",
            content=llm_response.content,
            sequence_number=2,  # TODO: Calcular sequência correta
            model_name=llm_response.model,
            temperature=request.temperature or 0.3,
            max_tokens=request.max_tokens or 1000,
            metadata={
                "tokens_used": llm_response.total_tokens,
                "cost_usd": llm_response.cost_usd,
                "response_time_ms": llm_response.processing_time_ms
            }
        )
        
        # Marcar query como concluída
        query_repo.complete_query(query_log.query_id)
        
        # Commit das mudanças
        db.commit()
        
        response_time_ms = (time.time() - start_time) * 1000
        
        return ChatResponse(
            session_id=session_id,
            response=llm_response.content,
            citations=[],  # TODO: Implementar citações
            metadata={
                "tokens_used": llm_response.total_tokens,
                "cost_usd": llm_response.cost_usd,
                "response_time_ms": response_time_ms,
                "context_chunks": len(context),
                "model": llm_response.model,
                "temperature": request.temperature or 0.3
            }
        )
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Erro no chat: {str(e)}")

@router.get("/sessions", response_model=List[ChatSessionInfo])
async def list_chat_sessions(
    limit: int = 10,
    offset: int = 0,
    db: Session = Depends(get_database_session)
):
    """Listar sessões de chat recentes."""
    chat_repo = ChatSessionRepository(db)

    sessions = chat_repo.get_active_sessions(limit=limit)
    
    return [
        ChatSessionInfo(
            session_id=str(session.id),
            name=session.name or f"Chat {str(session.id)[:8]}",
            status=session.status.value,
            created_at=session.created_at.isoformat(),
            message_count=0  # TODO: Implementar contagem de mensagens
        )
        for session in sessions
    ]

@router.get("/sessions/{session_id}")
async def get_chat_session(
    session_id: str,
    db: Session = Depends(get_database_session)
):
    """Obter detalhes de uma sessão de chat específica."""
    chat_repo = ChatSessionRepository(db)
    
    try:
        session = chat_repo.get_by_id(uuid.UUID(session_id))
        if not session:
            raise HTTPException(status_code=404, detail="Sessão não encontrada")
        
        return {
            "session_id": str(session.id),
            "name": session.name,
            "status": session.status.value,
            "created_at": session.created_at.isoformat(),
            "updated_at": session.updated_at.isoformat(),
            "messages": [
                {
                    "id": str(msg.id),
                    "role": msg.role,
                    "content": msg.content,
                    "created_at": msg.created_at.isoformat(),
                    "metadata": msg.extra_metadata or {}
                }
                for msg in session.messages
            ]
        }
        
    except ValueError:
        raise HTTPException(status_code=400, detail="ID de sessão inválido")


async def process_analysis_request(query_intent, db: Session) -> Dict[str, Any]:
    """
    Processa solicitações de análise e gera relatórios.

    Args:
        query_intent: Intenção extraída da consulta
        db: Sessão do banco de dados

    Returns:
        Dicionário com resposta e metadados
    """
    try:
        analysis_service = DataAnalysisService(db)
        report_generator = ReportGenerator()

        if query_intent.query_type == NLQueryType.PRODUCT_ANALYSIS:
            # Análise de produto
            product_name = query_intent.entities.get('product_name')
            if not product_name:
                return {
                    'response': "❌ Não consegui identificar o nome do produto. Por favor, especifique qual produto você gostaria de analisar.",
                    'metadata': {'error': 'missing_product_name'}
                }

            # Realizar análise
            analysis = analysis_service.analyze_product_versions(product_name)

            # Gerar relatório
            report_info = report_generator.generate_product_analysis_report(analysis, True)

            response = f"""✅ **Análise do Produto: {product_name}**

📊 **Resumo:**
- **{analysis.total_documents}** documentos encontrados
- **{analysis.total_versions}** versões diferentes
- **{len(analysis.languages)}** idiomas detectados
- Período: {analysis.date_range[0].strftime('%d/%m/%Y')} a {analysis.date_range[1].strftime('%d/%m/%Y')}

🔗 **Relatório Completo:** [Visualizar Relatório]({report_info['url']})

📄 **Exportar:** [Download PDF]({report_info['url']}/export-pdf)

O relatório contém análise detalhada de todos os materiais relacionados ao produto, incluindo comparações entre versões e estatísticas completas."""

            return {
                'response': response,
                'metadata': {
                    'report_id': report_info['id'],
                    'report_url': report_info['url'],
                    'analysis_type': 'product_analysis',
                    'product_name': product_name,
                    'total_documents': analysis.total_documents
                }
            }

        elif query_intent.query_type == NLQueryType.VERSION_COMPARISON:
            # Comparação de versões
            version_a = query_intent.entities.get('version_a')
            version_b = query_intent.entities.get('version_b')

            if not version_a or not version_b:
                return {
                    'response': "❌ Não consegui identificar as versões para comparação. Por favor, especifique as duas versões que deseja comparar (ex: 'compare versão v1.0 com v2.0').",
                    'metadata': {'error': 'missing_versions'}
                }

            # Tentar extrair nome do documento (pode estar implícito)
            document_name = query_intent.entities.get('document_name', 'documento')

            # Realizar comparação
            comparison = analysis_service.compare_document_versions(
                document_name, version_a, version_b
            )

            # Gerar relatório
            report_info = report_generator.generate_comparison_report(comparison)

            response = f"""✅ **Comparação de Versões: {version_a} vs {version_b}**

📊 **Resultado:**
- **Similaridade:** {comparison.similarity_score * 100:.1f}%
- **Diferenças encontradas:** {comparison.differences_count}
- **Produto:** {comparison.product_name}

📝 **Resumo:** {comparison.summary}

🔗 **Relatório Completo:** [Visualizar Comparação]({report_info['url']})

📄 **Exportar:** [Download PDF]({report_info['url']}/export-pdf)

O relatório detalha todas as mudanças, adições e remoções entre as versões."""

            return {
                'response': response,
                'metadata': {
                    'report_id': report_info['id'],
                    'report_url': report_info['url'],
                    'analysis_type': 'version_comparison',
                    'version_a': version_a,
                    'version_b': version_b,
                    'similarity_score': comparison.similarity_score
                }
            }

        elif query_intent.query_type == NLQueryType.DATABASE_STATS:
            # Estatísticas da base de dados
            stats = analysis_service.get_database_statistics()

            response = f"""✅ **Estatísticas da Base de Dados**

📊 **Resumo Geral:**
- **{stats['total_documents']}** documentos importados
- **{stats['total_chunks']}** chunks de texto processados
- **{stats['total_versions']}** versões de documentos
- **{stats['total_languages']}** idiomas detectados
- **Tamanho médio dos chunks:** {stats['avg_chunk_size']} caracteres

💾 **Informações Técnicas:**
- Base de dados ativa e funcional
- Última atualização: {stats.get('last_updated', 'N/A')}
- Sistema de embeddings configurado

🔍 **Capacidades Disponíveis:**
- ✅ Busca semântica por conteúdo
- ✅ Análise comparativa entre versões
- ✅ Relatórios detalhados por produto
- ✅ Exportação para PDF

💡 **Próximos Passos:**
Posso ajudar com análises específicas de produtos, comparações entre versões, ou busca por conteúdos específicos. Basta me dizer o que você gostaria de analisar!"""

            return {
                'response': response,
                'metadata': {
                    'analysis_type': 'database_stats',
                    'total_documents': stats['total_documents'],
                    'total_chunks': stats['total_chunks'],
                    'total_versions': stats['total_versions'],
                    'total_languages': stats['total_languages']
                }
            }

        elif query_intent.query_type == NLQueryType.DOCUMENT_SEARCH:
            # Busca de documentos ou produtos
            search_term = query_intent.entities.get('search_term', query_intent.original_query)

            # Se a pergunta é sobre produtos disponíveis, listar produtos
            if any(word in search_term.lower() for word in ['produtos', 'product', 'quais', 'que', 'temos']):
                products_info = analysis_service.get_available_products()

                if not products_info['products']:
                    response = """❌ **Nenhum produto encontrado**

📊 **Base de dados:** Não há produtos identificados na base de dados atual."""
                else:
                    products_list = []
                    for product in products_info['products'][:10]:  # Limitar a 10 produtos
                        products_list.append(
                            f"- **{product['name']}**: {product['document_count']} documentos, "
                            f"{product['version_count']} versões (último: {product['last_import']})"
                        )

                    response = f"""✅ **Produtos Disponíveis na Base de Dados**

📊 **Resumo:**
- **{products_info['total_products']}** produtos identificados
- **{products_info['total_documents']}** documentos totais

🏭 **Lista de Produtos:**
{chr(10).join(products_list)}

💡 **Próximos Passos:**
Posso fazer análises detalhadas de qualquer produto específico. Basta me dizer qual produto você gostaria de analisar!"""

                return {
                    'response': response,
                    'metadata': {
                        'analysis_type': 'products_list',
                        'total_products': products_info['total_products'],
                        'total_documents': products_info['total_documents']
                    }
                }
            else:
                # Busca por documentos específicos
                documents = analysis_service.search_documents_by_product(search_term)

                if not documents:
                    response = f"""❌ **Nenhum documento encontrado**

🔍 **Termo buscado:** "{search_term}"

💡 **Sugestões:**
- Verifique a ortografia do termo
- Tente termos mais gerais
- Use palavras-chave relacionadas ao produto ou conteúdo

📊 **Dica:** Use "quais produtos temos" para ver todos os produtos disponíveis"""
                else:
                    response = f"""✅ **Documentos Encontrados**

🔍 **Termo:** "{search_term}"
📊 **Encontrados:** {len(documents)} documentos

📄 **Documentos Relevantes:**
{chr(10).join([f"- **{doc.document_name}** (v{doc.version_tag})" +
                       f" - {doc.created_at.strftime('%d/%m/%Y')}"
                       for doc in documents[:10]])}

💡 **Posso ajudar com:**
- Análise detalhada de qualquer documento específico
- Comparação entre versões
- Relatórios por produto"""

                return {
                    'response': response,
                    'metadata': {
                        'analysis_type': 'document_search',
                        'search_term': search_term,
                        'results_count': len(documents)
                    }
                }

        else:
            return {
                'response': "❌ Tipo de análise não suportado. Posso ajudar com análise de produtos, comparação de versões, estatísticas da base de dados ou busca de documentos.",
                'metadata': {'error': 'unsupported_analysis_type'}
            }

    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Erro ao processar análise: {e}")
        return {
            'response': f"❌ Erro ao processar análise: {str(e)}. Tente reformular sua solicitação.",
            'metadata': {'error': str(e)}
        }
