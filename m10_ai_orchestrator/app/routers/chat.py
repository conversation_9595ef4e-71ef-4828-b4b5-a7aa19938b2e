"""
M10 AI Orchestrator - Chat Router

Router para endpoints de chat e conversação com IA.
Implementa funcionalidades de RAG (Retrieval-Augmented Generation).
"""

import uuid
import time
import logging
from datetime import datetime
from typing import Optional, List, Dict, Any
from uuid import uuid4

# Configurar logger
logger = logging.getLogger(__name__)

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from sqlalchemy.orm import Session

from app.database import get_database_session
from app.services.llm_service import LLMService
from app.services.nl_query_processor import NLQueryProcessor, QueryType as NLQueryType
from app.services.data_analysis_service import DataAnalysisService
from app.services.report_generator import ReportGenerator
from app.services.semantic_search_service import SemanticSearchService
from app.repositories.chat_session_repository import ChatSessionRepository
from app.repositories.message_repository import MessageRepository
from app.repositories.query_log_repository import QueryLogRepository
from app.models.query_log import QueryType, QueryStatus
from sqlalchemy import text

# Configurar router
router = APIRouter()

# Schemas
class ChatRequest(BaseModel):
    message: str
    session_id: Optional[str] = None
    use_context: bool = True
    temperature: Optional[float] = 0.3
    max_tokens: Optional[int] = 1000

class ChatResponse(BaseModel):
    session_id: str
    response: str
    citations: List[Dict[str, Any]] = []
    metadata: Dict[str, Any] = {}

class ChatSessionInfo(BaseModel):
    session_id: str
    name: str
    status: str
    created_at: str
    message_count: int

# LLM Service será instanciado quando necessário

@router.post("/chat/rag", response_model=ChatResponse)
async def rag_chat_endpoint(
    request: ChatRequest,
    db: Session = Depends(get_database_session)
) -> ChatResponse:
    """
    🧠 Chat RAG Inteligente - Farejador de Informações

    Transforma conversas naturais em buscas poderosas usando:
    - Busca semântica com embeddings vetoriais
    - Recuperação de contexto relevante
    - Geração de respostas contextualizadas com LLM
    """
    try:
        logger.info(f"🧠 Chat RAG iniciado - Query: '{request.message[:100]}...'")

        # Inicializar serviços
        from app.services.semantic_search_service import SemanticSearchService
        from app.services.llm_service import LLMService

        semantic_search = SemanticSearchService()
        llm_service = LLMService()

        # 1. Busca semântica inteligente na base RAG
        search_results = semantic_search.hybrid_search(
            query=request.message,
            limit=10,
            semantic_weight=0.8,  # Priorizar busca semântica
            db_session=db  # Passar sessão do banco PostgreSQL
        )

        # 2. Construir contexto a partir dos resultados
        context_chunks = []
        sources = []

        for result in search_results:
            context_chunks.append({
                'text': result['text'],
                'document': result['original_name'],
                'similarity': result['similarity_score'],
                'metadata': result.get('document_metadata', {})
            })

            sources.append({
                'document': result['original_name'],
                'chunk_id': result['chunk_id'],
                'similarity': result['similarity_score']
            })

        # 3. Gerar resposta contextualizada com LLM
        if context_chunks:
            # Construir prompt RAG
            context_text = "\n\n".join([
                f"[Documento: {chunk['document']} | Similaridade: {chunk['similarity']:.2f}]\n{chunk['text']}"
                for chunk in context_chunks[:5]  # Top 5 resultados
            ])

            system_prompt = f"""Você é um assistente RAG especializado em análise de documentos e dados.
Sua função é ser um "farejador de informações" inteligente que transforma conversas em buscas poderosas.

CONTEXTO ENCONTRADO NA BASE DE DADOS:
{context_text}

INSTRUÇÕES:
1. Use APENAS as informações do contexto fornecido
2. Seja preciso e detalhado nas respostas
3. Cite as fontes dos documentos quando relevante
4. Se a pergunta não puder ser respondida com o contexto, diga claramente
5. Forneça insights e análises baseadas nos dados encontrados
6. Responda em português brasileiro

Transforme esta conversa em uma busca poderosa e forneça uma resposta inteligente."""

            response = llm_service.simple_completion(
                prompt=request.message,
                system_prompt=system_prompt,
                temperature=0.3,
                max_tokens=1000
            )

            response_text = response if response else "Não foi possível gerar uma resposta baseada no contexto encontrado."

        else:
            # Nenhum resultado encontrado
            response_text = f"""🔍 **Busca Realizada**: "{request.message}"

❌ **Nenhuma informação encontrada** na base de dados RAG que corresponda à sua consulta.

💡 **Sugestões**:
- Tente reformular a pergunta com termos mais específicos
- Verifique se os documentos relacionados foram processados
- Use palavras-chave diferentes ou sinônimos

🧠 **Como funciona**: Este chat é um farejador inteligente que busca semanticamente em toda a base de documentos usando embeddings vetoriais."""

            sources = []

        # 4. Criar resposta estruturada
        chat_response = ChatResponse(
            response=response_text,
            session_id=request.session_id or str(uuid4()),
            citations=sources,
            metadata={
                'search_type': 'rag_semantic',
                'results_found': len(search_results),
                'query_processed': request.message,
                'timestamp': datetime.utcnow().isoformat()
            }
        )

        logger.info(f"✅ Chat RAG concluído - {len(search_results)} resultados encontrados")
        return chat_response

    except Exception as e:
        logger.error(f"❌ Erro no chat RAG: {e}")
        return ChatResponse(
            response=f"❌ Erro interno no sistema RAG: {str(e)}",
            session_id=request.session_id or str(uuid4()),
            citations=[],
            metadata={'error': str(e)}
        )

@router.post("/chat", response_model=ChatResponse)
async def process_chat_message(
    request: ChatRequest,
    db: Session = Depends(get_database_session)
):
    """
    Processar mensagem de chat com IA.
    
    Implementa RAG (Retrieval-Augmented Generation):
    1. Busca contexto relevante no M6 (se habilitado)
    2. Gera resposta usando LLM
    3. Salva conversa no banco
    4. Retorna resposta com metadados
    """
    start_time = time.time()
    session_id = request.session_id or str(uuid4())
    
    # Repositórios
    chat_repo = ChatSessionRepository(db)
    message_repo = MessageRepository(db)
    query_repo = QueryLogRepository(db)
    
    try:
        # Criar ou buscar sessão de chat
        chat_session = chat_repo.get_by_id(uuid.UUID(session_id))
        if not chat_session:
            chat_session = chat_repo.create_session(
                title=f"Chat {session_id[:8]}"
            )
            session_id = str(chat_session.id)
        
        # Log da query
        query_log = query_repo.create_query_log(
            query_id=str(uuid4()),
            query_type="chat",
            original_query=request.message,
            session_id=uuid.UUID(session_id),
            detected_intent="chat_message",
            intent_confidence=0.9
        )
        
        # Processar consulta com NL Query Processor
        nl_processor = NLQueryProcessor()
        query_intent = nl_processor.process_query(request.message)

        # Verificar se é uma solicitação de análise/relatório
        if query_intent.query_type in [NLQueryType.PRODUCT_ANALYSIS, NLQueryType.VERSION_COMPARISON,
                                       NLQueryType.DATABASE_STATS, NLQueryType.DETAILED_DATABASE_ANALYSIS,
                                       NLQueryType.DOCUMENT_SEARCH, NLQueryType.PRODUCT_LIST]:
            # Processar solicitação de análise
            analysis_result = await process_analysis_request(query_intent, db)

            # Retornar resposta com link para relatório
            return ChatResponse(
                session_id=session_id,
                response=analysis_result['response'],
                citations=[],
                metadata=analysis_result['metadata']
            )

        # Buscar contexto no M6 se habilitado
        context = []
        context_sources = []
        if request.use_context:
            # TODO: Implementar busca no M6 Vector Indexer
            # context = await search_vector_context(request.message)
            pass

        # Preparar mensagens para o LLM
        messages = [
            {"role": "user", "content": request.message}
        ]

        # Gerar resposta com LLM
        llm_service = LLMService()
        llm_response = llm_service.generate_response(
            messages=messages,
            temperature=request.temperature or 0.3,
            max_tokens=request.max_tokens or 1000,
            system_prompt="Você é um assistente especializado em documentos regulatórios e técnicos. Responda de forma precisa e útil."
        )
        
        # Salvar mensagem do usuário
        user_message = message_repo.create_message(
            session_id=uuid.UUID(session_id),
            role="user",
            content=request.message,
            sequence_number=1,  # TODO: Calcular sequência correta
            intent_classification="chat_message",
            intent_confidence=0.9,
            context_retrieved=request.use_context,
            context_sources=context_sources
        )
        
        # Salvar resposta do assistente
        assistant_message = message_repo.create_message(
            session_id=uuid.UUID(session_id),
            role="assistant",
            content=llm_response.content,
            sequence_number=2,  # TODO: Calcular sequência correta
            model_name=llm_response.model,
            temperature=request.temperature or 0.3,
            max_tokens=request.max_tokens or 1000,
            metadata={
                "tokens_used": llm_response.total_tokens,
                "cost_usd": llm_response.cost_usd,
                "response_time_ms": llm_response.processing_time_ms
            }
        )
        
        # Marcar query como concluída
        query_repo.complete_query(query_log.query_id)
        
        # Commit das mudanças
        db.commit()
        
        response_time_ms = (time.time() - start_time) * 1000
        
        return ChatResponse(
            session_id=session_id,
            response=llm_response.content,
            citations=[],  # TODO: Implementar citações
            metadata={
                "tokens_used": llm_response.total_tokens,
                "cost_usd": llm_response.cost_usd,
                "response_time_ms": response_time_ms,
                "context_chunks": len(context),
                "model": llm_response.model,
                "temperature": request.temperature or 0.3
            }
        )
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Erro no chat: {str(e)}")

@router.get("/sessions", response_model=List[ChatSessionInfo])
async def list_chat_sessions(
    limit: int = 10,
    offset: int = 0,
    db: Session = Depends(get_database_session)
):
    """Listar sessões de chat recentes."""
    chat_repo = ChatSessionRepository(db)

    sessions = chat_repo.get_active_sessions(limit=limit)
    
    return [
        ChatSessionInfo(
            session_id=str(session.id),
            name=session.name or f"Chat {str(session.id)[:8]}",
            status=session.status.value,
            created_at=session.created_at.isoformat(),
            message_count=0  # TODO: Implementar contagem de mensagens
        )
        for session in sessions
    ]

@router.get("/sessions/{session_id}")
async def get_chat_session(
    session_id: str,
    db: Session = Depends(get_database_session)
):
    """Obter detalhes de uma sessão de chat específica."""
    chat_repo = ChatSessionRepository(db)
    
    try:
        session = chat_repo.get_by_id(uuid.UUID(session_id))
        if not session:
            raise HTTPException(status_code=404, detail="Sessão não encontrada")
        
        return {
            "session_id": str(session.id),
            "name": session.name,
            "status": session.status.value,
            "created_at": session.created_at.isoformat(),
            "updated_at": session.updated_at.isoformat(),
            "messages": [
                {
                    "id": str(msg.id),
                    "role": msg.role,
                    "content": msg.content,
                    "created_at": msg.created_at.isoformat(),
                    "metadata": msg.extra_metadata or {}
                }
                for msg in session.messages
            ]
        }
        
    except ValueError:
        raise HTTPException(status_code=400, detail="ID de sessão inválido")


async def process_analysis_request(query_intent, db: Session) -> Dict[str, Any]:
    """
    Processa solicitações de análise e gera relatórios.

    Args:
        query_intent: Intenção extraída da consulta
        db: Sessão do banco de dados

    Returns:
        Dicionário com resposta e metadados
    """
    try:
        analysis_service = DataAnalysisService(db)
        report_generator = ReportGenerator()

        if query_intent.query_type == NLQueryType.PRODUCT_ANALYSIS:
            # Análise de produto
            product_name = query_intent.entities.get('product_name')
            if not product_name:
                return {
                    'response': "❌ Não consegui identificar o nome do produto. Por favor, especifique qual produto você gostaria de analisar.",
                    'metadata': {'error': 'missing_product_name'}
                }

            # Realizar análise
            analysis = analysis_service.analyze_product_versions(product_name)

            # Gerar relatório
            report_info = report_generator.generate_product_analysis_report(analysis, True)

            response = f"""✅ **Análise do Produto: {product_name}**

📊 **Resumo:**
- **{analysis.total_documents}** documentos encontrados
- **{analysis.total_versions}** versões diferentes
- **{len(analysis.languages)}** idiomas detectados
- Período: {analysis.date_range[0].strftime('%d/%m/%Y')} a {analysis.date_range[1].strftime('%d/%m/%Y')}

🔗 **Relatório Completo:** [Visualizar Relatório]({report_info['url']})

📄 **Exportar:** [Download PDF]({report_info['url']}/export-pdf)

O relatório contém análise detalhada de todos os materiais relacionados ao produto, incluindo comparações entre versões e estatísticas completas."""

            return {
                'response': response,
                'metadata': {
                    'report_id': report_info['id'],
                    'report_url': report_info['url'],
                    'analysis_type': 'product_analysis',
                    'product_name': product_name,
                    'total_documents': analysis.total_documents
                }
            }

        elif query_intent.query_type == NLQueryType.VERSION_COMPARISON:
            # Comparação de versões
            version_a = query_intent.entities.get('version_a')
            version_b = query_intent.entities.get('version_b')

            if not version_a or not version_b:
                return {
                    'response': "❌ Não consegui identificar as versões para comparação. Por favor, especifique as duas versões que deseja comparar (ex: 'compare versão v1.0 com v2.0').",
                    'metadata': {'error': 'missing_versions'}
                }

            # Tentar extrair nome do documento (pode estar implícito)
            document_name = query_intent.entities.get('document_name', 'documento')

            # Realizar comparação
            comparison = analysis_service.compare_document_versions(
                document_name, version_a, version_b
            )

            # Gerar relatório
            report_info = report_generator.generate_comparison_report(comparison)

            response = f"""✅ **Comparação de Versões: {version_a} vs {version_b}**

📊 **Resultado:**
- **Similaridade:** {comparison.similarity_score * 100:.1f}%
- **Diferenças encontradas:** {comparison.differences_count}
- **Produto:** {comparison.product_name}

📝 **Resumo:** {comparison.summary}

🔗 **Relatório Completo:** [Visualizar Comparação]({report_info['url']})

📄 **Exportar:** [Download PDF]({report_info['url']}/export-pdf)

O relatório detalha todas as mudanças, adições e remoções entre as versões."""

            return {
                'response': response,
                'metadata': {
                    'report_id': report_info['id'],
                    'report_url': report_info['url'],
                    'analysis_type': 'version_comparison',
                    'version_a': version_a,
                    'version_b': version_b,
                    'similarity_score': comparison.similarity_score
                }
            }

        elif query_intent.query_type == NLQueryType.DATABASE_STATS:
            # Estatísticas da base de dados
            stats = analysis_service.get_database_statistics()

            response = f"""✅ **Estatísticas da Base de Dados**

📊 **Resumo Geral:**
- **{stats['total_documents']}** documentos importados
- **{stats['total_chunks']}** chunks de texto processados
- **{stats['total_versions']}** versões de documentos
- **{stats['total_languages']}** idiomas detectados
- **Tamanho médio dos chunks:** {stats['avg_chunk_size']} caracteres

💾 **Informações Técnicas:**
- Base de dados ativa e funcional
- Última atualização: {stats.get('last_updated', 'N/A')}
- Sistema de embeddings configurado

🔍 **Capacidades Disponíveis:**
- ✅ Busca semântica por conteúdo
- ✅ Análise comparativa entre versões
- ✅ Relatórios detalhados por produto
- ✅ Exportação para PDF

💡 **Próximos Passos:**
Posso ajudar com análises específicas de produtos, comparações entre versões, ou busca por conteúdos específicos. Basta me dizer o que você gostaria de analisar!"""

            return {
                'response': response,
                'metadata': {
                    'analysis_type': 'database_stats',
                    'total_documents': stats['total_documents'],
                    'total_chunks': stats['total_chunks'],
                    'total_versions': stats['total_versions'],
                    'total_languages': stats['total_languages']
                }
            }

        elif query_intent.query_type == NLQueryType.DETAILED_DATABASE_ANALYSIS:
            # Análise detalhada da base de dados
            detailed_stats = analysis_service.get_detailed_database_analysis()

            if 'error' in detailed_stats:
                response = f"❌ **Erro na Análise Detalhada:** {detailed_stats['error']}"
            else:
                # Usar a estrutura simplificada retornada pela função
                doc_types = detailed_stats['document_types']
                chunk_dist = detailed_stats['chunk_distribution']
                temporal = detailed_stats['temporal_analysis']
                lang_dist = detailed_stats['language_distribution']
                top_products = detailed_stats['top_products']
                volume = detailed_stats['volume_analysis']

                response = f"""📊 **Análise Detalhada da Base de Dados**

🗂️ **Resumo Geral:**
- **{doc_types['total_documents']}** documentos importados
- **{chunk_dist['total_chunks']}** chunks de texto processados
- **{temporal['total_versions']}** versões de documentos
- **{lang_dist['total_languages']}** idiomas detectados
- **Tamanho médio dos chunks:** {chunk_dist['avg_chunk_size']} caracteres

📁 **Tipos de Documentos:**
- **PDF:** {doc_types['pdf_documents']} documentos
- **Word:** {doc_types['word_documents']} documentos
- **Excel:** {doc_types['excel_documents']} documentos
- **Texto:** {doc_types['text_documents']} documentos

📏 **Distribuição por Tamanho:**
- **Chunks pequenos** (<500 chars): {chunk_dist['small_chunks']}
- **Chunks médios** (500-1500 chars): {chunk_dist['medium_chunks']}
- **Chunks grandes** (>1500 chars): {chunk_dist['large_chunks']}

💾 **Volume de Dados:**
- **Tamanho total:** {volume['total_content_size']} caracteres
- **Tamanho médio por documento:** {volume['avg_document_size']} caracteres

🌐 **Análise de Idiomas:**
- **Conteúdo em Português:** {lang_dist['portuguese_content']} chunks
- **Conteúdo em Inglês:** {lang_dist['english_content']} chunks

📅 **Informações Temporais:**"""

                if temporal['oldest_document']:
                    response += f"\n- **Documento mais antigo:** {temporal['oldest_document']}"
                if temporal['newest_document']:
                    response += f"\n- **Documento mais recente:** {temporal['newest_document']}"

                if not temporal['oldest_document'] and not temporal['newest_document']:
                    response += f"\n- {detailed_stats['message']}"

                response += f"\n\n🏆 **Top Produtos por Volume:**"
                if top_products:
                    for i, product in enumerate(top_products[:5], 1):
                        response += f"\n{i}. **{product['product_name']}**: {product['document_count']} docs"
                else:
                    response += f"\n- Nenhum produto encontrado na base"

                response += f"\n\n💡 **Próximos Passos:**\nPosso fazer análises específicas de qualquer produto, comparar versões, ou buscar conteúdos específicos. Basta me dizer o que você gostaria de analisar!"

            return {
                'response': response,
                'metadata': {
                    'analysis_type': 'detailed_database_analysis',
                    'total_documents': detailed_stats.get('document_types', {}).get('total_documents', 0),
                    'total_chunks': detailed_stats.get('chunk_distribution', {}).get('total_chunks', 0),
                    'document_types': detailed_stats.get('document_types', {}),
                    'top_products': detailed_stats.get('top_products', [])
                }
            }

        elif query_intent.query_type == NLQueryType.PRODUCT_LIST:
            # Lista de produtos disponíveis
            products_data = analysis_service.get_available_products()

            if products_data.get('products'):
                product_list = []
                for product_info in products_data['products']:
                    product_list.append(
                        f"- **{product_info['name']}**: {product_info['document_count']} documentos, "
                        f"{product_info['version_count']} versões (último: {product_info['last_import']})"
                    )

                response = f"""✅ **Produtos Disponíveis na Base de Dados**

📊 **Resumo:**
- **{products_data['total_products']}** produtos identificados
- **{products_data['total_documents']}** documentos totais

🏭 **Lista de Produtos:**
{chr(10).join(product_list)}

💡 **Próximos Passos:**
Posso fazer análises detalhadas de qualquer produto específico. Basta me dizer qual produto você gostaria de analisar!"""
            else:
                response = "❌ **Nenhum produto encontrado na base de dados.**"

            return {
                'response': response,
                'metadata': {
                    'analysis_type': 'product_list',
                    'total_products': products_data.get('total_products', 0),
                    'total_documents': products_data.get('total_documents', 0)
                }
            }

        elif query_intent.query_type == NLQueryType.DOCUMENT_SEARCH:
            # Busca de documentos ou produtos
            search_term = query_intent.entities.get('search_term', query_intent.original_query)

            # Se a pergunta é sobre produtos disponíveis, listar produtos
            if any(word in search_term.lower() for word in ['produtos', 'product', 'quais', 'que', 'temos']):
                products_info = analysis_service.get_available_products()

                if not products_info['products']:
                    response = """❌ **Nenhum produto encontrado**

📊 **Base de dados:** Não há produtos identificados na base de dados atual."""
                else:
                    products_list = []
                    for product in products_info['products'][:10]:  # Limitar a 10 produtos
                        products_list.append(
                            f"- **{product['name']}**: {product['document_count']} documentos, "
                            f"{product['version_count']} versões (último: {product['last_import']})"
                        )

                    response = f"""✅ **Produtos Disponíveis na Base de Dados**

📊 **Resumo:**
- **{products_info['total_products']}** produtos identificados
- **{products_info['total_documents']}** documentos totais

🏭 **Lista de Produtos:**
{chr(10).join(products_list)}

💡 **Próximos Passos:**
Posso fazer análises detalhadas de qualquer produto específico. Basta me dizer qual produto você gostaria de analisar!"""

                return {
                    'response': response,
                    'metadata': {
                        'analysis_type': 'products_list',
                        'total_products': products_info['total_products'],
                        'total_documents': products_info['total_documents']
                    }
                }
            else:
                # Usar busca semântica híbrida (semântica + textual)
                import logging
                logging.getLogger(__name__).info(f"🔍 Realizando busca semântica para: '{search_term}'")

                # Primeiro tentar busca semântica
                semantic_results = analysis_service.semantic_search_documents(search_term, limit=10)

                if semantic_results and semantic_results.get('documents'):
                    # Converter resultados semânticos para formato DocumentAnalysis
                    from app.services.data_analysis_service import DocumentAnalysis
                    documents = []
                    for doc in semantic_results['documents']:
                        doc_analysis = DocumentAnalysis(
                            document_id=doc['document_id'],
                            original_name=doc['original_name'],
                            version_tag=doc.get('version_tag', 'v1'),
                            chunk_count=doc.get('chunk_count', 0),
                            word_count=doc.get('word_count', 0),
                            language_detected=doc.get('language_detected', 'unknown'),
                            created_at=doc.get('created_at'),
                            metadata=doc.get('metadata', {}),
                            content_sample=doc.get('content_sample', '')
                        )
                        documents.append(doc_analysis)

                    logging.getLogger(__name__).info(f"✅ Busca semântica encontrou {len(documents)} documentos")
                else:
                    # Fallback para busca textual se semântica não retornar resultados
                    logging.getLogger(__name__).info(f"🔄 Fallback para busca textual: '{search_term}'")
                    documents = analysis_service.search_documents_by_product(search_term)

                # Determinar se foi busca semântica ou textual
                search_type = "Busca Semântica" if semantic_results and semantic_results.get('documents') else "Busca Textual"
                analysis_type = "document_search_semantic" if semantic_results and semantic_results.get('documents') else "document_search_textual"

                if not documents:
                        response = f"""❌ **Nenhum documento encontrado ({search_type})**

🔍 **Termo buscado:** "{search_term}"

💡 **Sugestões:**
- Verifique a ortografia do termo
- Tente termos mais gerais
- Use palavras-chave relacionadas ao produto ou conteúdo

📊 **Dica:** Use "quais produtos temos" para ver todos os produtos disponíveis"""

                        return {
                            'response': response,
                            'metadata': {
                                'analysis_type': analysis_type,
                                'search_term': search_term,
                                'results_count': 0,
                                'semantic_search_used': bool(semantic_results and semantic_results.get('documents'))
                            }
                        }
                else:
                    response = f"""✅ **Documentos Encontrados ({search_type})**

🔍 **Termo:** "{search_term}"
📊 **Encontrados:** {len(documents)} documentos

📄 **Documentos Relevantes:**
{chr(10).join([f"- **{doc.original_name}** (v{doc.version_tag})" +
                               f" - {doc.created_at.strftime('%d/%m/%Y') if hasattr(doc.created_at, 'strftime') else str(doc.created_at)}"
                               for doc in documents[:10]])}

💡 **Posso ajudar com:**
- Análise detalhada de qualquer documento específico
- Comparação entre versões
- Relatórios por produto"""

                return {
                    'response': response,
                    'metadata': {
                        'analysis_type': analysis_type,
                        'search_term': search_term,
                        'results_count': len(documents) if documents else 0,
                        'semantic_search_used': bool(semantic_results and semantic_results.get('documents'))
                    }
                }

        else:
            return {
                'response': "❌ Tipo de análise não suportado. Posso ajudar com análise de produtos, comparação de versões, estatísticas da base de dados ou busca de documentos.",
                'metadata': {'error': 'unsupported_analysis_type'}
            }

    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Erro ao processar análise: {e}")
        return {
            'response': f"❌ Erro ao processar análise: {str(e)}. Tente reformular sua solicitação.",
            'metadata': {'error': str(e)}
        }


@router.get("/debug/database-stats")
async def get_database_stats(db: Session = Depends(get_database_session)):
    """Endpoint temporário para verificar estatísticas da base de dados."""
    try:
        # Verificar documentos
        documents_count = db.execute(text("SELECT COUNT(*) as count FROM documents")).fetchone()

        # Verificar chunks
        chunks_count = db.execute(text("SELECT COUNT(*) as count FROM chunks_parent")).fetchone()

        # Verificar embeddings
        embeddings_count = db.execute(text("SELECT COUNT(*) as count FROM chunk_embeddings")).fetchone()

        # Verificar versões
        versions_count = db.execute(text("SELECT COUNT(*) as count FROM versions")).fetchone()

        return {
            "documents": documents_count[0] if documents_count else 0,
            "chunks": chunks_count[0] if chunks_count else 0,
            "embeddings": embeddings_count[0] if embeddings_count else 0,
            "versions": versions_count[0] if versions_count else 0
        }

    except Exception as e:
        logger.error(f"Erro ao obter estatísticas: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/debug/test-search")
async def test_search_with_threshold(
    request: dict,
    db: Session = Depends(get_database_session)
):
    """Endpoint temporário para testar busca com diferentes thresholds."""
    try:
        query = request.get("query", "dados")
        threshold = request.get("threshold", 0.3)
        limit = request.get("limit", 5)

        # Inicializar serviço de busca semântica
        search_service = SemanticSearchService()

        # Gerar embedding da query para debug
        query_embedding = search_service.generate_query_embedding(query)

        # Realizar busca com threshold personalizado
        results = search_service.semantic_search(
            query=query,
            limit=limit,
            similarity_threshold=threshold
        )

        return {
            "query": query,
            "threshold": threshold,
            "query_embedding_length": len(query_embedding) if query_embedding else 0,
            "results_count": len(results),
            "results": results[:3] if results else []  # Primeiros 3 resultados
        }

    except Exception as e:
        logger.error(f"Erro no teste de busca: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/debug/sample-embeddings")
async def get_sample_embeddings(db: Session = Depends(get_database_session)):
    """Endpoint temporário para verificar embeddings na base de dados."""
    try:
        # Verificar alguns embeddings e chunks
        query = text("""
            SELECT
                ce.embedding_id,
                ce.chunk_id,
                ce.model_name,
                cp.text,
                d.original_name,
                LENGTH(ce.embedding::text) as embedding_length
            FROM chunk_embeddings ce
            JOIN chunks_parent cp ON (ce.version_id = cp.version_id AND ce.chunk_id = cp.chunk_id)
            JOIN versions v ON ce.version_id = v.version_id
            JOIN documents d ON v.document_id = d.document_id
            LIMIT 5
        """)

        result = db.execute(query)
        rows = result.fetchall()

        sample_data = []
        for row in rows:
            sample_data.append({
                "embedding_id": row[0],
                "chunk_id": row[1],
                "model_name": row[2],
                "text_preview": row[3][:100] + "..." if len(row[3]) > 100 else row[3],
                "document_name": row[4],
                "embedding_length": row[5]
            })

        return {
            "sample_count": len(sample_data),
            "samples": sample_data
        }

    except Exception as e:
        logger.error(f"Erro ao obter embeddings de amostra: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/debug/test-sql-search")
async def test_sql_search(
    request: dict,
    db: Session = Depends(get_database_session)
):
    """Endpoint temporário para testar a query SQL diretamente."""
    try:
        query = request.get("query", "sterilization")
        threshold = request.get("threshold", 0.1)

        # Inicializar serviço de busca semântica
        search_service = SemanticSearchService()

        # Gerar embedding da query
        query_embedding = search_service.generate_query_embedding(query)
        if not query_embedding:
            return {"error": "Falha ao gerar embedding"}

        # Converter embedding para string PostgreSQL
        embedding_str = '[' + ','.join(map(str, query_embedding)) + ']'

        # Query SQL direta - usando interpolação direta para o embedding
        sql_query = text(f"""
            SELECT
                ce.embedding_id,
                ce.chunk_id,
                cp.text,
                d.original_name,
                (1 - (ce.embedding <=> '{embedding_str}'::vector)) as similarity_score
            FROM chunk_embeddings ce
            JOIN chunks_parent cp ON (ce.version_id = cp.version_id AND ce.chunk_id = cp.chunk_id)
            JOIN versions v ON ce.version_id = v.version_id
            JOIN documents d ON v.document_id = d.document_id
            WHERE ce.model_name = :model_name
                AND (1 - (ce.embedding <=> '{embedding_str}'::vector)) >= :similarity_threshold
            ORDER BY similarity_score DESC
            LIMIT :limit
        """)

        # Parâmetros
        params = {
            'model_name': 'text-embedding-3-small',
            'similarity_threshold': threshold,
            'limit': 5
        }

        # Executar query
        result = db.execute(sql_query, params)
        rows = result.fetchall()

        results = []
        for row in rows:
            results.append({
                "embedding_id": row[0],
                "chunk_id": row[1],
                "text_preview": row[2][:200] + "..." if len(row[2]) > 200 else row[2],
                "document_name": row[3],
                "similarity_score": float(row[4])
            })

        return {
            "query": query,
            "threshold": threshold,
            "embedding_length": len(query_embedding),
            "results_count": len(results),
            "results": results
        }

    except Exception as e:
        logger.error(f"Erro no teste SQL: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/debug/test-simple-search")
async def test_simple_search(
    request: dict,
    db: Session = Depends(get_database_session)
):
    """Endpoint para testar busca simples sem embeddings."""
    try:
        query = request.get("query", "test")

        # Query SQL simples
        sql_query = text("""
            SELECT
                ce.embedding_id,
                ce.chunk_id,
                cp.text,
                d.original_name
            FROM chunk_embeddings ce
            JOIN chunks_parent cp ON (ce.version_id = cp.version_id AND ce.chunk_id = cp.chunk_id)
            JOIN versions v ON ce.version_id = v.version_id
            JOIN documents d ON v.document_id = d.document_id
            WHERE ce.model_name = :model_name
                AND cp.text ILIKE :search_term
            LIMIT :limit
        """)

        # Parâmetros
        params = {
            'model_name': 'text-embedding-3-small',
            'search_term': f'%{query}%',
            'limit': 5
        }

        # Executar
        result = db.execute(sql_query, params)
        rows = result.fetchall()

        return {
            "query": query,
            "results_count": len(rows),
            "results": [
                {
                    "embedding_id": str(row.embedding_id),
                    "chunk_id": str(row.chunk_id),
                    "text": row.text[:200] + "..." if len(row.text) > 200 else row.text,
                    "original_name": row.original_name
                }
                for row in rows
            ]
        }

    except Exception as e:
        logger.error(f"Erro no teste simples: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/debug/test-hybrid-search")
async def test_hybrid_search(request: dict, db: Session = Depends(get_database_session)):
    """Endpoint para testar busca híbrida com sessão do banco."""
    try:
        query = request.get("query", "sterilization")

        # Inicializar serviço de busca semântica
        from app.services.semantic_search_service import SemanticSearchService
        search_service = SemanticSearchService()

        # Testar busca híbrida com sessão do banco
        results = search_service.hybrid_search(
            query=query,
            limit=5,
            semantic_weight=0.8,
            db_session=db  # Passar sessão do PostgreSQL
        )

        return {
            "query": query,
            "results_count": len(results),
            "results": results[:5]  # Limitar para visualização
        }

    except Exception as e:
        logger.error(f"Erro no teste de busca híbrida: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/debug/test-textual-search")
async def test_textual_search(request: dict, db: Session = Depends(get_database_session)):
    """Endpoint para testar apenas busca textual."""
    try:
        query = request.get("query", "sterilization")

        # Inicializar serviço de busca semântica
        from app.services.semantic_search_service import SemanticSearchService
        search_service = SemanticSearchService()

        # Testar busca textual com sessão do banco
        results = search_service._textual_search(
            query=query,
            limit=5,
            db_session=db  # Passar sessão do PostgreSQL
        )

        return {
            "query": query,
            "results_count": len(results),
            "results": results[:5]  # Limitar para visualização
        }

    except Exception as e:
        logger.error(f"Erro no teste de busca textual: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/debug/test-semantic-search")
async def test_semantic_search(request: dict, db: Session = Depends(get_database_session)):
    """Endpoint para testar apenas busca semântica."""
    try:
        query = request.get("query", "sterilization")
        threshold = request.get("threshold", 0.3)

        # Inicializar serviço de busca semântica
        from app.services.semantic_search_service import SemanticSearchService
        search_service = SemanticSearchService()

        # Testar busca semântica com sessão do banco
        results = search_service.semantic_search(
            query=query,
            limit=5,
            similarity_threshold=threshold,
            db_session=db  # Passar sessão do PostgreSQL
        )

        return {
            "query": query,
            "threshold": threshold,
            "results_count": len(results),
            "results": results[:5]  # Limitar para visualização
        }

    except Exception as e:
        logger.error(f"Erro no teste de busca semântica: {e}")
        raise HTTPException(status_code=500, detail=str(e))
