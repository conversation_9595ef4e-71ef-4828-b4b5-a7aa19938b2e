"""
M10 AI Orchestrator - Chat Session Model

SQLAlchemy model for storing chat sessions, including metadata
for user information, conversation context, and system tracking.
"""

from sqlalchemy import Column, String, Text, Integer, Float, Boolean, JSON, Enum as SQLEnum # Adicionado Enum as SQLEnum
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
from enum import Enum
import uuid
from .base import BaseModel


class SessionStatus(str, Enum):
    """Enumeration for chat session status."""
    ACTIVE = "active"
    COMPLETED = "completed"
    ARCHIVED = "archived"
    DELETED = "deleted"


class ChatSession(BaseModel):
    """
    Model for chat sessions in the AI Orchestrator.
    
    A chat session represents a conversation between a user and the AI,
    containing multiple messages, context information, and metadata.
    """
    
    __tablename__ = "chat_sessions"
    
    # Session identification and metadata
    name = Column(
        String(255),
        nullable=True,
        comment="Human-readable name for the chat session"
    )
    
    status = Column(
        SQLEnum(SessionStatus, values_callable=lambda obj: [e.value for e in obj]),
        nullable=False,
        default=SessionStatus.ACTIVE,
        comment="Status of the chat session"
    )
    
    # Relationships
    messages = relationship(
        "Message",
        back_populates="chat_session",
        cascade="all, delete-orphan",
        order_by="Message.created_at"
    )
    
    query_logs = relationship(
        "QueryLog",
        back_populates="session",
        cascade="all, delete-orphan",
        order_by="QueryLog.created_at"
    )
    
    def complete(self):
        """Mark session as completed."""
        self.status = SessionStatus.COMPLETED
    
    def archive(self):
        """Mark session as archived."""
        self.status = SessionStatus.ARCHIVED
    
    def is_active(self) -> bool:
        """Check if session is active."""
        return self.status == SessionStatus.ACTIVE
    
    def is_completed(self) -> bool:
        """Check if session is completed."""
        return self.status == SessionStatus.COMPLETED
    
    def __repr__(self) -> str:
        """String representation of the chat session."""
        return (
            f"<ChatSession(id={self.id}, name='{self.name}', status='{self.status}')>"
        )
