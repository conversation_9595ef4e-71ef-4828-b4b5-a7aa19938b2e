"""
M10 AI Orchestrator - Base SQLAlchemy Model

Defines the base model class with common fields and utilities
that all database models inherit from.
"""

from sqlalchemy import Column, DateTime, String, Text, func
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.dialects.postgresql import UUID
import uuid
from datetime import datetime
from ..utils.unique_id_generator import generate_unique_uuid

# Create the declarative base that all models will inherit from
Base = declarative_base()


class BaseModel(Base):
    """
    Abstract base model with common fields and methods for all entities.
    
    Provides:
    - UUID primary key
    - Created and updated timestamps
    - Common utility methods
    """
    
    __abstract__ = True
    __table_args__ = {'schema': 'ai_orchestrator'}
    
    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=generate_unique_uuid,
        unique=True,
        nullable=False,
        comment="Unique identifier for the record"
    )
    
    created_at = Column(
        DateTime(timezone=True),
        nullable=False,
        default=func.now(),
        comment="Timestamp when the record was created"
    )
    
    updated_at = Column(
        DateTime(timezone=True),
        nullable=False,
        default=func.now(),
        onupdate=func.now(),
        comment="Timestamp when the record was last updated"
    )
    
    def to_dict(self) -> dict:
        """
        Convert model instance to dictionary.
        
        Returns:
            dict: Dictionary representation of the model
        """
        return {
            column.name: getattr(self, column.name)
            for column in self.__table__.columns
        }
    
    def update(self, **kwargs):
        """
        Update model attributes from keyword arguments.
        
        Args:
            **kwargs: Attributes to update
        """
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def __repr__(self) -> str:
        """String representation of the model."""
        return f"<{self.__class__.__name__}(id={self.id})>"
