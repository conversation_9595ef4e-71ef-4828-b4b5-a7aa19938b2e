"""
M10 AI Orchestrator - Message Model

SQLAlchemy model for storing individual messages within chat sessions,
including user messages, AI responses, and associated metadata.
"""

from sqlalchemy import Column, String, Text, Integer, <PERSON>loat, <PERSON>olean, JSON, <PERSON><PERSON>ey
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
from enum import Enum
import uuid
from .base import BaseModel


class MessageRole(str, Enum):
    """Enumeration for message roles in conversation."""
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"


class MessageStatus(str, Enum):
    """Enumeration for message processing status."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class Message(BaseModel):
    """
    Model for individual messages in chat sessions.
    
    Represents a single message from either user or assistant,
    with associated metadata, context, and processing information.
    """
    
    __tablename__ = "messages"
    
    # Foreign key to chat session
    chat_session_id = Column(
        UUID(as_uuid=True),
        ForeignKey("ai_orchestrator.chat_sessions.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        comment="ID of the chat session this message belongs to"
    )
    
    # Message identification and content
    role = Column(
        String(20),
        nullable=False,
        comment="Role of the message sender (user, assistant, system)"
    )
    
    content = Column(
        Text,
        nullable=False,
        comment="The actual message content"
    )
    
    # Additional metadata
    extra_metadata = Column( # Renomeado de 'metadata'
        JSON,
        nullable=True,
        default={},
        comment="Additional metadata as JSON"
    )
    
    # Relationships
    chat_session = relationship(
        "ChatSession",
        back_populates="messages"
    )
    
    citations = relationship(
        "Citation",
        back_populates="message",
        cascade="all, delete-orphan"
    )
    
    def is_user_message(self) -> bool:
        """Check if this is a user message."""
        return self.role == 'user'
    
    def is_assistant_message(self) -> bool:
        """Check if this is an assistant message."""
        return self.role == 'assistant'
    
    def is_system_message(self) -> bool:
        """Check if this is a system message."""
        return self.role == 'system'
    
    def __repr__(self) -> str:
        """String representation of the message."""
        content_preview = (
            self.content[:50] + "..." 
            if len(self.content) > 50 
            else self.content
        )
        return (
            f"<Message(id={self.id}, role='{self.role}', "
            f"content='{content_preview}')>"
        )
