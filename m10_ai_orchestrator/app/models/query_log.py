"""
M10 AI Orchestrator - Query Log Model

SQLAlchemy model for logging query operations, performance metrics,
and debugging information for the AI Orchestrator system.
"""

from sqlalchemy import Column, String, Text, Integer, Float, Boolean, JSON, ForeignKey, Enum as SQLEnum, DateTime
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
from enum import Enum
import uuid
from datetime import datetime
from .base import BaseModel


class QueryType(str, Enum):
    """Enumeration for different types of queries."""
    SEARCH = "search"
    DIFF = "diff"
    LOOKUP = "lookup"
    CHAT = "chat"
    CONTEXT_RETRIEVAL = "context_retrieval"
    INTENT_CLASSIFICATION = "intent_classification"


class QueryStatus(str, Enum):
    """Enumeration for query execution status."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    TIMEOUT = "timeout"
    CANCELLED = "cancelled"


class QueryLog(BaseModel):
    """
    Model for logging query operations and performance metrics.
    
    Tracks all queries processed by the AI Orchestrator, including
    performance metrics, routing decisions, and debugging information.
    """
    
    __tablename__ = "query_logs"
    
    # Core identification
    query_id = Column(
        String(255),
        nullable=False,
        unique=True,
        comment="Unique identifier for the query"
    )
    
    # Query content
    original_query = Column(
        Text,
        nullable=False,
        comment="The original user query"
    )
    
    processed_query = Column(
        Text,
        nullable=True,
        comment="Processed/normalized query"
    )
    
    query_text = Column(
        Text,
        nullable=True,
        comment="Legacy field - mapped to original_query"
    )
    
    response_text = Column(
        Text,
        nullable=True,
        comment="The response generated for the query"
    )
    
    query_type = Column(
        SQLEnum(QueryType, values_callable=lambda obj: [e.value for e in obj]),
        nullable=False,
        default=QueryType.CHAT,
        comment="Type of the query (e.g., chat, search, diff)"
    )
    
    status = Column(
        SQLEnum(QueryStatus, values_callable=lambda obj: [e.value for e in obj]),
        nullable=False,
        default=QueryStatus.PENDING,
        comment="Status of the query execution"
    )
    
    # Session and user context
    session_id = Column(
        UUID(as_uuid=True),
        ForeignKey("ai_orchestrator.chat_sessions.id", ondelete="SET NULL"),
        nullable=True,
        comment="Associated chat session ID"
    )
    
    user_agent = Column(
        String(500),
        nullable=True,
        comment="User agent string"
    )
    
    ip_address = Column(
        String(45),
        nullable=True,
        comment="IP address of requester"
    )
    
    request_id = Column(
        String(255),
        nullable=True,
        comment="Request ID for tracing"
    )
    
    # Intent classification
    detected_intent = Column(
        String(100),
        nullable=True,
        comment="Detected intent from classification"
    )
    
    intent_confidence = Column(
        Float,
        nullable=True,
        comment="Confidence score for intent detection"
    )
    
    # Routing information
    routed_to_service = Column(
        String(100),
        nullable=True,
        comment="Service the query was routed to"
    )
    
    routing_reason = Column(
        String(255),
        nullable=True,
        comment="Reason for routing decision"
    )
    
    fallback_used = Column(
        Boolean,
        nullable=False,
        default=False,
        comment="Whether fallback routing was used"
    )
    
    # Performance metrics
    total_execution_time_ms = Column(
        Integer,
        nullable=True,
        comment="Total execution time in milliseconds"
    )
    
    execution_time_ms = Column(
        Integer,
        nullable=True,
        comment="Legacy field - mapped to total_execution_time_ms"
    )
    
    intent_classification_time_ms = Column(
        Integer,
        nullable=True,
        comment="Intent classification time in milliseconds"
    )
    
    context_retrieval_time_ms = Column(
        Integer,
        nullable=True,
        comment="Context retrieval time in milliseconds"
    )
    
    llm_processing_time_ms = Column(
        Integer,
        nullable=True,
        comment="LLM processing time in milliseconds"
    )
    
    response_formatting_time_ms = Column(
        Integer,
        nullable=True,
        comment="Response formatting time in milliseconds"
    )
    
    # Usage and cost metrics
    prompt_tokens = Column(
        Integer,
        nullable=True,
        comment="Tokens used in prompt"
    )
    
    completion_tokens = Column(
        Integer,
        nullable=True,
        comment="Tokens used in completion"
    )
    
    total_tokens = Column(
        Integer,
        nullable=True,
        comment="Total tokens used"
    )
    
    tokens_used = Column(
        Integer,
        nullable=True,
        comment="Legacy field - mapped to total_tokens"
    )
    
    cost_usd = Column(
        Float,
        nullable=True,
        comment="Cost in USD"
    )
    
    # Context information
    context_sources_count = Column(
        Integer,
        nullable=False,
        default=0,
        comment="Number of context sources used"
    )
    
    context_tokens_used = Column(
        Integer,
        nullable=False,
        default=0,
        comment="Tokens used for context"
    )
    
    context_truncated = Column(
        Boolean,
        nullable=False,
        default=False,
        comment="Whether context was truncated"
    )
    
    # Response metrics
    response_length = Column(
        Integer,
        nullable=True,
        comment="Length of response in characters"
    )
    
    citations_count = Column(
        Integer,
        nullable=False,
        default=0,
        comment="Number of citations in response"
    )
    
    response_quality_score = Column(
        Float,
        nullable=True,
        comment="Quality score for the response"
    )
    
    # Error handling
    error_message = Column(
        Text,
        nullable=True,
        comment="Error message if query failed"
    )
    
    error_details = Column(
        JSON,
        nullable=True,
        comment="Detailed error information"
    )
    
    error_service = Column(
        String(100),
        nullable=True,
        comment="Service where error occurred"
    )
    
    # Additional metadata
    query_metadata = Column(
        JSON,
        nullable=True,
        default={},
        comment="Additional metadata as JSON"
    )
    
    extra_metadata = Column(
        JSON,
        nullable=True,
        default={},
        comment="Legacy field - mapped to query_metadata"
    )
    
    # Relationships
    session = relationship("ChatSession", back_populates="query_logs")
    
    def set_processing(self):
        """Mark query as processing."""
        self.status = QueryStatus.PROCESSING
    
    def set_completed(self):
        """Mark query as completed."""
        self.status = QueryStatus.COMPLETED
    
    def set_failed(self, error_message: str, error_details: dict = None, error_service: str = None):
        """Mark query as failed with error information."""
        self.status = QueryStatus.FAILED
        self.error_message = error_message
        self.error_details = error_details or {}
        self.error_service = error_service
    
    def set_timeout(self):
        """Mark query as timed out."""
        self.status = QueryStatus.TIMEOUT
        if not self.error_message:
            self.error_message = "Query execution timed out"
    
    def is_completed(self) -> bool:
        """Check if query is completed."""
        return self.status == QueryStatus.COMPLETED
    
    def is_failed(self) -> bool:
        """Check if query failed or timed out."""
        return self.status in [QueryStatus.FAILED, QueryStatus.TIMEOUT]
    
    def is_processing(self) -> bool:
        """Check if query is currently processing."""
        return self.status == QueryStatus.PROCESSING
    
    def get_execution_time_seconds(self) -> float:
        """
        Get execution time in seconds.
        
        Returns:
            float: Processing time in seconds, or 0.0 if not available
        """
        execution_time = self.total_execution_time_ms or self.execution_time_ms
        if execution_time is not None:
            return execution_time / 1000.0
        return 0.0
    
    def __repr__(self) -> str:
        """String representation of the query log."""
        query_preview = (
            self.original_query[:50] + "..." 
            if self.original_query and len(self.original_query) > 50 
            else (self.original_query or "No query")
        )
        return (
            f"<QueryLog(id={self.id}, "
            f"query_id='{self.query_id}', "
            f"query='{query_preview}', status='{self.status}')>"
        )
