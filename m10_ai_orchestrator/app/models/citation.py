"""
M10 AI Orchestrator - Citation Model

SQLAlchemy model for storing citations that link AI responses
to their source documents and chunks for transparency and verification.
"""

from sqlalchemy import Column, String, Text, Integer, Float, Boolean, JSON, <PERSON><PERSON>ey
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
from enum import Enum
import uuid
from .base import BaseModel


class SourceType(str, Enum):
    """Enumeration for different types of citation sources."""
    DOCUMENT = "document"
    CHUNK = "chunk"
    SEARCH_RESULT = "search_result"
    DIFF_RESULT = "diff_result"
    EXTERNAL_API = "external_api"
    KNOWLEDGE_BASE = "knowledge_base"


class Citation(BaseModel):
    """
    Model for citations that link AI responses to source material.
    
    Citations provide transparency by tracking which sources were used
    to generate specific parts of AI responses, enabling users to verify
    information and trace back to original documents.
    """
    
    __tablename__ = "citations"
    
    # Foreign key to message
    message_id = Column(
        UUID(as_uuid=True),
        ForeignKey("ai_orchestrator.messages.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        comment="ID of the message this citation belongs to"
    )
    
    # Source identification
    source_type = Column(
        String(50),
        nullable=False,
        comment="Type of source being cited"
    )
    
    source_id = Column(
        String(255),
        nullable=False,
        comment="Identifier of the source (document ID, chunk ID, etc.)"
    )
    
    # Citation content
    content = Column(
        Text,
        nullable=True,
        comment="The specific content that was cited from the source"
    )
    
    # Additional metadata
    extra_metadata = Column( # Renomeado de 'metadata'
        JSON,
        nullable=True,
        default={},
        comment="Additional metadata about the citation as JSON"
    )
    
    # Relationships
    message = relationship(
        "Message",
        back_populates="citations"
    )
    
    def __repr__(self) -> str:
        """String representation of the citation."""
        content_preview = (
            self.content[:30] + "..." 
            if self.content and len(self.content) > 30 
            else self.content or ""
        )
        return (
            f"<Citation(id={self.id}, source_id='{self.source_id}', "
            f"type='{self.source_type}', content='{content_preview}')>"
        )
