import time
import json
import sqlalchemy
from sqlalchemy.exc import SQLAlchemyError
import structlog
import asyncio # Adicionado import

# Corrigir o caminho de importação para settings, assumindo que config.py está em app/
from app.config import get_settings

logger = structlog.get_logger(__name__)
settings = get_settings()

async def check_postgresql_health():
    """
    Verifica a saúde da conexão com o PostgreSQL.
    Tenta conectar e executar uma query simples ('SELECT 1').
    Retorna um dicionário com status, tempo de resposta e erro (se houver).
    """
    start_time = time.perf_counter()
    try:
        # Usar a URL do banco de dados das configurações
        # Adicionado pool_pre_ping para verificar conexões antes de usar
        # Adicionado connect_timeout para evitar bloqueio indefinido
        engine = sqlalchemy.create_engine(
            settings.database_url, 
            pool_pre_ping=True, 
            connect_args={"connect_timeout": 5} # Timeout de 5 segundos para conexão
        )
        
        # O método connect() em si pode bloquear, idealmente seria executado em um threadpool
        # para uma aplicação FastAPI assíncrona, mas para um health check simples,
        # um bloqueio curto pode ser aceitável ou gerenciado pelo timeout.
        # Para uma implementação totalmente assíncrona, bibliotecas como 'asyncpg' seriam usadas.
        with engine.connect() as connection:
            connection.execute(sqlalchemy.text("SELECT 1"))
            # Para garantir que a transação seja fechada se houver uma
            if connection.in_transaction():
                connection.commit() # ou rollback() dependendo da necessidade
        
        response_time_ms = (time.perf_counter() - start_time) * 1000
        logger.debug("PostgreSQL health check successful", response_time_ms=response_time_ms)
        return {
            "status": "healthy",
            "response_time_ms": round(response_time_ms, 2),
            "details": "Connection successful and test query executed."
        }
    except SQLAlchemyError as e:
        response_time_ms = (time.perf_counter() - start_time) * 1000
        # Capturar a exceção original para mais detalhes no log, se necessário
        original_error_info = f" (Details: {e.orig})" if hasattr(e, 'orig') and e.orig else ""
        error_message = f"SQLAlchemyError: {str(e)}{original_error_info}"
        logger.error("PostgreSQL health check failed", error=error_message, response_time_ms=response_time_ms)
        return {
            "status": "unhealthy",
            "response_time_ms": round(response_time_ms, 2),
            "error": error_message,
            "details": "Failed to connect or execute test query."
        }
    except Exception as e:
        response_time_ms = (time.perf_counter() - start_time) * 1000
        error_message = f"Unexpected error: {str(e)}"
        logger.error("PostgreSQL health check failed with unexpected error", error=error_message, response_time_ms=response_time_ms)
        return {
            "status": "unhealthy",
            "response_time_ms": round(response_time_ms, 2),
            "error": error_message,
            "details": "An unexpected error occurred during health check."
        }

import redis.asyncio as aioredis # Adicionar import para redis assíncrono

# Adicionar aqui futuramente as verificações para outros serviços:
async def check_redis_health():
    """
    Verifica a saúde da conexão com o Redis.
    Tenta conectar e executar um comando PING.
    """
    start_time = time.perf_counter()
    try:
        # Usar redis_url das configurações
        # settings.redis_url já inclui host, port, db
        # Ex: redis://localhost:6379/0
        redis_client = await aioredis.from_url(
            settings.redis_url, 
            socket_connect_timeout=5, # Timeout de 5 segundos para conexão
            socket_timeout=5 # Timeout para operações
        )
        
        await redis_client.ping()
        await redis_client.close() # Fechar a conexão explicitamente
        
        response_time_ms = (time.perf_counter() - start_time) * 1000
        logger.debug("Redis health check successful", response_time_ms=response_time_ms)
        return {
            "status": "healthy",
            "response_time_ms": round(response_time_ms, 2),
            "details": "Connection successful and PING responded."
        }
    except aioredis.RedisError as e:
        response_time_ms = (time.perf_counter() - start_time) * 1000
        error_message = f"RedisError: {str(e)}"
        logger.error("Redis health check failed", error=error_message, response_time_ms=response_time_ms)
        return {
            "status": "unhealthy",
            "response_time_ms": round(response_time_ms, 2),
            "error": error_message,
            "details": "Failed to connect or PING Redis."
        }
    except Exception as e:
        response_time_ms = (time.perf_counter() - start_time) * 1000
        error_message = f"Unexpected error: {str(e)}"
        logger.error("Redis health check failed with unexpected error", error=error_message, response_time_ms=response_time_ms)
        return {
            "status": "unhealthy",
            "response_time_ms": round(response_time_ms, 2),
            "error": error_message,
            "details": "An unexpected error occurred during Redis health check."
        }

from openai import OpenAI, APIConnectionError, RateLimitError, APIStatusError # Adicionar imports do OpenAI

async def check_openai_health():
    """
    Verifica a saúde da conexão com a OpenAI API.
    Tenta listar um modelo para verificar a conectividade e a chave da API.
    """
    start_time = time.perf_counter()
    try:
        if not settings.openai_api_key:
            logger.warn("OpenAI API key não configurada. Health check pulado.")
            return {
                "status": "degraded", # Ou "unknown" ou "not_configured"
                "response_time_ms": 0,
                "details": "OpenAI API key not configured."
            }

        # Usar timeout muito mais agressivo para health check
        client = OpenAI(api_key=settings.openai_api_key, timeout=5.0) # Timeout de 5s
        
        # Usar asyncio.wait_for para garantir timeout absoluto
        models_task = asyncio.to_thread(client.models.list)
        await asyncio.wait_for(models_task, timeout=5.0) 
        
        response_time_ms = (time.perf_counter() - start_time) * 1000
        logger.debug("OpenAI API health check successful", response_time_ms=response_time_ms)
        return {
            "status": "healthy",
            "response_time_ms": round(response_time_ms, 2),
            "details": "Connection successful and API responded."
        }
    except asyncio.TimeoutError:
        response_time_ms = (time.perf_counter() - start_time) * 1000
        error_message = "OpenAI API health check timeout (>5s)"
        logger.error("OpenAI API health check timed out", error=error_message, response_time_ms=response_time_ms)
        return {
            "status": "unhealthy",
            "response_time_ms": round(response_time_ms, 2),
            "error": error_message,
            "details": "OpenAI API took too long to respond."
        }
    except APIConnectionError as e:
        response_time_ms = (time.perf_counter() - start_time) * 1000
        error_message = f"OpenAI APIConnectionError: {str(e)}"
        logger.error("OpenAI API health check failed (ConnectionError)", error=error_message, response_time_ms=response_time_ms)
        return {
            "status": "unhealthy",
            "response_time_ms": round(response_time_ms, 2),
            "error": error_message,
            "details": "Failed to connect to OpenAI API."
        }
    except RateLimitError as e:
        response_time_ms = (time.perf_counter() - start_time) * 1000
        error_message = f"OpenAI RateLimitError: {str(e)}"
        logger.warn("OpenAI API health check failed (RateLimitError)", error=error_message, response_time_ms=response_time_ms)
        return {
            "status": "degraded", # Rate limit é mais um 'degraded' do que 'unhealthy'
            "response_time_ms": round(response_time_ms, 2),
            "error": error_message,
            "details": "OpenAI API rate limit exceeded."
        }
    except APIStatusError as e: # Captura outros erros da API (4xx, 5xx)
        response_time_ms = (time.perf_counter() - start_time) * 1000
        error_message = f"OpenAI APIStatusError (HTTP {e.status_code}): {str(e)}"
        logger.error(f"OpenAI API health check failed (APIStatusError {e.status_code})", error=error_message, response_time_ms=response_time_ms)
        return {
            "status": "unhealthy", # Pode ser 'degraded' para alguns 4xx
            "response_time_ms": round(response_time_ms, 2),
            "error": error_message,
            "details": f"OpenAI API returned status {e.status_code}."
        }
    except Exception as e:
        response_time_ms = (time.perf_counter() - start_time) * 1000
        error_message = f"Unexpected error: {str(e)}"
        logger.error("OpenAI API health check failed with unexpected error", error=error_message, response_time_ms=response_time_ms)
        return {
            "status": "unhealthy",
            "response_time_ms": round(response_time_ms, 2),
            "error": error_message,
            "details": "An unexpected error occurred during OpenAI API health check."
        }

import httpx # Adicionar import para httpx

async def check_generic_service_health(service_name: str, health_url: str):
    """
    Verifica a saúde de um serviço genérico baseado em HTTP.
    Tenta fazer um GET request para a URL de health check.
    """
    start_time = time.perf_counter()
    try:
        async with httpx.AsyncClient(timeout=10.0) as client: # Timeout de 10s
            response = await client.get(health_url)
        
        response_time_ms = (time.perf_counter() - start_time) * 1000
        
        if response.status_code == 200:
            try:
                data = response.json()
                # Tentar encontrar um campo de status no JSON, comum em health checks
                service_reported_status = data.get("status", "healthy").lower()
                if service_reported_status == "healthy":
                    status = "healthy"
                    details = data.get("message", f"{service_name} is healthy.")
                else:
                    status = "degraded" # Se o serviço reporta algo diferente de healthy mas responde 200
                    details = f"{service_name} reported status: {service_reported_status}. Response: {data}"
            except json.JSONDecodeError:
                status = "healthy" # Se 200 OK mas não JSON, consideramos a conexão OK
                details = f"{service_name} responded with 200 OK, but non-JSON content."
            
            logger.debug(f"{service_name} health check successful", response_time_ms=response_time_ms, status=status)
            return {
                "status": status,
                "response_time_ms": round(response_time_ms, 2),
                "details": details
            }
        else:
            logger.warn(f"{service_name} health check failed", status_code=response.status_code, response_time_ms=response_time_ms)
            return {
                "status": "unhealthy",
                "response_time_ms": round(response_time_ms, 2),
                "error": f"HTTP status {response.status_code}",
                "details": f"{service_name} returned status {response.status_code}."
            }
    except httpx.RequestError as e:
        response_time_ms = (time.perf_counter() - start_time) * 1000
        error_message = f"httpx.RequestError for {service_name}: {str(e)}"
        logger.error(f"{service_name} health check failed (RequestError)", error=error_message, response_time_ms=response_time_ms)
        return {
            "status": "unhealthy",
            "response_time_ms": round(response_time_ms, 2),
            "error": error_message,
            "details": f"Failed to connect to {service_name}."
        }
    except Exception as e:
        response_time_ms = (time.perf_counter() - start_time) * 1000
        error_message = f"Unexpected error checking {service_name}: {str(e)}"
        logger.error(f"{service_name} health check failed with unexpected error", error=error_message, response_time_ms=response_time_ms)
        return {
            "status": "unhealthy",
            "response_time_ms": round(response_time_ms, 2),
            "error": error_message,
            "details": f"An unexpected error occurred during {service_name} health check."
        }

async def check_m2_health():
    if not settings.m2_api_url:
        logger.warn("M2_API_URL not configured. M2 health check pulado.")
        return {"status": "degraded", "response_time_ms": 0, "details": "M2_API_URL not configured."}
    # O endpoint de health do M2 é /healthz
    health_url = f"{settings.m2_api_url.rstrip('/')}/healthz"
    return await check_generic_service_health("M2 Task Orchestrator", health_url)

async def check_m4_health():
    if not settings.m4_api_url:
        logger.warn("M4_API_URL not configured. M4 health check pulado.")
        return {"status": "degraded", "response_time_ms": 0, "details": "M4_API_URL not configured."}
    health_url = f"{settings.m4_api_url.rstrip('/')}/healthz"
    return await check_generic_service_health("M4 Translator", health_url)

async def check_m5_health():
    if not settings.m5_db_browser_url:
        logger.warn("M5_DB_BROWSER_URL not configured. M5 health check skipped.")
        return {"status": "degraded", "response_time_ms": 0, "details": "M5_DB_BROWSER_URL not configured."}
    health_url = f"{settings.m5_db_browser_url.rstrip('/')}/healthz"
    return await check_generic_service_health("M5 DB Browser", health_url)

async def check_m6_health():
    if not settings.m6_vector_indexer_url:
        logger.warn("M6_VECTOR_INDEXER_URL not configured. M6 health check skipped.")
        return {"status": "degraded", "response_time_ms": 0, "details": "M6_VECTOR_INDEXER_URL not configured."}
    health_url = f"{settings.m6_vector_indexer_url.rstrip('/')}/healthz"
    return await check_generic_service_health("M6 Vector Indexer", health_url)

async def check_m7_health():
    if not settings.m7_diff_engine_url:
        logger.warn("M7_DIFF_ENGINE_URL not configured. M7 health check skipped.")
        return {"status": "degraded", "response_time_ms": 0, "details": "M7_DIFF_ENGINE_URL not configured."}
    health_url = f"{settings.m7_diff_engine_url.rstrip('/')}/healthz"
    return await check_generic_service_health("M7 Diff Engine", health_url)

# Adicionar import para RabbitMQ
try:
    import aioamqp
    RABBITMQ_AVAILABLE = True
except ImportError:
    RABBITMQ_AVAILABLE = False
    logger.warn("aioamqp not available. RabbitMQ health checks will be degraded.")

async def check_rabbitmq_health():
    """
    Verifica a saúde da conexão com o RabbitMQ.
    Tenta conectar e fazer uma operação básica.
    """
    start_time = time.perf_counter()
    try:
        if not RABBITMQ_AVAILABLE:
            return {
                "status": "degraded",
                "response_time_ms": 0,
                "details": "aioamqp library not available for RabbitMQ health check."
            }
            
        rabbitmq_host = getattr(settings, 'rabbitmq_host', None)
        if not rabbitmq_host:
            logger.warn("RABBITMQ_HOST not configured. RabbitMQ health check skipped.")
            return {"status": "degraded", "response_time_ms": 0, "details": "RABBITMQ_HOST not configured."}
        
        # Conectar ao RabbitMQ
        transport, protocol = await aioamqp.connect(
            host=rabbitmq_host,
            port=getattr(settings, 'rabbitmq_port', 5672),
            login=getattr(settings, 'rabbitmq_user', 'user'),
            password=getattr(settings, 'rabbitmq_pass', 'password'),
            heartbeat=5
        )
        
        # Fechar a conexão
        await protocol.close()
        transport.close()
        
        response_time_ms = (time.perf_counter() - start_time) * 1000
        logger.debug("RabbitMQ health check successful", response_time_ms=response_time_ms)
        return {
            "status": "healthy",
            "response_time_ms": round(response_time_ms, 2),
            "details": "Connection successful to RabbitMQ."
        }
    except Exception as e:
        response_time_ms = (time.perf_counter() - start_time) * 1000
        error_message = f"RabbitMQ connection error: {str(e)}"
        logger.error("RabbitMQ health check failed", error=error_message, response_time_ms=response_time_ms)
        return {
            "status": "unhealthy",
            "response_time_ms": round(response_time_ms, 2),
            "error": error_message,
            "details": "Failed to connect to RabbitMQ."
        }

async def check_m1_health():
    """
    Verifica a saúde do M1 Drive Connector.
    """
    m1_url = getattr(settings, 'm1_drive_connector_url', None)
    if not m1_url:
        logger.warn("M1_DRIVE_CONNECTOR_URL not configured. M1 health check skipped.")
        return {"status": "degraded", "response_time_ms": 0, "details": "M1_DRIVE_CONNECTOR_URL not configured."}
    health_url = f"{m1_url.rstrip('/')}/healthz"
    return await check_generic_service_health("M1 Drive Connector", health_url)

async def check_m8_health():
    """
    Verifica a saúde do M8 API Gateway.
    """
    m8_url = getattr(settings, 'm8_api_gateway_url', None)
    if not m8_url:
        logger.warn("M8_API_GATEWAY_URL not configured. M8 health check skipped.")
        return {"status": "degraded", "response_time_ms": 0, "details": "M8_API_GATEWAY_URL not configured."}
    health_url = f"{m8_url.rstrip('/')}/healthz"
    return await check_generic_service_health("M8 API Gateway", health_url)

async def check_m14_health():
    """
    Verifica a saúde do M14 Importek BFF.
    """
    m14_url = getattr(settings, 'm14_importek_bff_url', None)
    if not m14_url:
        logger.warn("M14_IMPORTEK_BFF_URL not configured. M14 health check skipped.")
        return {"status": "degraded", "response_time_ms": 0, "details": "M14_IMPORTEK_BFF_URL not configured."}
    health_url = f"{m14_url.rstrip('/')}/healthz"
    return await check_generic_service_health("M14 Importek BFF", health_url)

async def check_m3_health():
    """
    M3 é um consumer/worker, mas vamos tentar verificar se existe endpoint de health.
    Se não tiver URL configurada, consideramos como worker service.
    """
    m3_url = getattr(settings, 'm3_extractor_url', None)
    if not m3_url:
        logger.info("M3_EXTRACTOR_URL not configured. M3 is a worker service.")
        return {"status": "unknown", "response_time_ms": 0, "details": "M3 is a worker service, no health endpoint configured."}
    health_url = f"{m3_url.rstrip('/')}/healthz"
    return await check_generic_service_health("M3 Extractor", health_url)
