"""
Serviço de Busca Semântica
Implementa busca vetorial usando embeddings para encontrar conteúdo semanticamente similar.
"""

import logging
import time
from typing import List, Dict, Any, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import text
import openai
from ..config import get_settings
from ..database import DatabaseConfig

logger = logging.getLogger(__name__)

class SemanticSearchService:
    """Serviço para busca semântica usando embeddings vetoriais."""
    
    def __init__(self):
        self.settings = get_settings()
        self.openai_client = openai.OpenAI(api_key=self.settings.openai_api_key)
        self.embedding_model = "text-embedding-3-small"
        self.embedding_dimensions = 1536
        self.db_config = DatabaseConfig()
    
    def generate_query_embedding(self, query: str) -> Optional[List[float]]:
        """
        Gera embedding para uma consulta de busca.
        
        Args:
            query: Texto da consulta
            
        Returns:
            Lista de floats representando o embedding ou None se falhar
        """
        try:
            logger.info(f"🔄 Gerando embedding para consulta: '{query[:50]}...'")
            
            response = self.openai_client.embeddings.create(
                model=self.embedding_model,
                input=query.strip(),
                encoding_format="float"
            )
            
            embedding = response.data[0].embedding
            logger.info(f"✅ Embedding gerado com {len(embedding)} dimensões")
            return embedding
            
        except Exception as e:
            logger.error(f"❌ Erro ao gerar embedding: {e}")
            return None
    
    def semantic_search(
        self,
        query: str,
        limit: int = 10,
        similarity_threshold: float = 0.3,  # Reduzido de 0.7 para 0.3 para capturar mais resultados relevantes
        product_filter: Optional[str] = None,
        db_session: Optional[Session] = None  # Sessão opcional do banco
    ) -> List[Dict[str, Any]]:
        """
        Realiza busca semântica usando embeddings vetoriais.
        
        Args:
            query: Consulta em linguagem natural
            limit: Número máximo de resultados
            similarity_threshold: Limiar mínimo de similaridade (0.0 a 1.0)
            product_filter: Filtrar por produto específico
            
        Returns:
            Lista de chunks semanticamente similares
        """
        try:
            # 1. Gerar embedding da consulta
            query_embedding = self.generate_query_embedding(query)
            if not query_embedding:
                logger.error("Falha ao gerar embedding da consulta")
                return []
            
            # 2. Buscar por similaridade no banco
            if db_session:
                # Usar sessão fornecida
                results = self._vector_similarity_search(
                    db=db_session,
                    query_embedding=query_embedding,
                    limit=limit,
                    similarity_threshold=similarity_threshold,
                    product_filter=product_filter
                )
                logger.info(f"✅ Busca semântica retornou {len(results)} resultados para '{query}'")
                return results
            else:
                # Criar nova sessão
                db = self.db_config.SessionLocal()
                try:
                    results = self._vector_similarity_search(
                        db=db,
                        query_embedding=query_embedding,
                        limit=limit,
                        similarity_threshold=similarity_threshold,
                        product_filter=product_filter
                    )

                    logger.info(f"✅ Busca semântica concluída: {len(results)} resultados encontrados")
                    return results

                finally:
                    db.close()
                
        except Exception as e:
            logger.error(f"❌ Erro na busca semântica: {e}")
            return []
    
    def _vector_similarity_search(
        self,
        db: Session,
        query_embedding: List[float],
        limit: int,
        similarity_threshold: float,
        product_filter: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Executa busca vetorial no PostgreSQL usando pgvector.
        
        Args:
            db: Sessão do banco de dados
            query_embedding: Embedding da consulta
            limit: Número máximo de resultados
            similarity_threshold: Limiar de similaridade
            product_filter: Filtro opcional por produto
            
        Returns:
            Lista de resultados da busca vetorial
        """
        try:
            # Converter embedding para string PostgreSQL
            embedding_str = f"[{','.join(map(str, query_embedding))}]"
            
            # Query base com busca por similaridade coseno
            base_query = """
                SELECT
                    ce.embedding_id,
                    ce.chunk_id,
                    ce.version_id,
                    ce.model_name,
                    cp.chunk_index,
                    cp.text,
                    cp.metadata as chunk_metadata,
                    d.document_id,
                    d.original_name,
                    d.metadata as document_metadata,
                    v.version_tag,
                    (1 - (ce.embedding <=> %(embedding_param)s::vector)) as similarity_score
                FROM chunk_embeddings ce
                JOIN chunks_parent cp ON (ce.version_id = cp.version_id AND ce.chunk_id = cp.chunk_id)
                JOIN versions v ON ce.version_id = v.version_id
                JOIN documents d ON v.document_id = d.document_id
                WHERE ce.model_name = %(model_name)s
                    AND (1 - (ce.embedding <=> %(embedding_param)s::vector)) >= %(similarity_threshold)s
            """
            
            # Adicionar filtro por produto se especificado
            if product_filter:
                base_query += """
                    AND (
                        d.original_name ILIKE %(product_filter)s
                        OR d.metadata::text ILIKE %(product_filter)s
                        OR cp.text ILIKE %(product_filter)s
                    )
                """
            
            base_query += """
                ORDER BY ce.embedding <=> %(embedding_param)s::vector
                LIMIT %(limit)s
            """
            
            # Parâmetros da query
            params = {
                'embedding_param': embedding_str,
                'model_name': self.embedding_model,
                'similarity_threshold': similarity_threshold,
                'limit': limit
            }
            
            if product_filter:
                params['product_filter'] = f'%{product_filter}%'
            
            # Executar query
            result = db.execute(text(base_query), params)
            rows = result.fetchall()
            
            # Converter para lista de dicionários
            results = []
            for row in rows:
                results.append({
                    'embedding_id': str(row.embedding_id),
                    'chunk_id': str(row.chunk_id),
                    'version_id': str(row.version_id),
                    'model_name': row.model_name,
                    'chunk_index': row.chunk_index,
                    'text': row.text,
                    'chunk_metadata': row.chunk_metadata,
                    'document_id': str(row.document_id),
                    'original_name': row.original_name,
                    'document_metadata': row.document_metadata,
                    'version_tag': row.version_tag,
                    'similarity_score': float(row.similarity_score)
                })
            
            logger.debug(f"Busca vetorial retornou {len(results)} resultados")
            return results
            
        except Exception as e:
            logger.error(f"Erro na busca vetorial: {e}")
            return []
    
    def hybrid_search(
        self,
        query: str,
        limit: int = 10,
        semantic_weight: float = 0.7,
        product_filter: Optional[str] = None,
        db_session: Optional[Session] = None  # Sessão opcional do banco
    ) -> List[Dict[str, Any]]:
        """
        Realiza busca híbrida combinando busca semântica e textual.

        Args:
            query: Consulta em linguagem natural
            limit: Número máximo de resultados
            semantic_weight: Peso da busca semântica (0.0 a 1.0)
            product_filter: Filtrar por produto específico

        Returns:
            Lista de resultados combinados e ranqueados
        """
        # Garantir que o logger está disponível
        import logging
        local_logger = logging.getLogger(__name__)

        try:
            # Busca semântica
            semantic_results = self.semantic_search(
                query=query,
                limit=limit * 2,  # Buscar mais para combinar
                similarity_threshold=0.3,  # Limiar mais baixo para híbrida
                product_filter=product_filter,
                db_session=db_session  # Passar sessão do banco
            )
            
            # Busca textual simples
            textual_results = self._textual_search(
                query=query,
                limit=limit * 2,
                product_filter=product_filter,
                db_session=db_session  # Passar sessão do banco
            )
            
            # Combinar e ranquear resultados
            combined_results = self._combine_search_results(
                semantic_results=semantic_results,
                textual_results=textual_results,
                semantic_weight=semantic_weight,
                limit=limit
            )
            
            local_logger.info(f"✅ Busca híbrida retornou {len(combined_results)} resultados")
            return combined_results

        except Exception as e:
            local_logger.error(f"❌ Erro na busca híbrida: {e}")
            return []
    
    def _textual_search(
        self,
        query: str,
        limit: int,
        product_filter: Optional[str] = None,
        db_session: Optional[Session] = None  # Sessão opcional do banco
    ) -> List[Dict[str, Any]]:
        """Busca textual simples usando ILIKE e full-text search."""
        try:
            # Usar sessão fornecida ou criar nova
            if db_session:
                db = db_session
                close_db = False
            else:
                db = self.db_config.SessionLocal()
                close_db = True

            try:
                base_query = """
                    SELECT
                        cp.chunk_id,
                        cp.version_id,
                        cp.chunk_index,
                        cp.text,
                        cp.metadata as chunk_metadata,
                        d.document_id,
                        d.original_name,
                        d.metadata as document_metadata,
                        v.version_tag,
                        ts_rank_cd(to_tsvector('portuguese', cp.text), plainto_tsquery('portuguese', :query)) as text_rank
                    FROM chunks_parent cp
                    JOIN versions v ON cp.version_id = v.version_id
                    JOIN documents d ON v.document_id = d.document_id
                    WHERE (
                        cp.text ILIKE :query_pattern
                        OR to_tsvector('portuguese', cp.text) @@ plainto_tsquery('portuguese', :query)
                    )
                """
                
                if product_filter:
                    base_query += """
                        AND (
                            d.original_name ILIKE :product_filter
                            OR d.metadata::text ILIKE :product_filter
                        )
                    """
                
                base_query += """
                    ORDER BY text_rank DESC, cp.chunk_index ASC
                    LIMIT :limit
                """
                
                params = {
                    'query': query,
                    'query_pattern': f'%{query}%',
                    'limit': limit
                }
                
                if product_filter:
                    params['product_filter'] = f'%{product_filter}%'
                
                result = db.execute(text(base_query), params)
                rows = result.fetchall()
                
                results = []
                for row in rows:
                    results.append({
                        'chunk_id': str(row.chunk_id),
                        'version_id': str(row.version_id),
                        'chunk_index': row.chunk_index,
                        'text': row.text,
                        'chunk_metadata': row.chunk_metadata,
                        'document_id': str(row.document_id),
                        'original_name': row.original_name,
                        'document_metadata': row.document_metadata,
                        'version_tag': row.version_tag,
                        'text_rank': float(row.text_rank) if row.text_rank else 0.0
                    })
                
                return results
                
            finally:
                if close_db:
                    db.close()
                
        except Exception as e:
            logger.error(f"Erro na busca textual: {e}")
            return []
    
    def _combine_search_results(
        self,
        semantic_results: List[Dict[str, Any]],
        textual_results: List[Dict[str, Any]],
        semantic_weight: float,
        limit: int
    ) -> List[Dict[str, Any]]:
        """Combina e ranqueia resultados de busca semântica e textual."""
        try:
            # Criar dicionário para combinar resultados por chunk_id
            combined = {}
            
            # Adicionar resultados semânticos
            for result in semantic_results:
                chunk_id = result['chunk_id']
                combined[chunk_id] = result.copy()
                combined[chunk_id]['semantic_score'] = result.get('similarity_score', 0.0)
                combined[chunk_id]['textual_score'] = 0.0
            
            # Adicionar/combinar resultados textuais
            for result in textual_results:
                chunk_id = result['chunk_id']
                if chunk_id in combined:
                    # Chunk já existe, adicionar score textual
                    combined[chunk_id]['textual_score'] = result.get('text_rank', 0.0)
                else:
                    # Novo chunk, adicionar com score textual
                    combined[chunk_id] = result.copy()
                    combined[chunk_id]['semantic_score'] = 0.0
                    combined[chunk_id]['textual_score'] = result.get('text_rank', 0.0)
            
            # Calcular score combinado e ordenar
            for chunk_id in combined:
                semantic_score = combined[chunk_id]['semantic_score']
                textual_score = combined[chunk_id]['textual_score']
                
                # Normalizar scores (0-1)
                normalized_semantic = min(semantic_score, 1.0)
                normalized_textual = min(textual_score / 10.0, 1.0)  # text_rank pode ser > 1
                
                # Score combinado
                combined_score = (
                    semantic_weight * normalized_semantic +
                    (1 - semantic_weight) * normalized_textual
                )
                
                combined[chunk_id]['combined_score'] = combined_score
            
            # Ordenar por score combinado e limitar
            sorted_results = sorted(
                combined.values(),
                key=lambda x: x['combined_score'],
                reverse=True
            )
            
            return sorted_results[:limit]
            
        except Exception as e:
            logger.error(f"Erro ao combinar resultados: {e}")
            return []
