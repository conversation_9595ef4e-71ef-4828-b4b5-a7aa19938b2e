"""
M10 AI Orchestrator - Natural Language Query Processor

Processador para interpretar consultas em linguagem natural e convertê-las
em operações estruturadas de análise de dados.
"""

from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import re
import logging
from .llm_service import LLMService

logger = logging.getLogger(__name__)


class QueryType(Enum):
    """Tipos de consulta suportados."""
    PRODUCT_ANALYSIS = "product_analysis"
    VERSION_COMPARISON = "version_comparison"
    DOCUMENT_SEARCH = "document_search"
    DATABASE_STATS = "database_stats"
    PRODUCT_LIST = "product_list"
    GENERAL_CHAT = "general_chat"


@dataclass
class QueryIntent:
    """Intenção extraída de uma consulta."""
    query_type: QueryType
    confidence: float
    entities: Dict[str, Any]
    parameters: Dict[str, Any]
    original_query: str


@dataclass
class ExtractedEntity:
    """Entidade extraída do texto."""
    entity_type: str
    value: str
    confidence: float
    position: Tuple[int, int]


class NLQueryProcessor:
    """
    Processador de consultas em linguagem natural.
    
    Analisa consultas do usuário e extrai:
    - Tipo de operação desejada
    - Entidades relevantes (produtos, versões, etc.)
    - Parâmetros para a análise
    """
    
    def __init__(self, llm_service: Optional[LLMService] = None):
        self.llm_service = llm_service or LLMService()
        
        # Padrões regex para detecção rápida (ordem de prioridade: mais específicos primeiro)
        self.patterns = {
            'product_list': [
                r'(?:quais?|que)\s+produtos?.*(?:temos|disponíveis?|na\s+base)',
                r'lista(?:r?).*produtos?',
                r'produtos?\s+(?:disponíveis?|na\s+base)',
                r'listar?\s+produtos?',
            ],
            'version_comparison': [
                r'compar[ae].*versões?\s+(?:de\s+)?(["\']?)([^"\']+)\1.*(?:com|e|para)\s+(["\']?)([^"\']+)\3',
                r'diferenças?\s+entre\s+(["\']?)([^"\']+)\1.*(?:e|com)\s+(["\']?)([^"\']+)\3',
                r'mudanças?\s+(?:da|de)\s+versão\s+(["\']?)([^"\']+)\1.*para\s+(["\']?)([^"\']+)\3',
            ],
            'product_analysis': [
                r'(?:me\s+)?(?:traga|faça|gere).*resumo.*(?:do\s+documento\s+|geral.*do\s+|de\s+|sobre\s+)(["\']?)([A-Za-z0-9\-_]+)\1',
                r'analis[ae].*(?:produto\s+|detalhada.*de\s+|o\s+)(["\']?)([A-Za-z0-9\-_]+)\1',
                r'relatório.*(?:do\s+produto\s+|sobre\s+)(["\']?)([A-Za-z0-9\-_]+)\1',
                r'(?:detalhes|informações).*do\s+(["\']?)([A-Za-z0-9\-_]+)\1',
                r'resumo.*(?:do\s+documento\s+|geral.*do\s+)(["\']?)([A-Za-z0-9\-_]+)\1',
                r'análise\s+do\s+produto\s+([A-Za-z0-9\-_]+)',
            ],
            'document_search': [
                r'(?:quais?|que)\s+documentos?.*(?:falam|sobre).*(?:de\s+|sobre\s+)(["\']?)([A-Za-z0-9\-_]{2,})\1(?:\s+(?:temos|na\s+base|disponíveis?))?',
                r'documentos?.*(?:que.*falam|sobre).*(?:de\s+|sobre\s+)(["\']?)([A-Za-z0-9\-_]{2,})\1(?:\s+(?:temos|na\s+base|disponíveis?))?',
                r'(?:buscar?|encontrar?|procurar?)\s+(?:documentos?|arquivos?).*(?:sobre\s+|de\s+|por\s+)(["\']?)([A-Za-z0-9\-_]{2,})\1',
                r'o\s+que.*(?:sobre|falar.*sobre)\s+o?\s*(["\']?)([A-Za-z0-9\-_]{2,})\1',
                r'(?:informações?|conte).*sobre\s+o?\s*(["\']?)([A-Za-z0-9\-_]{2,})\1',
                r'(?:temos|sabe).*sobre\s+o?\s*(["\']?)([A-Za-z0-9\-_]{2,})\1',
            ],
            'database_stats': [
                r'estatísticas?\s+da\s+base',
                r'quantos?\s+documentos?(?:\s+(?:temos|na\s+base))?$',
                r'total\s+de\s+(?:arquivos?|documentos?)',
                r'resumo\s+da\s+base\s+de\s+dados',
                r'informações?\s+(?:da|sobre)\s+base',
            ]
        }
    
    def process_query(self, query: str) -> QueryIntent:
        """
        Processa uma consulta em linguagem natural.
        
        Args:
            query: Consulta do usuário
            
        Returns:
            Intenção extraída com entidades e parâmetros
        """
        try:
            # Normalizar query
            normalized_query = query.lower().strip()
            
            # Tentar detecção rápida com regex
            quick_result = self._quick_pattern_detection(normalized_query)
            if quick_result:
                # Melhorar entidades usando LLM
                llm_entities = self._extract_entities_with_llm(query, quick_result.query_type)
                if llm_entities:
                    # Combinar entidades do regex com LLM
                    combined_entities = {**quick_result.entities, **llm_entities}
                    quick_result.entities = combined_entities
                return quick_result

            # Usar LLM para análise mais sofisticada
            llm_result = self._llm_intent_analysis(query)
            if llm_result:
                return llm_result
            
            # Fallback para chat geral
            return QueryIntent(
                query_type=QueryType.GENERAL_CHAT,
                confidence=0.5,
                entities={},
                parameters={},
                original_query=query
            )
            
        except Exception as e:
            logger.error(f"Erro ao processar consulta: {e}")
            return QueryIntent(
                query_type=QueryType.GENERAL_CHAT,
                confidence=0.1,
                entities={},
                parameters={'error': str(e)},
                original_query=query
            )
    
    def _quick_pattern_detection(self, query: str) -> Optional[QueryIntent]:
        """Detecção rápida usando padrões regex."""

        # Verificar lista de produtos (prioridade alta)
        for pattern in self.patterns['product_list']:
            if re.search(pattern, query, re.IGNORECASE):
                logger.info(f"Padrão PRODUCT_LIST encontrado: {pattern}")
                return QueryIntent(
                    query_type=QueryType.PRODUCT_LIST,
                    confidence=0.9,
                    entities={},
                    parameters={},
                    original_query=query
                )

        # Verificar análise de produto
        for pattern in self.patterns['product_analysis']:
            match = re.search(pattern, query, re.IGNORECASE)
            if match:
                # Extrair nome do produto de forma mais robusta
                product_name = None
                for group_idx in range(1, len(match.groups()) + 1):
                    group_value = match.group(group_idx)
                    if group_value and group_value not in ['"', "'", "", " "]:
                        product_name = group_value.strip()
                        break

                # Se não encontrou nos grupos, tentar extrair da query
                if not product_name:
                    # Padrões simples para extrair nomes de produtos
                    simple_patterns = [
                        r'(?:documento|produto)\s+([A-Za-z0-9\-_]+)',
                        r'([A-Za-z0-9\-_]+)(?:\s*$)',
                    ]
                    for simple_pattern in simple_patterns:
                        simple_match = re.search(simple_pattern, query, re.IGNORECASE)
                        if simple_match:
                            product_name = simple_match.group(1).strip()
                            break

                logger.info(f"Padrão PRODUCT_ANALYSIS encontrado: {pattern} -> produto: {product_name}")
                return QueryIntent(
                    query_type=QueryType.PRODUCT_ANALYSIS,
                    confidence=0.8,
                    entities={'product_name': product_name or 'unknown'},
                    parameters={'analysis_type': 'full'},
                    original_query=query
                )
        
        # Verificar comparação de versões
        for pattern in self.patterns['version_comparison']:
            match = re.search(pattern, query, re.IGNORECASE)
            if match and len(match.groups()) >= 4:
                version_a = match.group(2).strip()
                version_b = match.group(4).strip()
                return QueryIntent(
                    query_type=QueryType.VERSION_COMPARISON,
                    confidence=0.8,
                    entities={
                        'version_a': version_a,
                        'version_b': version_b
                    },
                    parameters={'comparison_type': 'detailed'},
                    original_query=query
                )
        
        # Verificar busca de documentos
        for pattern in self.patterns['document_search']:
            match = re.search(pattern, query, re.IGNORECASE)
            if match:
                # Extrair termo de busca de forma mais robusta
                search_term = None
                for group_idx in range(1, len(match.groups()) + 1):
                    group_value = match.group(group_idx)
                    if group_value and group_value not in ['"', "'", "", " "]:
                        search_term = group_value.strip()
                        break

                logger.info(f"Padrão DOCUMENT_SEARCH encontrado: {pattern} -> termo: {search_term}")
                return QueryIntent(
                    query_type=QueryType.DOCUMENT_SEARCH,
                    confidence=0.7,
                    entities={'search_term': search_term or 'unknown'},
                    parameters={'limit': 20},
                    original_query=query
                )
        
        # Verificar estatísticas
        for pattern in self.patterns['database_stats']:
            if re.search(pattern, query, re.IGNORECASE):
                return QueryIntent(
                    query_type=QueryType.DATABASE_STATS,
                    confidence=0.9,
                    entities={},
                    parameters={},
                    original_query=query
                )
        
        return None
    
    def _llm_intent_analysis(self, query: str) -> Optional[QueryIntent]:
        """Análise de intenção usando LLM."""
        try:
            system_prompt = """Você é um especialista em análise de intenções para um sistema de análise de documentos.

Analise a consulta do usuário e identifique:
1. Tipo de operação (product_analysis, version_comparison, document_search, database_stats, general_chat)
2. Entidades mencionadas (nomes de produtos, versões, termos de busca)
3. Parâmetros relevantes

Responda APENAS em formato JSON:
{
    "query_type": "tipo_da_operacao",
    "confidence": 0.95,
    "entities": {"produto": "nome_do_produto", "versao": "v1.0"},
    "parameters": {"tipo_analise": "completa"}
}"""
            
            response = self.llm_service.simple_completion(
                prompt=query,
                system_prompt=system_prompt,
                temperature=0.1,
                max_tokens=200
            )
            
            # Tentar parsear resposta JSON
            import json
            result_data = json.loads(response.content.strip())
            
            # Validar e converter
            query_type = QueryType(result_data.get('query_type', 'general_chat'))
            confidence = float(result_data.get('confidence', 0.5))
            entities = result_data.get('entities', {})
            parameters = result_data.get('parameters', {})
            
            return QueryIntent(
                query_type=query_type,
                confidence=confidence,
                entities=entities,
                parameters=parameters,
                original_query=query
            )
            
        except Exception as e:
            logger.warning(f"Erro na análise LLM: {e}")
            return None

    def _extract_entities_with_llm(self, query: str, query_type: QueryType) -> Dict[str, Any]:
        """
        Extrai entidades usando LLM para análise mais robusta.

        Args:
            query: Consulta do usuário
            query_type: Tipo de consulta identificado

        Returns:
            Dicionário com entidades extraídas
        """
        try:
            # Prompt específico para extração de entidades
            system_prompt = f"""Você é um especialista em extração de entidades de consultas sobre documentos.

Analise a consulta do usuário e extraia as entidades relevantes baseado no tipo de consulta: {query_type.value}

REGRAS DE EXTRAÇÃO:
- Para PRODUCT_ANALYSIS: extraia o nome EXATO do produto/documento mencionado
- Para DOCUMENT_SEARCH: extraia os termos de busca principais (ignore palavras como "base", "dados", "temos")
- Para VERSION_COMPARISON: extraia os nomes dos produtos/versões a comparar
- Preserve maiúsculas/minúsculas dos nomes de produtos
- Para termos de busca, extraia apenas as palavras-chave relevantes

EXEMPLOS:
- "Quais documentos que falam de testes temos na base de dados?" → search_terms: ["testes"]
- "Ok, me traga um resumo geral do documento ChecklistAnchor" → product_name: "ChecklistAnchor"
- "análise do produto ChecklistAnchor" → product_name: "ChecklistAnchor"

Retorne APENAS um JSON válido com as entidades extraídas:
{{
    "product_name": "nome_do_produto_se_aplicavel",
    "search_terms": ["termo1", "termo2"],
    "version_1": "versao1_se_aplicavel",
    "version_2": "versao2_se_aplicavel"
}}

Se não conseguir extrair uma entidade, use null."""

            user_prompt = f"Consulta: {query}"

            response = self.llm_service.generate_response(
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                max_tokens=200,
                temperature=0.1
            )

            if response and response.content.strip():
                # Tentar parsear JSON
                import json
                try:
                    entities = json.loads(response.content.strip())
                    logger.info(f"Entidades extraídas via LLM: {entities}")
                    return entities
                except json.JSONDecodeError:
                    logger.warning(f"LLM retornou JSON inválido: {response.content}")
                    return {}

            return {}

        except Exception as e:
            logger.error(f"Erro na extração de entidades via LLM: {e}")
            return {}

    def extract_entities(self, text: str) -> List[ExtractedEntity]:
        """
        Extrai entidades específicas do texto.
        
        Args:
            text: Texto para análise
            
        Returns:
            Lista de entidades extraídas
        """
        entities = []
        
        # Padrões para diferentes tipos de entidades
        entity_patterns = {
            'product_name': [
                r'produto\s+(["\']?)([A-Za-z0-9\s\-_]+)\1',
                r'material\s+(["\']?)([A-Za-z0-9\s\-_]+)\1',
            ],
            'version': [
                r'versão\s+(["\']?)([v]?[\d\.]+)\1',
                r'v\.?\s*(\d+\.?\d*)',
            ],
            'document_type': [
                r'(manual|especificação|relatório|documento)',
                r'(pdf|doc|docx|txt)',
            ]
        }
        
        for entity_type, patterns in entity_patterns.items():
            for pattern in patterns:
                matches = re.finditer(pattern, text, re.IGNORECASE)
                for match in matches:
                    value = match.group(2) if len(match.groups()) >= 2 else match.group(1)
                    entities.append(ExtractedEntity(
                        entity_type=entity_type,
                        value=value.strip(),
                        confidence=0.8,
                        position=(match.start(), match.end())
                    ))
        
        return entities
    
    def suggest_queries(self, partial_query: str) -> List[str]:
        """
        Sugere consultas baseadas em entrada parcial.
        
        Args:
            partial_query: Consulta parcial do usuário
            
        Returns:
            Lista de sugestões de consulta
        """
        suggestions = []
        
        partial_lower = partial_query.lower()
        
        # Sugestões baseadas em padrões comuns
        if 'analis' in partial_lower or 'produto' in partial_lower:
            suggestions.extend([
                "Analise todos os materiais do produto X",
                "Mostre informações sobre o produto Y",
                "Quais documentos temos sobre o produto Z?"
            ])
        
        if 'compar' in partial_lower or 'versão' in partial_lower:
            suggestions.extend([
                "Compare as versões v1.0 e v2.0 do produto X",
                "Quais são as diferenças entre as versões?",
                "Mostre mudanças da versão anterior para atual"
            ])
        
        if 'buscar' in partial_lower or 'procurar' in partial_lower:
            suggestions.extend([
                "Buscar documentos sobre segurança",
                "Encontrar arquivos relacionados a qualidade",
                "Procurar por especificações técnicas"
            ])
        
        if 'estatística' in partial_lower or 'quantos' in partial_lower:
            suggestions.extend([
                "Quantos documentos temos na base?",
                "Estatísticas da base de dados",
                "Resumo geral dos arquivos"
            ])
        
        return suggestions[:5]  # Limitar a 5 sugestões
