"""
M10 AI Orchestrator - Data Analysis Service

Serviço para análise de dados da base de documentos, incluindo:
- Busca por produtos e versões
- Análise comparativa entre versões
- Extração de metadados e estatísticas
- Geração de insights automáticos
"""

from typing import List, Dict, Any, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import text, func, and_, or_
from datetime import datetime, timedelta
import json
import logging
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class DocumentAnalysis:
    """Resultado de análise de um documento."""
    document_id: str
    original_name: str
    version_tag: str
    chunk_count: int
    word_count: int
    language_detected: str
    created_at: datetime
    metadata: Dict[str, Any]
    content_sample: str


@dataclass
class ProductAnalysis:
    """Resultado de análise de um produto."""
    product_name: str
    total_documents: int
    total_versions: int
    languages: List[str]
    date_range: Tuple[datetime, datetime]
    documents: List[DocumentAnalysis]


@dataclass
class ComparisonResult:
    """Resultado de comparação entre versões."""
    product_name: str
    version_a: str
    version_b: str
    differences_count: int
    similarity_score: float
    added_content: List[str]
    removed_content: List[str]
    modified_content: List[str]
    summary: str


class DataAnalysisService:
    """
    Serviço para análise avançada de dados da base de documentos.
    
    Fornece funcionalidades para:
    - Buscar documentos por produto/categoria
    - Analisar versões e mudanças
    - Gerar estatísticas e insights
    - Comparar diferentes versões de documentos
    """
    
    def __init__(self, db_session: Session):
        self.db = db_session
        
    def search_documents_by_product(self, product_name: str, limit: int = 50) -> List[DocumentAnalysis]:
        """
        Busca documentos relacionados a um produto específico.
        
        Args:
            product_name: Nome do produto para buscar
            limit: Limite de resultados
            
        Returns:
            Lista de análises de documentos encontrados
        """
        try:
            # Query usando a view document_full para buscar por produto
            query = text("""
                SELECT DISTINCT
                    d.document_id,
                    d.original_name,
                    v.version_tag,
                    COUNT(cp.chunk_id) as chunk_count,
                    SUM(LENGTH(cp.text)) as total_chars,
                    d.metadata,
                    d.created_at,
                    STRING_AGG(SUBSTRING(cp.text, 1, 200), ' | ') as content_sample
                FROM ai_orchestrator.documents d
                JOIN ai_orchestrator.versions v ON d.document_id = v.document_id
                LEFT JOIN ai_orchestrator.chunks_parent cp ON v.version_id = cp.version_id
                WHERE 
                    LOWER(d.original_name) LIKE LOWER(:product_pattern)
                    OR d.metadata::text ILIKE :product_pattern
                    OR cp.text ILIKE :product_pattern
                GROUP BY d.document_id, d.original_name, v.version_tag, d.metadata, d.created_at
                ORDER BY d.created_at DESC
                LIMIT :limit
            """)
            
            result = self.db.execute(query, {
                'product_pattern': f'%{product_name}%',
                'limit': limit
            })
            
            documents = []
            for row in result:
                # Extrair metadados
                metadata = row.metadata if row.metadata else {}
                
                # Detectar idioma dos metadados ou conteúdo
                language = metadata.get('language', 'unknown')
                
                # Calcular contagem aproximada de palavras
                word_count = row.total_chars // 5 if row.total_chars else 0
                
                doc_analysis = DocumentAnalysis(
                    document_id=str(row.document_id),
                    original_name=row.original_name,
                    version_tag=row.version_tag or 'v1',
                    chunk_count=row.chunk_count or 0,
                    word_count=word_count,
                    language_detected=language,
                    created_at=row.created_at,
                    metadata=metadata,
                    content_sample=row.content_sample or ''
                )
                documents.append(doc_analysis)
            
            logger.info(f"Encontrados {len(documents)} documentos para produto '{product_name}'")
            return documents
            
        except Exception as e:
            logger.error(f"Erro ao buscar documentos por produto: {e}")
            return []
    
    def analyze_product_versions(self, product_name: str) -> ProductAnalysis:
        """
        Analisa todas as versões de um produto específico.
        
        Args:
            product_name: Nome do produto para analisar
            
        Returns:
            Análise completa do produto
        """
        try:
            documents = self.search_documents_by_product(product_name, limit=100)
            
            if not documents:
                return ProductAnalysis(
                    product_name=product_name,
                    total_documents=0,
                    total_versions=0,
                    languages=[],
                    date_range=(datetime.now(), datetime.now()),
                    documents=[]
                )
            
            # Extrair estatísticas
            versions = set(doc.version_tag for doc in documents)
            languages = list(set(doc.language_detected for doc in documents if doc.language_detected != 'unknown'))
            dates = [doc.created_at for doc in documents]
            
            analysis = ProductAnalysis(
                product_name=product_name,
                total_documents=len(documents),
                total_versions=len(versions),
                languages=languages,
                date_range=(min(dates), max(dates)) if dates else (datetime.now(), datetime.now()),
                documents=documents
            )
            
            logger.info(f"Análise do produto '{product_name}': {len(documents)} docs, {len(versions)} versões")
            return analysis
            
        except Exception as e:
            logger.error(f"Erro ao analisar versões do produto: {e}")
            return ProductAnalysis(
                product_name=product_name,
                total_documents=0,
                total_versions=0,
                languages=[],
                date_range=(datetime.now(), datetime.now()),
                documents=[]
            )
    
    def compare_document_versions(self, document_name: str, version_a: str, version_b: str) -> ComparisonResult:
        """
        Compara duas versões de um documento.
        
        Args:
            document_name: Nome do documento
            version_a: Primeira versão para comparar
            version_b: Segunda versão para comparar
            
        Returns:
            Resultado da comparação
        """
        try:
            # Buscar conteúdo das duas versões
            query = text("""
                SELECT 
                    v.version_tag,
                    STRING_AGG(cp.text, ' ' ORDER BY cp.chunk_index) as full_text
                FROM ai_orchestrator.documents d
                JOIN ai_orchestrator.versions v ON d.document_id = v.document_id
                JOIN ai_orchestrator.chunks_parent cp ON v.version_id = cp.version_id
                WHERE 
                    LOWER(d.original_name) LIKE LOWER(:doc_pattern)
                    AND v.version_tag IN (:version_a, :version_b)
                GROUP BY v.version_tag
            """)
            
            result = self.db.execute(query, {
                'doc_pattern': f'%{document_name}%',
                'version_a': version_a,
                'version_b': version_b
            })
            
            versions_content = {row.version_tag: row.full_text for row in result}
            
            if len(versions_content) < 2:
                return ComparisonResult(
                    product_name=document_name,
                    version_a=version_a,
                    version_b=version_b,
                    differences_count=0,
                    similarity_score=0.0,
                    added_content=[],
                    removed_content=[],
                    modified_content=[],
                    summary="Não foi possível encontrar ambas as versões para comparação."
                )
            
            # Análise simples de diferenças (pode ser melhorada com diff algorithms)
            text_a = versions_content.get(version_a, '')
            text_b = versions_content.get(version_b, '')
            
            # Calcular similaridade básica
            words_a = set(text_a.lower().split())
            words_b = set(text_b.lower().split())
            
            intersection = words_a.intersection(words_b)
            union = words_a.union(words_b)
            similarity = len(intersection) / len(union) if union else 0.0
            
            # Identificar diferenças básicas
            added_words = list(words_b - words_a)[:10]  # Primeiras 10
            removed_words = list(words_a - words_b)[:10]  # Primeiras 10
            
            differences_count = len(words_a.symmetric_difference(words_b))
            
            summary = f"Similaridade: {similarity:.2%}. "
            summary += f"Adicionadas {len(added_words)} palavras, removidas {len(removed_words)} palavras."
            
            return ComparisonResult(
                product_name=document_name,
                version_a=version_a,
                version_b=version_b,
                differences_count=differences_count,
                similarity_score=similarity,
                added_content=added_words,
                removed_content=removed_words,
                modified_content=[],  # Implementar análise mais sofisticada se necessário
                summary=summary
            )
            
        except Exception as e:
            logger.error(f"Erro ao comparar versões: {e}")
            return ComparisonResult(
                product_name=document_name,
                version_a=version_a,
                version_b=version_b,
                differences_count=0,
                similarity_score=0.0,
                added_content=[],
                removed_content=[],
                modified_content=[],
                summary=f"Erro na comparação: {str(e)}"
            )
    
    def get_database_statistics(self) -> Dict[str, Any]:
        """
        Obtém estatísticas gerais da base de dados.
        
        Returns:
            Dicionário com estatísticas da base
        """
        try:
            stats_query = text("""
                SELECT 
                    COUNT(DISTINCT d.document_id) as total_documents,
                    COUNT(DISTINCT v.version_id) as total_versions,
                    COUNT(cp.chunk_id) as total_chunks,
                    AVG(LENGTH(cp.text)) as avg_chunk_size,
                    COUNT(DISTINCT t.language_code) as total_languages
                FROM ai_orchestrator.documents d
                LEFT JOIN ai_orchestrator.versions v ON d.document_id = v.document_id
                LEFT JOIN ai_orchestrator.chunks_parent cp ON v.version_id = cp.version_id
                LEFT JOIN ai_orchestrator.translations t ON cp.version_id = t.version_id AND cp.chunk_id = t.chunk_id
            """)
            
            result = self.db.execute(stats_query).first()
            
            return {
                'total_documents': result.total_documents or 0,
                'total_versions': result.total_versions or 0,
                'total_chunks': result.total_chunks or 0,
                'avg_chunk_size': round(result.avg_chunk_size or 0, 2),
                'total_languages': result.total_languages or 0,
                'last_updated': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Erro ao obter estatísticas: {e}")
            return {
                'total_documents': 0,
                'total_versions': 0,
                'total_chunks': 0,
                'avg_chunk_size': 0,
                'total_languages': 0,
                'error': str(e)
            }

    def get_available_products(self) -> Dict[str, Any]:
        """
        Lista todos os produtos disponíveis na base de dados.

        Returns:
            Dicionário com produtos e suas informações
        """
        try:
            products_query = text("""
                SELECT
                    CASE
                        WHEN d.metadata->>'product_name' IS NOT NULL
                        THEN d.metadata->>'product_name'
                        ELSE REGEXP_REPLACE(
                            SPLIT_PART(d.original_name, '.', 1),
                            '[0-9\-_]+', '', 'g'
                        )
                    END as product_name,
                    COUNT(DISTINCT d.document_id) as document_count,
                    COUNT(DISTINCT v.version_id) as version_count,
                    MIN(d.created_at) as first_import,
                    MAX(d.created_at) as last_import,
                    STRING_AGG(DISTINCT d.original_name, ', ' ORDER BY d.original_name) as sample_files
                FROM ai_orchestrator.documents d
                LEFT JOIN ai_orchestrator.versions v ON d.document_id = v.document_id
                GROUP BY
                    CASE
                        WHEN d.metadata->>'product_name' IS NOT NULL
                        THEN d.metadata->>'product_name'
                        ELSE REGEXP_REPLACE(
                            SPLIT_PART(d.original_name, '.', 1),
                            '[0-9\-_]+', '', 'g'
                        )
                    END
                HAVING COUNT(DISTINCT d.document_id) > 0
                ORDER BY document_count DESC, product_name
            """)

            result = self.db.execute(products_query)

            products = []
            for row in result:
                # Limpar nome do produto
                product_name = row.product_name.strip()
                if not product_name or len(product_name) < 2:
                    product_name = "Documentos Diversos"

                products.append({
                    'name': product_name,
                    'document_count': row.document_count,
                    'version_count': row.version_count,
                    'first_import': row.first_import.strftime('%d/%m/%Y') if row.first_import else 'N/A',
                    'last_import': row.last_import.strftime('%d/%m/%Y') if row.last_import else 'N/A',
                    'sample_files': row.sample_files[:200] + '...' if len(row.sample_files) > 200 else row.sample_files
                })

            return {
                'products': products,
                'total_products': len(products),
                'total_documents': sum(p['document_count'] for p in products),
                'last_updated': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Erro ao obter produtos disponíveis: {e}")
            return {
                'products': [],
                'total_products': 0,
                'total_documents': 0,
                'error': str(e)
            }
