"""
M10 AI Orchestrator - PDF Exporter

Serviço para exportar relatórios HTML para arquivos PDF.
Suporta diferentes bibliotecas de conversão e templates personalizados.
"""

from typing import Dict, Any, Optional, Union
import os
import tempfile
import logging
from pathlib import Path
from datetime import datetime
import asyncio

logger = logging.getLogger(__name__)


class PDFExporter:
    """
    Exportador de relatórios para PDF.
    
    Suporta múltiplas bibliotecas de conversão:
    - WeasyPrint (recomendado para produção)
    - Playwright (fallback)
    - Reportlab (para casos específicos)
    """
    
    def __init__(self, export_dir: Optional[str] = None):
        self.export_dir = Path(export_dir) if export_dir else Path(tempfile.gettempdir()) / "m10_reports"
        self.export_dir.mkdir(exist_ok=True)
        
        # Detectar bibliotecas disponíveis
        self.available_engines = self._detect_available_engines()
        logger.info(f"PDF engines disponíveis: {self.available_engines}")
    
    def _detect_available_engines(self) -> Dict[str, bool]:
        """Detecta quais engines de PDF estão disponíveis."""
        engines = {}
        
        # Testar WeasyPrint
        try:
            import weasyprint
            engines['weasyprint'] = True
        except ImportError:
            engines['weasyprint'] = False
        
        # Testar Playwright
        try:
            from playwright.sync_api import sync_playwright
            engines['playwright'] = True
        except ImportError:
            engines['playwright'] = False
        
        # Testar ReportLab
        try:
            from reportlab.pdfgen import canvas
            engines['reportlab'] = True
        except ImportError:
            engines['reportlab'] = False
        
        return engines
    
    def export_html_to_pdf(
        self, 
        html_content: str, 
        filename: Optional[str] = None,
        engine: str = 'auto'
    ) -> Dict[str, Any]:
        """
        Exporta conteúdo HTML para PDF.
        
        Args:
            html_content: Conteúdo HTML para converter
            filename: Nome do arquivo (opcional)
            engine: Engine a usar ('auto', 'weasyprint', 'playwright', 'reportlab')
            
        Returns:
            Dicionário com informações do arquivo gerado
        """
        try:
            # Determinar engine a usar
            if engine == 'auto':
                if self.available_engines.get('weasyprint'):
                    engine = 'weasyprint'
                elif self.available_engines.get('playwright'):
                    engine = 'playwright'
                else:
                    raise Exception("Nenhum engine de PDF disponível")
            
            # Gerar nome do arquivo se não fornecido
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"relatorio_{timestamp}.pdf"
            
            if not filename.endswith('.pdf'):
                filename += '.pdf'
            
            output_path = self.export_dir / filename
            
            # Converter usando engine selecionado
            if engine == 'weasyprint':
                result = self._export_with_weasyprint(html_content, output_path)
            elif engine == 'playwright':
                result = self._export_with_playwright(html_content, output_path)
            elif engine == 'reportlab':
                result = self._export_with_reportlab(html_content, output_path)
            else:
                raise ValueError(f"Engine não suportado: {engine}")
            
            # Verificar se arquivo foi criado
            if not output_path.exists():
                raise Exception("Arquivo PDF não foi criado")
            
            file_size = output_path.stat().st_size
            
            return {
                'success': True,
                'filename': filename,
                'filepath': str(output_path),
                'file_size': file_size,
                'engine_used': engine,
                'created_at': datetime.now().isoformat(),
                'download_url': f'/reports/download/{filename}'
            }
            
        except Exception as e:
            logger.error(f"Erro ao exportar PDF: {e}")
            return {
                'success': False,
                'error': str(e),
                'engine_attempted': engine
            }
    
    def _export_with_weasyprint(self, html_content: str, output_path: Path) -> bool:
        """Exporta usando WeasyPrint."""
        try:
            import weasyprint
            from weasyprint import HTML, CSS
            
            # CSS adicional para impressão
            print_css = CSS(string="""
                @page {
                    size: A4;
                    margin: 2cm;
                }
                body {
                    font-size: 12pt;
                    line-height: 1.4;
                }
                .export-btn {
                    display: none !important;
                }
                .container {
                    box-shadow: none !important;
                    border-radius: 0 !important;
                }
            """)
            
            # Converter HTML para PDF
            html_doc = HTML(string=html_content)
            html_doc.write_pdf(str(output_path), stylesheets=[print_css])
            
            logger.info(f"PDF gerado com WeasyPrint: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Erro no WeasyPrint: {e}")
            raise
    
    def _export_with_playwright(self, html_content: str, output_path: Path) -> bool:
        """Exporta usando Playwright."""
        try:
            from playwright.sync_api import sync_playwright
            
            with sync_playwright() as p:
                browser = p.chromium.launch()
                page = browser.new_page()
                
                # Carregar HTML
                page.set_content(html_content)
                
                # Aguardar carregamento completo
                page.wait_for_load_state('networkidle')
                
                # Configurações de PDF
                pdf_options = {
                    'path': str(output_path),
                    'format': 'A4',
                    'margin': {
                        'top': '2cm',
                        'right': '2cm',
                        'bottom': '2cm',
                        'left': '2cm'
                    },
                    'print_background': True
                }
                
                # Gerar PDF
                page.pdf(**pdf_options)
                browser.close()
            
            logger.info(f"PDF gerado com Playwright: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Erro no Playwright: {e}")
            raise
    
    def _export_with_reportlab(self, html_content: str, output_path: Path) -> bool:
        """Exporta usando ReportLab (implementação básica)."""
        try:
            from reportlab.pdfgen import canvas
            from reportlab.lib.pagesizes import A4
            from reportlab.lib.styles import getSampleStyleSheet
            from reportlab.platypus import SimpleDocTemplate, Paragraph
            from bs4 import BeautifulSoup
            
            # Parse HTML básico
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Extrair texto principal
            title = soup.find('h1')
            title_text = title.get_text() if title else "Relatório"
            
            # Criar PDF
            doc = SimpleDocTemplate(str(output_path), pagesize=A4)
            styles = getSampleStyleSheet()
            story = []
            
            # Adicionar título
            story.append(Paragraph(title_text, styles['Title']))
            
            # Adicionar conteúdo (implementação básica)
            paragraphs = soup.find_all(['p', 'div'])
            for p in paragraphs[:20]:  # Limitar para evitar PDFs muito grandes
                text = p.get_text().strip()
                if text and len(text) > 10:
                    story.append(Paragraph(text, styles['Normal']))
            
            doc.build(story)
            
            logger.info(f"PDF gerado com ReportLab: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Erro no ReportLab: {e}")
            raise
    
    async def export_html_to_pdf_async(
        self, 
        html_content: str, 
        filename: Optional[str] = None,
        engine: str = 'auto'
    ) -> Dict[str, Any]:
        """Versão assíncrona da exportação."""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None, 
            self.export_html_to_pdf, 
            html_content, 
            filename, 
            engine
        )
    
    def get_export_info(self, filename: str) -> Optional[Dict[str, Any]]:
        """Obtém informações sobre um arquivo exportado."""
        file_path = self.export_dir / filename
        
        if not file_path.exists():
            return None
        
        stat = file_path.stat()
        
        return {
            'filename': filename,
            'filepath': str(file_path),
            'file_size': stat.st_size,
            'created_at': datetime.fromtimestamp(stat.st_ctime).isoformat(),
            'modified_at': datetime.fromtimestamp(stat.st_mtime).isoformat(),
            'download_url': f'/reports/download/{filename}'
        }
    
    def list_exported_files(self, limit: int = 20) -> List[Dict[str, Any]]:
        """Lista arquivos PDF exportados."""
        files = []
        
        for file_path in self.export_dir.glob("*.pdf"):
            info = self.get_export_info(file_path.name)
            if info:
                files.append(info)
        
        # Ordenar por data de criação (mais recentes primeiro)
        files.sort(key=lambda x: x['created_at'], reverse=True)
        
        return files[:limit]
    
    def cleanup_old_files(self, days_old: int = 7) -> int:
        """Remove arquivos PDF antigos."""
        cutoff_time = datetime.now().timestamp() - (days_old * 24 * 60 * 60)
        removed_count = 0
        
        for file_path in self.export_dir.glob("*.pdf"):
            if file_path.stat().st_ctime < cutoff_time:
                try:
                    file_path.unlink()
                    removed_count += 1
                    logger.info(f"Arquivo removido: {file_path.name}")
                except Exception as e:
                    logger.error(f"Erro ao remover {file_path.name}: {e}")
        
        return removed_count
    
    def install_dependencies(self) -> Dict[str, str]:
        """
        Retorna comandos para instalar dependências de PDF.
        
        Returns:
            Dicionário com comandos de instalação
        """
        return {
            'weasyprint': 'pip install weasyprint',
            'playwright': 'pip install playwright && playwright install chromium',
            'reportlab': 'pip install reportlab beautifulsoup4',
            'all': 'pip install weasyprint playwright reportlab beautifulsoup4 && playwright install chromium'
        }
