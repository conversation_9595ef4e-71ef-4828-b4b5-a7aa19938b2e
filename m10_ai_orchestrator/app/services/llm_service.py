"""
M10 AI Orchestrator - LLM Service

Serviço para integração com APIs de LLM (OpenAI, Anthropic, etc.)
"""

import os
import time
import tiktoken
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    logger.warning("OpenAI package not available. Install with: pip install openai")


@dataclass
class LLMResponse:
    """Estrutura para resposta do LLM."""
    content: str
    prompt_tokens: int
    completion_tokens: int
    total_tokens: int
    cost_usd: float
    model: str
    processing_time_ms: int
    finish_reason: str = "stop"


@dataclass
class LLMUsage:
    """Estrutura para rastreamento de uso."""
    prompt_tokens: int
    completion_tokens: int
    total_tokens: int
    cost_usd: float


class LLMService:
    """
    Serviço para integração com modelos de linguagem.
    
    Suporta OpenAI GPT models com rastreamento automático de tokens e custos.
    """
    
    # Preços por 1K tokens (atualizado em May 2024)
    PRICING = {
        "gpt-4o": {"input": 0.005, "output": 0.015},
        "gpt-4o-mini": {"input": 0.000150, "output": 0.000600},
        "gpt-4-turbo": {"input": 0.01, "output": 0.03},
        "gpt-4": {"input": 0.03, "output": 0.06},
        "gpt-3.5-turbo": {"input": 0.0015, "output": 0.002},
    }
    
    def __init__(self, api_key: Optional[str] = None, default_model: str = "gpt-4o-mini"):
        """
        Inicializa o serviço LLM.
        
        Args:
            api_key: Chave da API OpenAI (ou usa OPENAI_API_KEY do ambiente)
            default_model: Modelo padrão a ser usado
        """
        self.api_key = api_key or os.getenv("OPENAI_API_KEY")
        self.default_model = default_model
        
        if not OPENAI_AVAILABLE:
            raise ImportError("OpenAI package not installed. Run: pip install openai")
        
        if not self.api_key:
            raise ValueError("OpenAI API key not provided. Set OPENAI_API_KEY environment variable or pass api_key parameter.")
        
        # Configurar cliente OpenAI
        openai.api_key = self.api_key
        self.client = openai.OpenAI(api_key=self.api_key)
        
        logger.info(f"LLM Service initialized with model: {default_model}")
    
    def count_tokens(self, text: str, model: str = None) -> int:
        """
        Conta tokens em um texto para um modelo específico.
        
        Args:
            text: Texto para contar tokens
            model: Modelo (usa default se não especificado)
            
        Returns:
            int: Número de tokens
        """
        model = model or self.default_model
        
        try:
            # Mapear modelos para encoders
            encoder_map = {
                "gpt-4o": "o200k_base",
                "gpt-4o-mini": "o200k_base", 
                "gpt-4-turbo": "cl100k_base",
                "gpt-4": "cl100k_base",
                "gpt-3.5-turbo": "cl100k_base",
            }
            
            encoding_name = encoder_map.get(model, "cl100k_base")
            encoding = tiktoken.get_encoding(encoding_name)
            return len(encoding.encode(text))
            
        except Exception as e:
            logger.warning(f"Error counting tokens: {e}. Using approximation.")
            # Aproximação: ~4 chars = 1 token
            return len(text) // 4
    
    def calculate_cost(self, prompt_tokens: int, completion_tokens: int, model: str) -> float:
        """
        Calcula o custo baseado no uso de tokens.
        
        Args:
            prompt_tokens: Tokens do prompt
            completion_tokens: Tokens da resposta
            model: Modelo usado
            
        Returns:
            float: Custo em USD
        """
        if model not in self.PRICING:
            logger.warning(f"Pricing not available for model {model}. Using gpt-4o pricing.")
            model = "gpt-4o"
        
        pricing = self.PRICING[model]
        
        # Calcular custo (preços são por 1K tokens)
        prompt_cost = (prompt_tokens / 1000) * pricing["input"]
        completion_cost = (completion_tokens / 1000) * pricing["output"]
        
        return prompt_cost + completion_cost
    
    def generate_response(
        self,
        messages: List[Dict[str, str]],
        model: Optional[str] = None,
        temperature: float = 0.3,
        max_tokens: Optional[int] = None,
        system_prompt: Optional[str] = None
    ) -> LLMResponse:
        """
        Gera resposta usando modelo de linguagem.
        
        Args:
            messages: Lista de mensagens no formato [{"role": "user", "content": "texto"}]
            model: Modelo a usar (usa default se não especificado)
            temperature: Temperatura (0.0 a 1.0)
            max_tokens: Máximo de tokens na resposta
            system_prompt: Prompt do sistema (opcional)
            
        Returns:
            LLMResponse: Resposta com métricas
        """
        model = model or self.default_model
        start_time = time.time()
        
        # Preparar mensagens
        conversation = []
        
        if system_prompt:
            conversation.append({"role": "system", "content": system_prompt})
        
        conversation.extend(messages)
        
        # Contar tokens do prompt
        prompt_text = "\n".join([msg["content"] for msg in conversation])
        prompt_tokens = self.count_tokens(prompt_text, model)
        
        try:
            # Fazer chamada para API
            response = self.client.chat.completions.create(
                model=model,
                messages=conversation,
                temperature=temperature,
                max_tokens=max_tokens,
                stream=False
            )
            
            # Extrair dados da resposta
            content = response.choices[0].message.content
            finish_reason = response.choices[0].finish_reason
            
            # Métricas de uso
            if hasattr(response, 'usage') and response.usage:
                prompt_tokens_actual = response.usage.prompt_tokens
                completion_tokens = response.usage.completion_tokens
                total_tokens = response.usage.total_tokens
            else:
                # Fallback para contagem manual
                prompt_tokens_actual = prompt_tokens
                completion_tokens = self.count_tokens(content, model)
                total_tokens = prompt_tokens_actual + completion_tokens
            
            # Calcular custo e tempo
            cost_usd = self.calculate_cost(prompt_tokens_actual, completion_tokens, model)
            processing_time_ms = int((time.time() - start_time) * 1000)
            
            logger.info(f"LLM response generated: {total_tokens} tokens, ${cost_usd:.4f}, {processing_time_ms}ms")
            
            return LLMResponse(
                content=content,
                prompt_tokens=prompt_tokens_actual,
                completion_tokens=completion_tokens,
                total_tokens=total_tokens,
                cost_usd=cost_usd,
                model=model,
                processing_time_ms=processing_time_ms,
                finish_reason=finish_reason
            )
            
        except Exception as e:
            processing_time_ms = int((time.time() - start_time) * 1000)
            logger.error(f"Error generating LLM response: {e}")
            raise
    
    def simple_completion(
        self,
        prompt: str,
        model: Optional[str] = None,
        temperature: float = 0.3,
        max_tokens: Optional[int] = None,
        system_prompt: Optional[str] = None
    ) -> LLMResponse:
        """
        Método simplificado para completion simples.
        
        Args:
            prompt: Prompt do usuário
            model: Modelo a usar
            temperature: Temperatura
            max_tokens: Máximo de tokens
            system_prompt: Prompt do sistema
            
        Returns:
            LLMResponse: Resposta com métricas
        """
        messages = [{"role": "user", "content": prompt}]
        return self.generate_response(
            messages=messages,
            model=model,
            temperature=temperature,
            max_tokens=max_tokens,
            system_prompt=system_prompt
        )
    
    def classify_intent(self, user_message: str) -> Tuple[str, float]:
        """
        Classifica a intenção de uma mensagem do usuário.
        
        Args:
            user_message: Mensagem do usuário
            
        Returns:
            Tuple[str, float]: (intent, confidence_score)
        """
        system_prompt = """Você é um classificador de intenções. Analise a mensagem do usuário e classifique a intenção.

Intenções possíveis:
- pergunta_informativa: Perguntas sobre fatos, conceitos, explicações
- busca_especifica: Busca por informações específicas, documentos, dados
- conversa_casual: Conversas casuais, cumprimentos, interações sociais
- solicitacao_acao: Pedidos para executar ações, tarefas
- feedback: Comentários, sugestões, críticas
- suporte_tecnico: Problemas técnicos, dúvidas sobre funcionamento

Responda APENAS no formato: "intencao:confianca" (ex: "pergunta_informativa:0.95")"""
        
        try:
            response = self.simple_completion(
                prompt=user_message,
                system_prompt=system_prompt,
                temperature=0.1,
                max_tokens=50
            )
            
            # Extrair intenção e confiança
            result = response.content.strip()
            if ":" in result:
                intent, confidence_str = result.split(":", 1)
                confidence = float(confidence_str)
                return intent.strip(), confidence
            else:
                return "unknown", 0.5
                
        except Exception as e:
            logger.error(f"Error classifying intent: {e}")
            return "unknown", 0.0
    
    def get_model_info(self) -> Dict[str, any]:
        """
        Retorna informações sobre o modelo e configuração.
        
        Returns:
            dict: Informações do serviço
        """
        return {
            "default_model": self.default_model,
            "available": OPENAI_AVAILABLE,
            "api_configured": bool(self.api_key),
            "supported_models": list(self.PRICING.keys()),
            "pricing": self.PRICING
        }
    
    def health_check(self) -> bool:
        """
        Verifica se o serviço está funcionando.
        
        Returns:
            bool: True se funcional
        """
        try:
            response = self.simple_completion(
                prompt="Hello! Just testing the connection.",
                max_tokens=10
            )
            return bool(response.content)
            
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return False
