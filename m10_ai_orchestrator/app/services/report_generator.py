"""
M10 AI Orchestrator - Report Generator

Gerador de relatórios formatados em HTML/Markdown para análises de dados.
Suporta diferentes tipos de relatório com templates personalizáveis.
"""

from typing import Dict, List, Any, Optional
from datetime import datetime
import uuid
import json
import logging
from dataclasses import asdict
from .data_analysis_service import ProductAnalysis, ComparisonResult, DocumentAnalysis

logger = logging.getLogger(__name__)


class ReportGenerator:
    """
    Gerador de relatórios para análises de dados.
    
    Cria relatórios formatados em HTML com:
    - Templates responsivos
    - Gráficos e estatísticas
    - Exportação para PDF
    - Design profissional
    """
    
    def __init__(self):
        self.report_storage = {}  # Em produção, usar banco de dados
        
    def generate_product_analysis_report(
        self, 
        analysis: ProductAnalysis, 
        include_details: bool = True
    ) -> Dict[str, Any]:
        """
        Gera relatório de análise de produto.
        
        Args:
            analysis: Resultado da análise do produto
            include_details: Se deve incluir detalhes dos documentos
            
        Returns:
            Dicionário com dados do relatório
        """
        try:
            report_id = str(uuid.uuid4())
            
            # Preparar dados para o template
            report_data = {
                'id': report_id,
                'type': 'product_analysis',
                'title': f'Análise do Produto: {analysis.product_name}',
                'generated_at': datetime.now().isoformat(),
                'product_name': analysis.product_name,
                'summary': {
                    'total_documents': analysis.total_documents,
                    'total_versions': analysis.total_versions,
                    'languages': analysis.languages,
                    'date_range': {
                        'start': analysis.date_range[0].isoformat(),
                        'end': analysis.date_range[1].isoformat()
                    }
                },
                'documents': []
            }
            
            # Adicionar detalhes dos documentos se solicitado
            if include_details:
                for doc in analysis.documents:
                    doc_data = {
                        'document_id': doc.document_id,
                        'name': doc.original_name,
                        'version': doc.version_tag,
                        'chunk_count': doc.chunk_count,
                        'word_count': doc.word_count,
                        'language': doc.language_detected,
                        'created_at': doc.created_at.isoformat(),
                        'content_sample': doc.content_sample[:300] + '...' if len(doc.content_sample) > 300 else doc.content_sample,
                        'metadata': doc.metadata
                    }
                    report_data['documents'].append(doc_data)
            
            # Gerar HTML
            html_content = self._generate_product_analysis_html(report_data)
            
            # Armazenar relatório
            report_info = {
                'id': report_id,
                'type': 'product_analysis',
                'title': report_data['title'],
                'data': report_data,
                'html': html_content,
                'created_at': datetime.now(),
                'url': f'/reports/{report_id}'
            }
            
            self.report_storage[report_id] = report_info
            
            logger.info(f"Relatório de produto gerado: {report_id}")
            return report_info
            
        except Exception as e:
            logger.error(f"Erro ao gerar relatório de produto: {e}")
            raise
    
    def generate_comparison_report(
        self, 
        comparison: ComparisonResult
    ) -> Dict[str, Any]:
        """
        Gera relatório de comparação entre versões.
        
        Args:
            comparison: Resultado da comparação
            
        Returns:
            Dicionário com dados do relatório
        """
        try:
            report_id = str(uuid.uuid4())
            
            # Preparar dados para o template
            report_data = {
                'id': report_id,
                'type': 'version_comparison',
                'title': f'Comparação: {comparison.product_name} ({comparison.version_a} vs {comparison.version_b})',
                'generated_at': datetime.now().isoformat(),
                'product_name': comparison.product_name,
                'version_a': comparison.version_a,
                'version_b': comparison.version_b,
                'similarity_score': comparison.similarity_score,
                'differences_count': comparison.differences_count,
                'summary': comparison.summary,
                'changes': {
                    'added': comparison.added_content[:20],  # Limitar para exibição
                    'removed': comparison.removed_content[:20],
                    'modified': comparison.modified_content[:20]
                }
            }
            
            # Gerar HTML
            html_content = self._generate_comparison_html(report_data)
            
            # Armazenar relatório
            report_info = {
                'id': report_id,
                'type': 'version_comparison',
                'title': report_data['title'],
                'data': report_data,
                'html': html_content,
                'created_at': datetime.now(),
                'url': f'/reports/{report_id}'
            }
            
            self.report_storage[report_id] = report_info
            
            logger.info(f"Relatório de comparação gerado: {report_id}")
            return report_info
            
        except Exception as e:
            logger.error(f"Erro ao gerar relatório de comparação: {e}")
            raise
    
    def get_report(self, report_id: str) -> Optional[Dict[str, Any]]:
        """Recupera um relatório pelo ID."""
        return self.report_storage.get(report_id)
    
    def list_reports(self, limit: int = 20) -> List[Dict[str, Any]]:
        """Lista relatórios recentes."""
        reports = list(self.report_storage.values())
        reports.sort(key=lambda x: x['created_at'], reverse=True)
        return reports[:limit]
    
    def _generate_product_analysis_html(self, data: Dict[str, Any]) -> str:
        """Gera HTML para relatório de análise de produto."""
        
        # Estatísticas resumidas
        summary = data['summary']
        documents_table = ""
        
        if data['documents']:
            documents_table = "<h3>📄 Documentos Encontrados</h3>\n<div class='documents-grid'>\n"
            for doc in data['documents']:
                documents_table += f"""
                <div class='document-card'>
                    <h4>{doc['name']}</h4>
                    <div class='doc-meta'>
                        <span class='version'>Versão: {doc['version']}</span>
                        <span class='language'>Idioma: {doc['language']}</span>
                        <span class='chunks'>Chunks: {doc['chunk_count']}</span>
                        <span class='words'>Palavras: {doc['word_count']}</span>
                    </div>
                    <div class='content-sample'>
                        <strong>Amostra do conteúdo:</strong>
                        <p>{doc['content_sample']}</p>
                    </div>
                    <div class='doc-date'>Criado em: {doc['created_at'][:10]}</div>
                </div>
                """
            documents_table += "</div>\n"
        
        html_template = f"""
        <!DOCTYPE html>
        <html lang="pt-BR">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{data['title']}</title>
            <style>
                body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }}
                .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
                .header {{ text-align: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #3498db; }}
                .header h1 {{ color: #2c3e50; margin: 0; }}
                .header .meta {{ color: #7f8c8d; margin-top: 10px; }}
                .summary {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }}
                .stat-card {{ background: linear-gradient(135deg, #3498db, #2980b9); color: white; padding: 20px; border-radius: 8px; text-align: center; }}
                .stat-card h3 {{ margin: 0 0 10px 0; font-size: 2em; }}
                .stat-card p {{ margin: 0; opacity: 0.9; }}
                .documents-grid {{ display: grid; grid-template-columns: repeat(auto-fill, minmax(400px, 1fr)); gap: 20px; }}
                .document-card {{ border: 1px solid #ddd; border-radius: 8px; padding: 20px; background: #fafafa; }}
                .document-card h4 {{ color: #2c3e50; margin: 0 0 15px 0; }}
                .doc-meta {{ display: flex; flex-wrap: wrap; gap: 10px; margin-bottom: 15px; }}
                .doc-meta span {{ background: #ecf0f1; padding: 4px 8px; border-radius: 4px; font-size: 0.9em; }}
                .content-sample {{ margin: 15px 0; }}
                .content-sample p {{ background: white; padding: 10px; border-left: 4px solid #3498db; margin: 5px 0; font-style: italic; }}
                .doc-date {{ color: #7f8c8d; font-size: 0.9em; }}
                .export-btn {{ position: fixed; bottom: 20px; right: 20px; background: #e74c3c; color: white; padding: 15px 20px; border: none; border-radius: 50px; cursor: pointer; font-size: 16px; box-shadow: 0 4px 15px rgba(231,76,60,0.3); }}
                .export-btn:hover {{ background: #c0392b; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>📊 {data['title']}</h1>
                    <div class="meta">Gerado em: {data['generated_at'][:19].replace('T', ' ')}</div>
                </div>
                
                <div class="summary">
                    <div class="stat-card">
                        <h3>{summary['total_documents']}</h3>
                        <p>Documentos</p>
                    </div>
                    <div class="stat-card">
                        <h3>{summary['total_versions']}</h3>
                        <p>Versões</p>
                    </div>
                    <div class="stat-card">
                        <h3>{len(summary['languages'])}</h3>
                        <p>Idiomas</p>
                    </div>
                    <div class="stat-card">
                        <h3>{(datetime.fromisoformat(summary['date_range']['end']) - datetime.fromisoformat(summary['date_range']['start'])).days}</h3>
                        <p>Dias de Cobertura</p>
                    </div>
                </div>
                
                {documents_table}
            </div>
            
            <button class="export-btn" onclick="window.print()">📄 Exportar PDF</button>
        </body>
        </html>
        """
        
        return html_template
    
    def _generate_comparison_html(self, data: Dict[str, Any]) -> str:
        """Gera HTML para relatório de comparação."""
        
        similarity_percent = data['similarity_score'] * 100
        similarity_color = "#27ae60" if similarity_percent > 70 else "#f39c12" if similarity_percent > 40 else "#e74c3c"
        
        changes = data['changes']
        
        html_template = f"""
        <!DOCTYPE html>
        <html lang="pt-BR">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{data['title']}</title>
            <style>
                body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }}
                .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
                .header {{ text-align: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #3498db; }}
                .header h1 {{ color: #2c3e50; margin: 0; }}
                .versions {{ display: flex; justify-content: center; gap: 30px; margin: 20px 0; }}
                .version {{ background: #ecf0f1; padding: 15px 25px; border-radius: 8px; font-weight: bold; }}
                .similarity {{ text-align: center; margin: 30px 0; }}
                .similarity-score {{ font-size: 3em; font-weight: bold; color: {similarity_color}; }}
                .changes-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 30px 0; }}
                .change-section {{ border: 1px solid #ddd; border-radius: 8px; padding: 20px; }}
                .change-section.added {{ border-left: 4px solid #27ae60; }}
                .change-section.removed {{ border-left: 4px solid #e74c3c; }}
                .change-section.modified {{ border-left: 4px solid #f39c12; }}
                .change-list {{ list-style: none; padding: 0; }}
                .change-list li {{ background: #f8f9fa; margin: 5px 0; padding: 8px; border-radius: 4px; }}
                .summary-box {{ background: #ecf0f1; padding: 20px; border-radius: 8px; margin: 20px 0; }}
                .export-btn {{ position: fixed; bottom: 20px; right: 20px; background: #e74c3c; color: white; padding: 15px 20px; border: none; border-radius: 50px; cursor: pointer; font-size: 16px; box-shadow: 0 4px 15px rgba(231,76,60,0.3); }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🔄 {data['title']}</h1>
                    <div class="versions">
                        <div class="version">📋 {data['version_a']}</div>
                        <div style="align-self: center;">vs</div>
                        <div class="version">📋 {data['version_b']}</div>
                    </div>
                </div>
                
                <div class="similarity">
                    <div>Similaridade</div>
                    <div class="similarity-score">{similarity_percent:.1f}%</div>
                </div>
                
                <div class="summary-box">
                    <h3>📝 Resumo da Análise</h3>
                    <p>{data['summary']}</p>
                    <p><strong>Total de diferenças encontradas:</strong> {data['differences_count']}</p>
                </div>
                
                <div class="changes-grid">
                    <div class="change-section added">
                        <h3>➕ Conteúdo Adicionado ({len(changes['added'])} itens)</h3>
                        <ul class="change-list">
                            {''.join(f'<li>{item}</li>' for item in changes['added'][:10])}
                        </ul>
                    </div>
                    
                    <div class="change-section removed">
                        <h3>➖ Conteúdo Removido ({len(changes['removed'])} itens)</h3>
                        <ul class="change-list">
                            {''.join(f'<li>{item}</li>' for item in changes['removed'][:10])}
                        </ul>
                    </div>
                    
                    <div class="change-section modified">
                        <h3>🔄 Conteúdo Modificado ({len(changes['modified'])} itens)</h3>
                        <ul class="change-list">
                            {''.join(f'<li>{item}</li>' for item in changes['modified'][:10])}
                        </ul>
                    </div>
                </div>
            </div>
            
            <button class="export-btn" onclick="window.print()">📄 Exportar PDF</button>
        </body>
        </html>
        """
        
        return html_template
