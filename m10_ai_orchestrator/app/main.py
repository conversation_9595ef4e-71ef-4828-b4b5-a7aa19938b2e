"""
M10 AI Orchestrator - Aplicação Principal FastAPI

Ponto de entrada da aplicação que configura o FastAPI, middlewares,
roteamento e inicialização de serviços.
"""

import logging
import structlog
import os
from pathlib import Path
from contextlib import asynccontextmanager
from typing import Dict, Any
from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse, HTMLResponse
from fastapi.staticfiles import StaticFiles # Importar StaticFiles
from pathlib import Path # Importar Path
from prometheus_client import Counter, Histogram, generate_latest, CONTENT_TYPE_LATEST
import time
import asyncio # Adicionado import

from .config import Settings, get_settings
from . import __version__, __description__
from .routers import monitor, chat, reports
from .integrations.health_service import check_postgresql_health, check_redis_health, check_openai_health, check_m2_health, check_m4_health # Adicionado import
from .database import init_database

# Carregar variáveis de ambiente do .env
try:
    from dotenv import load_dotenv
    env_path = Path(__file__).parent.parent / ".env"
    if env_path.exists():
        load_dotenv(env_path)
        print(f"✅ Arquivo .env carregado de: {env_path}")
    else:
        print(f"⚠️ Arquivo .env não encontrado em: {env_path}")
except ImportError:
    print("⚠️ python-dotenv não instalado, usando apenas variáveis de ambiente do sistema")

# Configuração de logging estruturado
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)

# Métricas Prometheus
try:
    REQUEST_COUNT = Counter('m10_requests_total', 'Total de requisições', ['method', 'endpoint', 'status'])
    REQUEST_DURATION = Histogram('m10_request_duration_seconds', 'Duração das requisições', ['method', 'endpoint'])
    QUERY_COUNT = Counter('m10_queries_total', 'Total de consultas RAG', ['intent', 'status'])
    TOKEN_USAGE = Counter('m10_tokens_used_total', 'Total de tokens OpenAI utilizados', ['model', 'type'])
except ValueError as e:
    if "Duplicated timeseries" in str(e):
        logger.warning("Métricas Prometheus já registradas, criando novas instâncias")
        # Criar novas instâncias com nomes únicos
        REQUEST_COUNT = Counter('m10_requests_total_v2', 'Total de requisições', ['method', 'endpoint', 'status'])
        REQUEST_DURATION = Histogram('m10_request_duration_seconds_v2', 'Duração das requisições', ['method', 'endpoint'])
        QUERY_COUNT = Counter('m10_queries_total_v2', 'Total de consultas RAG', ['intent', 'status'])
        TOKEN_USAGE = Counter('m10_tokens_used_total_v2', 'Total de tokens OpenAI utilizados', ['model', 'type'])
    else:
        raise


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Gerenciamento do ciclo de vida da aplicação"""
    # Startup
    logger.info("🚀 Iniciando M10 AI Orchestrator", version=__version__)

    # Inicializar banco de dados (criar tabelas se não existirem)
    try:
        init_database()
        logger.info("✅ Banco de dados inicializado com sucesso")
    except Exception as e:
        logger.error("❌ Erro ao inicializar banco de dados", error=str(e))

    # Inicializar tarefas de background para monitoramento
    # A função startup_event em monitor.py já é chamada via @router.on_event("startup")
    # Não é necessário chamar start_background_tasks() explicitamente aqui.

    # TODO: Inicializar cache Redis
    # TODO: Carregar modelos de intent classification
    # TODO: Verificar conectividade com serviços M5, M6, M7
    
    yield
    
    # Shutdown
    logger.info("🛑 Finalizando M10 AI Orchestrator")
    
    # TODO: Fechar conexões de banco de dados
    # TODO: Fechar conexões Redis
    # TODO: Cleanup de recursos


def create_app(settings: Settings = None) -> FastAPI:
    """Factory function para criar a aplicação FastAPI"""
    
    if settings is None:
        settings = get_settings()
    
    # Configurar logging level
    logging.basicConfig(level=getattr(logging, settings.log_level.upper()))
    
    app = FastAPI(
        title="M10 AI Orchestrator",
        description=__description__,
        version=__version__,
        docs_url="/docs",
        redoc_url="/redoc",
        openapi_url="/openapi.json",
        lifespan=lifespan
    )
    
    # Middleware CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # TODO: Configurar origins específicos em produção
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Middleware de hosts confiáveis
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["*"]  # TODO: Configurar hosts específicos em produção
    )
    
    # Middleware para métricas
    @app.middleware("http")
    async def metrics_middleware(request, call_next):
        start_time = time.time()
        method = request.method
        endpoint = request.url.path
        
        response = await call_next(request)
        
        duration = time.time() - start_time
        status = response.status_code
        
        REQUEST_COUNT.labels(method=method, endpoint=endpoint, status=status).inc()
        REQUEST_DURATION.labels(method=method, endpoint=endpoint).observe(duration)
        
        return response
    
    # Middleware para logging de auditoria
    @app.middleware("http")
    async def audit_middleware(request, call_next):
        if settings.enable_audit_logging:
            logger.info(
                "HTTP Request",
                method=request.method,
                url=str(request.url),
                client_ip=request.client.host if request.client else None,
                user_agent=request.headers.get("user-agent")
            )
        
        response = await call_next(request)
        
        if settings.enable_audit_logging:
            logger.info(
                "HTTP Response",
                status_code=response.status_code,
                method=request.method,
                url=str(request.url)
            )
        
        return response
    
    # Incluir routers
    app.include_router(monitor.router, tags=["monitoring"])
    app.include_router(chat.router, prefix="/api/v1", tags=["chat"])
    app.include_router(reports.router, prefix="/api/v1", tags=["reports"])
    # TODO: Adicionar routers quando implementados
    # app.include_router(query_router, prefix="/api/v1/query", tags=["query"])

    # Rota para a página principal
    @app.get("/", response_class=HTMLResponse)
    async def read_root():
        """Serve a página principal da interface web"""
        html_file = Path(__file__).parent / "monitoring" / "index.html"
        if html_file.exists():
            return HTMLResponse(content=html_file.read_text(encoding='utf-8'))
        else:
            raise HTTPException(status_code=404, detail="Interface web não encontrada")

    # Montar arquivos estáticos para o monitor
    # Detectar automaticamente se está rodando em container ou localmente
    container_static_path = Path("/app/app/monitoring/static")
    local_static_path = Path(__file__).parent / "monitoring" / "static"
    
    STATIC_FILES_DIR = None
    if container_static_path.exists():
        STATIC_FILES_DIR = container_static_path
        logger.info(f"Usando diretório estático do container: {STATIC_FILES_DIR}")
    elif local_static_path.exists():
        STATIC_FILES_DIR = local_static_path
        logger.info(f"Usando diretório estático local: {STATIC_FILES_DIR}")
    
    if STATIC_FILES_DIR and STATIC_FILES_DIR.exists():
        logger.info(f"Montando diretório de arquivos estáticos do monitor em {STATIC_FILES_DIR} sob /monitor/static")
        app.mount("/monitor/static", StaticFiles(directory=str(STATIC_FILES_DIR)), name="monitor-static-files")
        # Também montar na raiz para compatibilidade com a página principal
        app.mount("/static", StaticFiles(directory=str(STATIC_FILES_DIR)), name="static-files")
    else:
        logger.warning("Diretório de arquivos estáticos do monitor NÃO ENCONTRADO. Monitor visual pode não funcionar corretamente.")
    
    return app


# Instância da aplicação
app = create_app()


@app.get("/health", tags=["health"])
async def health_check(settings: Settings = Depends(get_settings)) -> Dict[str, Any]:
    """Verificação de saúde do serviço M10 AI Orchestrator"""
    
    health_status = {
        "status": "healthy",
        "service": "m10_ai_orchestrator",
        "version": __version__,
        "timestamp": time.time(),
        "dependencies": {}
    }
    
    # TODO: Verificar saúde das dependências
    # - PostgreSQL connection
    # - Redis connection  
    # - OpenAI API availability
    # - M5, M6, M7 services connectivity
    
    try:
        # Verificar saúde das dependências em paralelo
        db_health, redis_health, openai_api_health, m2_health, m4_health = await asyncio.gather(
            check_postgresql_health(),
            check_redis_health(),
            check_openai_health(),
            check_m2_health(),
            check_m4_health()
        )
        
        health_status["dependencies"] = {
            "database": db_health,
            "redis": redis_health,
            "openai_api": openai_api_health,
            "m2_orchestrator": m2_health,
            "m4_translator": m4_health, # Usar a chave que o frontend espera
            "m5_service": "checking", 
            "m6_service": "checking",
            "m7_service": "checking"
        }
        
        # Determinar o status geral
        all_deps_status = [
            db_health["status"], 
            redis_health["status"], 
            openai_api_health["status"],
            m2_health["status"],
            m4_health["status"]
        ]

        if "unhealthy" in all_deps_status:
            health_status["status"] = "unhealthy"
        elif "degraded" in all_deps_status:
            health_status["status"] = "degraded"
        # else: mantém "healthy" (default)
        
        logger.info("Health check executado", 
                    status=health_status["status"], 
                    db_status=db_health["status"],
                    redis_status=redis_health["status"],
                    openai_status=openai_api_health["status"],
                    m2_status=m2_health["status"],
                    m4_status=m4_health["status"])
        return health_status
        
    except Exception as e:
        logger.error("Falha no health check", error=str(e))
        health_status["status"] = "unhealthy"
        health_status["error"] = str(e)
        
        return JSONResponse(
            status_code=503,
            content=health_status
        )


@app.get("/ready", tags=["health"])
async def readiness_check() -> Dict[str, Any]:
    """Verificação de prontidão do serviço"""
    
    # TODO: Verificar se todos os serviços necessários estão prontos
    # - Modelos carregados
    # - Conexões estabelecidas
    # - Cache inicializado
    
    return {
        "status": "ready",
        "service": "m10_ai_orchestrator",
        "timestamp": time.time()
    }


@app.get("/metrics", tags=["monitoring"])
async def get_metrics():
    """Endpoint para métricas Prometheus"""
    return generate_latest()


@app.get("/config", tags=["admin"])
async def get_config(settings: Settings = Depends(get_settings)) -> Dict[str, Any]:
    """Retorna configurações atuais (sem dados sensíveis)"""
    
    config = {
        "service": "m10_ai_orchestrator",
        "version": __version__,
        "debug": settings.debug,
        "openai_model": settings.openai_model,
        "max_search_results": settings.max_search_results,
        "similarity_threshold": settings.similarity_threshold,
        "context_max_tokens": settings.context_max_tokens,
        "cache_ttl_seconds": settings.cache_ttl_seconds,
        "enable_context_cache": settings.enable_context_cache,
        "max_concurrent_queries": settings.max_concurrent_queries,
        "query_timeout_seconds": settings.query_timeout_seconds,
        "log_level": settings.log_level,
        "enable_audit_logging": settings.enable_audit_logging
    }
    
    return config


@app.exception_handler(404)
async def not_found_handler(request, exc):
    """Handler customizado para 404"""
    return JSONResponse(
        status_code=404,
        content={
            "error": "Not Found",
            "message": f"Endpoint {request.url.path} não encontrado",
            "service": "m10_ai_orchestrator"
        }
    )


@app.exception_handler(500)
async def internal_error_handler(request, exc):
    """Handler customizado para erros internos"""
    logger.error("Erro interno do servidor", error=str(exc), path=request.url.path)
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal Server Error",
            "message": "Erro interno do servidor",
            "service": "m10_ai_orchestrator"
        }
    )


if __name__ == "__main__":
    import uvicorn
    
    settings = get_settings()
    uvicorn.run(
        "app.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )
