#!/usr/bin/env python3
"""
Teste do Sistema de Análise de Dados e Relatórios

Script para testar as funcionalidades implementadas:
- NLQueryProcessor
- DataAnalysisService  
- ReportGenerator
- PDFExporter
"""

import sys
import os
from pathlib import Path

# Adicionar o diretório do app ao path
sys.path.insert(0, str(Path(__file__).parent / "app"))

from app.services.nl_query_processor import NLQueryProcessor, QueryType
from app.services.report_generator import ReportGenerator
from app.services.pdf_exporter import PDFExporter


def test_nl_query_processor():
    """Testa o processador de linguagem natural."""
    print("🧠 Testando NL Query Processor...")
    
    processor = NLQueryProcessor()
    
    # Testes de consultas
    test_queries = [
        "Analise todos os materiais do produto ABC",
        "Compare a versão v1.0 com a v2.0",
        "Buscar documentos sobre qualidade",
        "Quantos documentos temos na base?",
        "Como está o tempo hoje?"  # Chat geral
    ]
    
    for query in test_queries:
        print(f"\n📝 Query: '{query}'")
        intent = processor.process_query(query)
        print(f"   Tipo: {intent.query_type.value}")
        print(f"   Confiança: {intent.confidence:.2f}")
        print(f"   Entidades: {intent.entities}")
        print(f"   Parâmetros: {intent.parameters}")
    
    print("✅ NL Query Processor testado com sucesso!")


def test_report_generator():
    """Testa o gerador de relatórios."""
    print("\n📊 Testando Report Generator...")
    
    generator = ReportGenerator()
    
    # Criar dados de teste para análise de produto
    from app.services.data_analysis_service import ProductAnalysis, DocumentAnalysis
    from datetime import datetime
    
    # Documentos de exemplo
    docs = [
        DocumentAnalysis(
            document_id="doc1",
            original_name="Manual_Produto_ABC_v1.0.pdf",
            version_tag="v1.0",
            chunk_count=50,
            word_count=2500,
            language_detected="pt",
            created_at=datetime(2024, 1, 15),
            metadata={"tipo": "manual"},
            content_sample="Este é o manual do produto ABC versão 1.0..."
        ),
        DocumentAnalysis(
            document_id="doc2", 
            original_name="Manual_Produto_ABC_v2.0.pdf",
            version_tag="v2.0",
            chunk_count=65,
            word_count=3200,
            language_detected="pt",
            created_at=datetime(2024, 6, 20),
            metadata={"tipo": "manual"},
            content_sample="Este é o manual atualizado do produto ABC versão 2.0..."
        )
    ]
    
    # Análise de exemplo
    analysis = ProductAnalysis(
        product_name="ABC",
        total_documents=2,
        total_versions=2,
        languages=["pt"],
        date_range=(datetime(2024, 1, 15), datetime(2024, 6, 20)),
        documents=docs
    )
    
    # Gerar relatório
    report_info = generator.generate_product_analysis_report(analysis, True)
    
    print(f"✅ Relatório gerado: {report_info['id']}")
    print(f"   Título: {report_info['title']}")
    print(f"   URL: {report_info['url']}")
    print(f"   Tamanho HTML: {len(report_info['html'])} caracteres")
    
    # Salvar HTML para visualização
    html_file = Path("test_report.html")
    with open(html_file, "w", encoding="utf-8") as f:
        f.write(report_info['html'])
    
    print(f"📄 HTML salvo em: {html_file.absolute()}")
    
    return report_info


def test_pdf_exporter(report_info):
    """Testa o exportador de PDF."""
    print("\n📄 Testando PDF Exporter...")
    
    exporter = PDFExporter()
    
    # Verificar engines disponíveis
    print(f"Engines disponíveis: {exporter.available_engines}")
    
    # Tentar exportar para PDF
    try:
        result = exporter.export_html_to_pdf(
            html_content=report_info['html'],
            filename="test_report.pdf"
        )
        
        if result['success']:
            print(f"✅ PDF gerado com sucesso!")
            print(f"   Arquivo: {result['filename']}")
            print(f"   Tamanho: {result['file_size']} bytes")
            print(f"   Engine usado: {result['engine_used']}")
            print(f"   Caminho: {result['filepath']}")
        else:
            print(f"❌ Erro na geração do PDF: {result['error']}")
            
    except Exception as e:
        print(f"❌ Erro ao testar PDF: {e}")
        print("💡 Instale as dependências: pip install weasyprint playwright reportlab beautifulsoup4")


def test_integration():
    """Teste de integração completo."""
    print("\n🔄 Teste de Integração Completo...")
    
    # 1. Processar consulta
    processor = NLQueryProcessor()
    query = "Analise todos os materiais do produto XYZ"
    intent = processor.process_query(query)
    
    print(f"1. Query processada: {intent.query_type.value}")
    
    # 2. Simular resposta do chat
    if intent.query_type == QueryType.PRODUCT_ANALYSIS:
        product_name = intent.entities.get('product_name', 'XYZ')
        
        response = f"""✅ **Análise do Produto: {product_name}**

📊 **Resumo:**
- **5** documentos encontrados
- **3** versões diferentes  
- **2** idiomas detectados
- Período: 01/01/2024 a 20/06/2024

🔗 **Relatório Completo:** [Visualizar Relatório](/reports/abc123)

📄 **Exportar:** [Download PDF](/reports/abc123/export-pdf)

O relatório contém análise detalhada de todos os materiais relacionados ao produto."""
        
        print(f"2. Resposta gerada:")
        print(response)
        
        print("✅ Integração testada com sucesso!")


def main():
    """Função principal de teste."""
    print("🚀 Iniciando testes do Sistema de Análise de Dados...")
    print("=" * 60)
    
    try:
        # Teste 1: NL Query Processor
        test_nl_query_processor()
        
        # Teste 2: Report Generator
        report_info = test_report_generator()
        
        # Teste 3: PDF Exporter
        test_pdf_exporter(report_info)
        
        # Teste 4: Integração
        test_integration()
        
        print("\n" + "=" * 60)
        print("🎉 Todos os testes concluídos!")
        print("\n📋 Próximos passos:")
        print("1. Instalar dependências: pip install -r requirements.txt")
        print("2. Configurar banco de dados PostgreSQL")
        print("3. Testar endpoints via API: /api/v1/chat e /api/v1/reports")
        print("4. Verificar interface web em http://localhost:8010/monitor/#chat")
        
    except Exception as e:
        print(f"\n❌ Erro durante os testes: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
